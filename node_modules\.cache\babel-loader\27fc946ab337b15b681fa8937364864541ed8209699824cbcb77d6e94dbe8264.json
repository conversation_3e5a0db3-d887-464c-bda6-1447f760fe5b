{"ast": null, "code": "/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const setextUnderline = {\n  name: 'setextUnderline',\n  resolveTo: resolveToSetextUnderline,\n  tokenize: tokenizeSetextUnderline\n};\n\n/** @type {Resolver} */\nfunction resolveToSetextUnderline(events, context) {\n  // To do: resolve like `markdown-rs`.\n  let index = events.length;\n  /** @type {number | undefined} */\n  let content;\n  /** @type {number | undefined} */\n  let text;\n  /** @type {number | undefined} */\n  let definition;\n\n  // Find the opening of the content.\n  // It’ll always exist: we don’t tokenize if it isn’t there.\n  while (index--) {\n    if (events[index][0] === 'enter') {\n      if (events[index][1].type === types.content) {\n        content = index;\n        break;\n      }\n      if (events[index][1].type === types.paragraph) {\n        text = index;\n      }\n    }\n    // Exit\n    else {\n      if (events[index][1].type === types.content) {\n        // Remove the content end (if needed we’ll add it later)\n        events.splice(index, 1);\n      }\n      if (!definition && events[index][1].type === types.definition) {\n        definition = index;\n      }\n    }\n  }\n  assert(text !== undefined, 'expected a `text` index to be found');\n  assert(content !== undefined, 'expected a `text` index to be found');\n  assert(events[content][2] === context, 'enter context should be same');\n  assert(events[events.length - 1][2] === context, 'enter context should be same');\n  const heading = {\n    type: types.setextHeading,\n    start: {\n      ...events[content][1].start\n    },\n    end: {\n      ...events[events.length - 1][1].end\n    }\n  };\n\n  // Change the paragraph to setext heading text.\n  events[text][1].type = types.setextHeadingText;\n\n  // If we have definitions in the content, we’ll keep on having content,\n  // but we need move it.\n  if (definition) {\n    events.splice(text, 0, ['enter', heading, context]);\n    events.splice(definition + 1, 0, ['exit', events[content][1], context]);\n    events[content][1].end = {\n      ...events[definition][1].end\n    };\n  } else {\n    events[content][1] = heading;\n  }\n\n  // Add the heading exit at the end.\n  events.push(['exit', heading, context]);\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeSetextUnderline(effects, ok, nok) {\n  const self = this;\n  /** @type {NonNullable<Code>} */\n  let marker;\n  return start;\n\n  /**\n   * At start of heading (setext) underline.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length;\n    /** @type {boolean | undefined} */\n    let paragraph;\n    assert(code === codes.dash || code === codes.equalsTo, 'expected `=` or `-`');\n\n    // Find an opening.\n    while (index--) {\n      // Skip enter/exit of line ending, line prefix, and content.\n      // We can now either have a definition or a paragraph.\n      if (self.events[index][1].type !== types.lineEnding && self.events[index][1].type !== types.linePrefix && self.events[index][1].type !== types.content) {\n        paragraph = self.events[index][1].type === types.paragraph;\n        break;\n      }\n    }\n\n    // To do: handle lazy/pierce like `markdown-rs`.\n    // To do: parse indent like `markdown-rs`.\n    if (!self.parser.lazy[self.now().line] && (self.interrupt || paragraph)) {\n      effects.enter(types.setextHeadingLine);\n      marker = code;\n      return before(code);\n    }\n    return nok(code);\n  }\n\n  /**\n   * After optional whitespace, at `-` or `=`.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    effects.enter(types.setextHeadingLineSequence);\n    return inside(code);\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code);\n      return inside;\n    }\n    effects.exit(types.setextHeadingLineSequence);\n    return markdownSpace(code) ? factorySpace(effects, after, types.lineSuffix)(code) : after(code);\n  }\n\n  /**\n   * After sequence, after optional whitespace.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.setextHeadingLine);\n      return ok(code);\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factorySpace", "markdownLineEnding", "markdownSpace", "codes", "types", "setextUnderline", "name", "resolveTo", "resolveToSetextUnderline", "tokenize", "tokenizeSetextUnderline", "events", "context", "index", "length", "content", "text", "definition", "type", "paragraph", "splice", "undefined", "heading", "setextHeading", "start", "end", "setextHeadingText", "push", "effects", "nok", "self", "marker", "code", "dash", "equalsTo", "lineEnding", "linePrefix", "parser", "lazy", "now", "line", "interrupt", "enter", "setextHeadingLine", "before", "setextHeadingLineSequence", "inside", "consume", "exit", "after", "lineSuffix", "eof"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark-core-commonmark/dev/lib/setext-underline.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const setextUnderline = {\n  name: 'setextUnderline',\n  resolveTo: resolveToSetextUnderline,\n  tokenize: tokenizeSetextUnderline\n}\n\n/** @type {Resolver} */\nfunction resolveToSetextUnderline(events, context) {\n  // To do: resolve like `markdown-rs`.\n  let index = events.length\n  /** @type {number | undefined} */\n  let content\n  /** @type {number | undefined} */\n  let text\n  /** @type {number | undefined} */\n  let definition\n\n  // Find the opening of the content.\n  // It’ll always exist: we don’t tokenize if it isn’t there.\n  while (index--) {\n    if (events[index][0] === 'enter') {\n      if (events[index][1].type === types.content) {\n        content = index\n        break\n      }\n\n      if (events[index][1].type === types.paragraph) {\n        text = index\n      }\n    }\n    // Exit\n    else {\n      if (events[index][1].type === types.content) {\n        // Remove the content end (if needed we’ll add it later)\n        events.splice(index, 1)\n      }\n\n      if (!definition && events[index][1].type === types.definition) {\n        definition = index\n      }\n    }\n  }\n\n  assert(text !== undefined, 'expected a `text` index to be found')\n  assert(content !== undefined, 'expected a `text` index to be found')\n  assert(events[content][2] === context, 'enter context should be same')\n  assert(\n    events[events.length - 1][2] === context,\n    'enter context should be same'\n  )\n  const heading = {\n    type: types.setextHeading,\n    start: {...events[content][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  // Change the paragraph to setext heading text.\n  events[text][1].type = types.setextHeadingText\n\n  // If we have definitions in the content, we’ll keep on having content,\n  // but we need move it.\n  if (definition) {\n    events.splice(text, 0, ['enter', heading, context])\n    events.splice(definition + 1, 0, ['exit', events[content][1], context])\n    events[content][1].end = {...events[definition][1].end}\n  } else {\n    events[content][1] = heading\n  }\n\n  // Add the heading exit at the end.\n  events.push(['exit', heading, context])\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeSetextUnderline(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * At start of heading (setext) underline.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length\n    /** @type {boolean | undefined} */\n    let paragraph\n\n    assert(\n      code === codes.dash || code === codes.equalsTo,\n      'expected `=` or `-`'\n    )\n\n    // Find an opening.\n    while (index--) {\n      // Skip enter/exit of line ending, line prefix, and content.\n      // We can now either have a definition or a paragraph.\n      if (\n        self.events[index][1].type !== types.lineEnding &&\n        self.events[index][1].type !== types.linePrefix &&\n        self.events[index][1].type !== types.content\n      ) {\n        paragraph = self.events[index][1].type === types.paragraph\n        break\n      }\n    }\n\n    // To do: handle lazy/pierce like `markdown-rs`.\n    // To do: parse indent like `markdown-rs`.\n    if (!self.parser.lazy[self.now().line] && (self.interrupt || paragraph)) {\n      effects.enter(types.setextHeadingLine)\n      marker = code\n      return before(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After optional whitespace, at `-` or `=`.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    effects.enter(types.setextHeadingLineSequence)\n    return inside(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    effects.exit(types.setextHeadingLineSequence)\n\n    return markdownSpace(code)\n      ? factorySpace(effects, after, types.lineSuffix)(code)\n      : after(code)\n  }\n\n  /**\n   * After sequence, after optional whitespace.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.setextHeadingLine)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,EAAEC,aAAa,QAAO,0BAA0B;AAC1E,SAAQC,KAAK,EAAEC,KAAK,QAAO,uBAAuB;;AAElD;AACA,OAAO,MAAMC,eAAe,GAAG;EAC7BC,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEC,wBAAwB;EACnCC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA,SAASF,wBAAwBA,CAACG,MAAM,EAAEC,OAAO,EAAE;EACjD;EACA,IAAIC,KAAK,GAAGF,MAAM,CAACG,MAAM;EACzB;EACA,IAAIC,OAAO;EACX;EACA,IAAIC,IAAI;EACR;EACA,IAAIC,UAAU;;EAEd;EACA;EACA,OAAOJ,KAAK,EAAE,EAAE;IACd,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;MAChC,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACW,OAAO,EAAE;QAC3CA,OAAO,GAAGF,KAAK;QACf;MACF;MAEA,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACe,SAAS,EAAE;QAC7CH,IAAI,GAAGH,KAAK;MACd;IACF;IACA;IAAA,KACK;MACH,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACW,OAAO,EAAE;QAC3C;QACAJ,MAAM,CAACS,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;MACzB;MAEA,IAAI,CAACI,UAAU,IAAIN,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACa,UAAU,EAAE;QAC7DA,UAAU,GAAGJ,KAAK;MACpB;IACF;EACF;EAEAd,MAAM,CAACiB,IAAI,KAAKK,SAAS,EAAE,qCAAqC,CAAC;EACjEtB,MAAM,CAACgB,OAAO,KAAKM,SAAS,EAAE,qCAAqC,CAAC;EACpEtB,MAAM,CAACY,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAKH,OAAO,EAAE,8BAA8B,CAAC;EACtEb,MAAM,CACJY,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKF,OAAO,EACxC,8BACF,CAAC;EACD,MAAMU,OAAO,GAAG;IACdJ,IAAI,EAAEd,KAAK,CAACmB,aAAa;IACzBC,KAAK,EAAE;MAAC,GAAGb,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAACS;IAAK,CAAC;IACpCC,GAAG,EAAE;MAAC,GAAGd,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACW;IAAG;EAC3C,CAAC;;EAED;EACAd,MAAM,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC,CAACE,IAAI,GAAGd,KAAK,CAACsB,iBAAiB;;EAE9C;EACA;EACA,IAAIT,UAAU,EAAE;IACdN,MAAM,CAACS,MAAM,CAACJ,IAAI,EAAE,CAAC,EAAE,CAAC,OAAO,EAAEM,OAAO,EAAEV,OAAO,CAAC,CAAC;IACnDD,MAAM,CAACS,MAAM,CAACH,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAEN,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEH,OAAO,CAAC,CAAC;IACvED,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAACU,GAAG,GAAG;MAAC,GAAGd,MAAM,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAACQ;IAAG,CAAC;EACzD,CAAC,MAAM;IACLd,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGO,OAAO;EAC9B;;EAEA;EACAX,MAAM,CAACgB,IAAI,CAAC,CAAC,MAAM,EAAEL,OAAO,EAAEV,OAAO,CAAC,CAAC;EAEvC,OAAOD,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASD,uBAAuBA,CAACkB,OAAO,EAAE9B,EAAE,EAAE+B,GAAG,EAAE;EACjD,MAAMC,IAAI,GAAG,IAAI;EACjB;EACA,IAAIC,MAAM;EAEV,OAAOP,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACQ,IAAI,EAAE;IACnB,IAAInB,KAAK,GAAGiB,IAAI,CAACnB,MAAM,CAACG,MAAM;IAC9B;IACA,IAAIK,SAAS;IAEbpB,MAAM,CACJiC,IAAI,KAAK7B,KAAK,CAAC8B,IAAI,IAAID,IAAI,KAAK7B,KAAK,CAAC+B,QAAQ,EAC9C,qBACF,CAAC;;IAED;IACA,OAAOrB,KAAK,EAAE,EAAE;MACd;MACA;MACA,IACEiB,IAAI,CAACnB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAAC+B,UAAU,IAC/CL,IAAI,CAACnB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACgC,UAAU,IAC/CN,IAAI,CAACnB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACW,OAAO,EAC5C;QACAI,SAAS,GAAGW,IAAI,CAACnB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKd,KAAK,CAACe,SAAS;QAC1D;MACF;IACF;;IAEA;IACA;IACA,IAAI,CAACW,IAAI,CAACO,MAAM,CAACC,IAAI,CAACR,IAAI,CAACS,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,KAAKV,IAAI,CAACW,SAAS,IAAItB,SAAS,CAAC,EAAE;MACvES,OAAO,CAACc,KAAK,CAACtC,KAAK,CAACuC,iBAAiB,CAAC;MACtCZ,MAAM,GAAGC,IAAI;MACb,OAAOY,MAAM,CAACZ,IAAI,CAAC;IACrB;IAEA,OAAOH,GAAG,CAACG,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASY,MAAMA,CAACZ,IAAI,EAAE;IACpBJ,OAAO,CAACc,KAAK,CAACtC,KAAK,CAACyC,yBAAyB,CAAC;IAC9C,OAAOC,MAAM,CAACd,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASc,MAAMA,CAACd,IAAI,EAAE;IACpB,IAAIA,IAAI,KAAKD,MAAM,EAAE;MACnBH,OAAO,CAACmB,OAAO,CAACf,IAAI,CAAC;MACrB,OAAOc,MAAM;IACf;IAEAlB,OAAO,CAACoB,IAAI,CAAC5C,KAAK,CAACyC,yBAAyB,CAAC;IAE7C,OAAO3C,aAAa,CAAC8B,IAAI,CAAC,GACtBhC,YAAY,CAAC4B,OAAO,EAAEqB,KAAK,EAAE7C,KAAK,CAAC8C,UAAU,CAAC,CAAClB,IAAI,CAAC,GACpDiB,KAAK,CAACjB,IAAI,CAAC;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiB,KAAKA,CAACjB,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAK7B,KAAK,CAACgD,GAAG,IAAIlD,kBAAkB,CAAC+B,IAAI,CAAC,EAAE;MAClDJ,OAAO,CAACoB,IAAI,CAAC5C,KAAK,CAACuC,iBAAiB,CAAC;MACrC,OAAO7C,EAAE,CAACkC,IAAI,CAAC;IACjB;IAEA,OAAOH,GAAG,CAACG,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}