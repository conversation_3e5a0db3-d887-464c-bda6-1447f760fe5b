{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport { VFileMessage } from './lib/index.js';", "map": {"version": 3, "names": ["VFileMessage"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/vfile-message/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport {VFileMessage} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}