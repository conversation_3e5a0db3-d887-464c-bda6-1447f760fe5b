{"ast": null, "code": "/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-from-markdown').Options} FromMarkdownOptions\n * @typedef {import('unified').Parser<Root>} Parser\n * @typedef {import('unified').Processor<Root>} Processor\n */\n\n/**\n * @typedef {Omit<FromMarkdownOptions, 'extensions' | 'mdastExtensions'>} Options\n */\n\nimport { fromMarkdown } from 'mdast-util-from-markdown';\n\n/**\n * Aadd support for parsing from markdown.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function remarkParse(options) {\n  /** @type {Processor} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this;\n  self.parser = parser;\n\n  /**\n   * @type {Parser}\n   */\n  function parser(doc) {\n    return fromMarkdown(doc, {\n      ...self.data('settings'),\n      ...options,\n      // Note: these options are not in the readme.\n      // The goal is for them to be set by plugins on `data` instead of being\n      // passed by users.\n      extensions: self.data('micromarkExtensions') || [],\n      mdastExtensions: self.data('fromMarkdownExtensions') || []\n    });\n  }\n}", "map": {"version": 3, "names": ["fromMarkdown", "remark<PERSON><PERSON><PERSON>", "options", "self", "parser", "doc", "data", "extensions", "mdastExtensions"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/remark-parse/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-from-markdown').Options} FromMarkdownOptions\n * @typedef {import('unified').Parser<Root>} Parser\n * @typedef {import('unified').Processor<Root>} Processor\n */\n\n/**\n * @typedef {Omit<FromMarkdownOptions, 'extensions' | 'mdastExtensions'>} Options\n */\n\nimport {fromMarkdown} from 'mdast-util-from-markdown'\n\n/**\n * Aadd support for parsing from markdown.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function remarkParse(options) {\n  /** @type {Processor} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n\n  self.parser = parser\n\n  /**\n   * @type {Parser}\n   */\n  function parser(doc) {\n    return fromMarkdown(doc, {\n      ...self.data('settings'),\n      ...options,\n      // Note: these options are not in the readme.\n      // The goal is for them to be set by plugins on `data` instead of being\n      // passed by users.\n      extensions: self.data('micromarkExtensions') || [],\n      mdastExtensions: self.data('fromMarkdownExtensions') || []\n    })\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,0BAA0B;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC3C;EACA;EACA,MAAMC,IAAI,GAAG,IAAI;EAEjBA,IAAI,CAACC,MAAM,GAAGA,MAAM;;EAEpB;AACF;AACA;EACE,SAASA,MAAMA,CAACC,GAAG,EAAE;IACnB,OAAOL,YAAY,CAACK,GAAG,EAAE;MACvB,GAAGF,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC;MACxB,GAAGJ,OAAO;MACV;MACA;MACA;MACAK,UAAU,EAAEJ,IAAI,CAACG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE;MAClDE,eAAe,EAAEL,IAAI,CAACG,IAAI,CAAC,wBAAwB,CAAC,IAAI;IAC1D,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}