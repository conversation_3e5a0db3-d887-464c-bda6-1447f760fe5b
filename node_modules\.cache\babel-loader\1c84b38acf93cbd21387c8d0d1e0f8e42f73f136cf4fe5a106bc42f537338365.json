{"ast": null, "code": "/**\n * @import {Code} from 'micromark-util-types'\n */\n\nimport { markdownLineEndingOrSpace, unicodePunctuation, unicodeWhitespace } from 'micromark-util-character';\nimport { codes, constants } from 'micromark-util-symbol';\n\n/**\n * Classify whether a code represents whitespace, punctuation, or something\n * else.\n *\n * Used for attention (emphasis, strong), whose sequences can open or close\n * based on the class of surrounding characters.\n *\n * > 👉 **Note**: eof (`null`) is seen as whitespace.\n *\n * @param {Code} code\n *   Code.\n * @returns {typeof constants.characterGroupWhitespace | typeof constants.characterGroupPunctuation | undefined}\n *   Group.\n */\nexport function classifyCharacter(code) {\n  if (code === codes.eof || markdownLineEndingOrSpace(code) || unicodeWhitespace(code)) {\n    return constants.characterGroupWhitespace;\n  }\n  if (unicodePunctuation(code)) {\n    return constants.characterGroupPunctuation;\n  }\n}", "map": {"version": 3, "names": ["markdownLineEndingOrSpace", "unicodePunctuation", "unicodeWhitespace", "codes", "constants", "classifyCharacter", "code", "eof", "characterGroupWhitespace", "characterGroupPunctuation"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark-util-classify-character/dev/index.js"], "sourcesContent": ["/**\n * @import {Code} from 'micromark-util-types'\n */\n\nimport {\n  markdownLineEndingOrSpace,\n  unicodePunctuation,\n  unicodeWhitespace\n} from 'micromark-util-character'\nimport {codes, constants} from 'micromark-util-symbol'\n\n/**\n * Classify whether a code represents whitespace, punctuation, or something\n * else.\n *\n * Used for attention (emphasis, strong), whose sequences can open or close\n * based on the class of surrounding characters.\n *\n * > 👉 **Note**: eof (`null`) is seen as whitespace.\n *\n * @param {Code} code\n *   Code.\n * @returns {typeof constants.characterGroupWhitespace | typeof constants.characterGroupPunctuation | undefined}\n *   Group.\n */\nexport function classifyCharacter(code) {\n  if (\n    code === codes.eof ||\n    markdownLineEndingOrSpace(code) ||\n    unicodeWhitespace(code)\n  ) {\n    return constants.characterGroupWhitespace\n  }\n\n  if (unicodePunctuation(code)) {\n    return constants.characterGroupPunctuation\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SACEA,yBAAyB,EACzBC,kBAAkB,EAClBC,iBAAiB,QACZ,0BAA0B;AACjC,SAAQC,KAAK,EAAEC,SAAS,QAAO,uBAAuB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACtC,IACEA,IAAI,KAAKH,KAAK,CAACI,GAAG,IAClBP,yBAAyB,CAACM,IAAI,CAAC,IAC/BJ,iBAAiB,CAACI,IAAI,CAAC,EACvB;IACA,OAAOF,SAAS,CAACI,wBAAwB;EAC3C;EAEA,IAAIP,kBAAkB,CAACK,IAAI,CAAC,EAAE;IAC5B,OAAOF,SAAS,CAACK,yBAAyB;EAC5C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}