{"ast": null, "code": "import { VOID, PRIMITIVE, ARRAY, OBJECT, DATE, REGEXP, MAP, SET, ERROR, BIGINT } from './types.js';\nconst EMPTY = '';\nconst {\n  toString\n} = {};\nconst {\n  keys\n} = Object;\nconst typeOf = value => {\n  const type = typeof value;\n  if (type !== 'object' || !value) return [PRIMITIVE, type];\n  const asString = toString.call(value).slice(8, -1);\n  switch (asString) {\n    case 'Array':\n      return [ARRAY, EMPTY];\n    case 'Object':\n      return [OBJECT, EMPTY];\n    case 'Date':\n      return [DATE, EMPTY];\n    case 'RegExp':\n      return [REGEXP, EMPTY];\n    case 'Map':\n      return [MAP, EMPTY];\n    case 'Set':\n      return [SET, EMPTY];\n    case 'DataView':\n      return [ARRAY, asString];\n  }\n  if (asString.includes('Array')) return [ARRAY, asString];\n  if (asString.includes('Error')) return [ERROR, asString];\n  return [OBJECT, asString];\n};\nconst shouldSkip = ([TYPE, type]) => TYPE === PRIMITIVE && (type === 'function' || type === 'symbol');\nconst serializer = (strict, json, $, _) => {\n  const as = (out, value) => {\n    const index = _.push(out) - 1;\n    $.set(value, index);\n    return index;\n  };\n  const pair = value => {\n    if ($.has(value)) return $.get(value);\n    let [TYPE, type] = typeOf(value);\n    switch (TYPE) {\n      case PRIMITIVE:\n        {\n          let entry = value;\n          switch (type) {\n            case 'bigint':\n              TYPE = BIGINT;\n              entry = value.toString();\n              break;\n            case 'function':\n            case 'symbol':\n              if (strict) throw new TypeError('unable to serialize ' + type);\n              entry = null;\n              break;\n            case 'undefined':\n              return as([VOID], value);\n          }\n          return as([TYPE, entry], value);\n        }\n      case ARRAY:\n        {\n          if (type) {\n            let spread = value;\n            if (type === 'DataView') {\n              spread = new Uint8Array(value.buffer);\n            } else if (type === 'ArrayBuffer') {\n              spread = new Uint8Array(value);\n            }\n            return as([type, [...spread]], value);\n          }\n          const arr = [];\n          const index = as([TYPE, arr], value);\n          for (const entry of value) arr.push(pair(entry));\n          return index;\n        }\n      case OBJECT:\n        {\n          if (type) {\n            switch (type) {\n              case 'BigInt':\n                return as([type, value.toString()], value);\n              case 'Boolean':\n              case 'Number':\n              case 'String':\n                return as([type, value.valueOf()], value);\n            }\n          }\n          if (json && 'toJSON' in value) return pair(value.toJSON());\n          const entries = [];\n          const index = as([TYPE, entries], value);\n          for (const key of keys(value)) {\n            if (strict || !shouldSkip(typeOf(value[key]))) entries.push([pair(key), pair(value[key])]);\n          }\n          return index;\n        }\n      case DATE:\n        return as([TYPE, value.toISOString()], value);\n      case REGEXP:\n        {\n          const {\n            source,\n            flags\n          } = value;\n          return as([TYPE, {\n            source,\n            flags\n          }], value);\n        }\n      case MAP:\n        {\n          const entries = [];\n          const index = as([TYPE, entries], value);\n          for (const [key, entry] of value) {\n            if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry)))) entries.push([pair(key), pair(entry)]);\n          }\n          return index;\n        }\n      case SET:\n        {\n          const entries = [];\n          const index = as([TYPE, entries], value);\n          for (const entry of value) {\n            if (strict || !shouldSkip(typeOf(entry))) entries.push(pair(entry));\n          }\n          return index;\n        }\n    }\n    const {\n      message\n    } = value;\n    return as([TYPE, {\n      name: type,\n      message\n    }], value);\n  };\n  return pair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */\nexport const serialize = (value, {\n  json,\n  lossy\n} = {}) => {\n  const _ = [];\n  return serializer(!(json || lossy), !!json, new Map(), _)(value), _;\n};", "map": {"version": 3, "names": ["VOID", "PRIMITIVE", "ARRAY", "OBJECT", "DATE", "REGEXP", "MAP", "SET", "ERROR", "BIGINT", "EMPTY", "toString", "keys", "Object", "typeOf", "value", "type", "asString", "call", "slice", "includes", "shouldSkip", "TYPE", "serializer", "strict", "json", "$", "_", "as", "out", "index", "push", "set", "pair", "has", "get", "entry", "TypeError", "spread", "Uint8Array", "buffer", "arr", "valueOf", "toJSON", "entries", "key", "toISOString", "source", "flags", "message", "name", "serialize", "lossy", "Map"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/@ungap/structured-clone/esm/serialize.js"], "sourcesContent": ["import {\n  VOID, PRIMITIVE,\n  ARRAY, OBJECT,\n  DATE, REGEXP, MAP, SET,\n  ERROR, BIGINT\n} from './types.js';\n\nconst EMPTY = '';\n\nconst {toString} = {};\nconst {keys} = Object;\n\nconst typeOf = value => {\n  const type = typeof value;\n  if (type !== 'object' || !value)\n    return [PRIMITIVE, type];\n\n  const asString = toString.call(value).slice(8, -1);\n  switch (asString) {\n    case 'Array':\n      return [ARRAY, EMPTY];\n    case 'Object':\n      return [OBJECT, EMPTY];\n    case 'Date':\n      return [DATE, EMPTY];\n    case 'RegExp':\n      return [REGEXP, EMPTY];\n    case 'Map':\n      return [MAP, EMPTY];\n    case 'Set':\n      return [SET, EMPTY];\n    case 'DataView':\n      return [ARRAY, asString];\n  }\n\n  if (asString.includes('Array'))\n    return [ARRAY, asString];\n\n  if (asString.includes('Error'))\n    return [ERROR, asString];\n\n  return [OBJECT, asString];\n};\n\nconst shouldSkip = ([TYPE, type]) => (\n  TYPE === PRIMITIVE &&\n  (type === 'function' || type === 'symbol')\n);\n\nconst serializer = (strict, json, $, _) => {\n\n  const as = (out, value) => {\n    const index = _.push(out) - 1;\n    $.set(value, index);\n    return index;\n  };\n\n  const pair = value => {\n    if ($.has(value))\n      return $.get(value);\n\n    let [TYPE, type] = typeOf(value);\n    switch (TYPE) {\n      case PRIMITIVE: {\n        let entry = value;\n        switch (type) {\n          case 'bigint':\n            TYPE = BIGINT;\n            entry = value.toString();\n            break;\n          case 'function':\n          case 'symbol':\n            if (strict)\n              throw new TypeError('unable to serialize ' + type);\n            entry = null;\n            break;\n          case 'undefined':\n            return as([VOID], value);\n        }\n        return as([TYPE, entry], value);\n      }\n      case ARRAY: {\n        if (type) {\n          let spread = value;\n          if (type === 'DataView') {\n            spread = new Uint8Array(value.buffer);\n          }\n          else if (type === 'ArrayBuffer') {\n            spread = new Uint8Array(value);\n          }\n          return as([type, [...spread]], value);\n        }\n\n        const arr = [];\n        const index = as([TYPE, arr], value);\n        for (const entry of value)\n          arr.push(pair(entry));\n        return index;\n      }\n      case OBJECT: {\n        if (type) {\n          switch (type) {\n            case 'BigInt':\n              return as([type, value.toString()], value);\n            case 'Boolean':\n            case 'Number':\n            case 'String':\n              return as([type, value.valueOf()], value);\n          }\n        }\n\n        if (json && ('toJSON' in value))\n          return pair(value.toJSON());\n\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const key of keys(value)) {\n          if (strict || !shouldSkip(typeOf(value[key])))\n            entries.push([pair(key), pair(value[key])]);\n        }\n        return index;\n      }\n      case DATE:\n        return as([TYPE, value.toISOString()], value);\n      case REGEXP: {\n        const {source, flags} = value;\n        return as([TYPE, {source, flags}], value);\n      }\n      case MAP: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const [key, entry] of value) {\n          if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry))))\n            entries.push([pair(key), pair(entry)]);\n        }\n        return index;\n      }\n      case SET: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const entry of value) {\n          if (strict || !shouldSkip(typeOf(entry)))\n            entries.push(pair(entry));\n        }\n        return index;\n      }\n    }\n\n    const {message} = value;\n    return as([TYPE, {name: type, message}], value);\n  };\n\n  return pair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */\n export const serialize = (value, {json, lossy} = {}) => {\n  const _ = [];\n  return serializer(!(json || lossy), !!json, new Map, _)(value), _;\n};\n"], "mappings": "AAAA,SACEA,IAAI,EAAEC,SAAS,EACfC,KAAK,EAAEC,MAAM,EACbC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EACtBC,KAAK,EAAEC,MAAM,QACR,YAAY;AAEnB,MAAMC,KAAK,GAAG,EAAE;AAEhB,MAAM;EAACC;AAAQ,CAAC,GAAG,CAAC,CAAC;AACrB,MAAM;EAACC;AAAI,CAAC,GAAGC,MAAM;AAErB,MAAMC,MAAM,GAAGC,KAAK,IAAI;EACtB,MAAMC,IAAI,GAAG,OAAOD,KAAK;EACzB,IAAIC,IAAI,KAAK,QAAQ,IAAI,CAACD,KAAK,EAC7B,OAAO,CAACd,SAAS,EAAEe,IAAI,CAAC;EAE1B,MAAMC,QAAQ,GAAGN,QAAQ,CAACO,IAAI,CAACH,KAAK,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,QAAQF,QAAQ;IACd,KAAK,OAAO;MACV,OAAO,CAACf,KAAK,EAAEQ,KAAK,CAAC;IACvB,KAAK,QAAQ;MACX,OAAO,CAACP,MAAM,EAAEO,KAAK,CAAC;IACxB,KAAK,MAAM;MACT,OAAO,CAACN,IAAI,EAAEM,KAAK,CAAC;IACtB,KAAK,QAAQ;MACX,OAAO,CAACL,MAAM,EAAEK,KAAK,CAAC;IACxB,KAAK,KAAK;MACR,OAAO,CAACJ,GAAG,EAAEI,KAAK,CAAC;IACrB,KAAK,KAAK;MACR,OAAO,CAACH,GAAG,EAAEG,KAAK,CAAC;IACrB,KAAK,UAAU;MACb,OAAO,CAACR,KAAK,EAAEe,QAAQ,CAAC;EAC5B;EAEA,IAAIA,QAAQ,CAACG,QAAQ,CAAC,OAAO,CAAC,EAC5B,OAAO,CAAClB,KAAK,EAAEe,QAAQ,CAAC;EAE1B,IAAIA,QAAQ,CAACG,QAAQ,CAAC,OAAO,CAAC,EAC5B,OAAO,CAACZ,KAAK,EAAES,QAAQ,CAAC;EAE1B,OAAO,CAACd,MAAM,EAAEc,QAAQ,CAAC;AAC3B,CAAC;AAED,MAAMI,UAAU,GAAGA,CAAC,CAACC,IAAI,EAAEN,IAAI,CAAC,KAC9BM,IAAI,KAAKrB,SAAS,KACjBe,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,QAAQ,CAC1C;AAED,MAAMO,UAAU,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAEzC,MAAMC,EAAE,GAAGA,CAACC,GAAG,EAAEd,KAAK,KAAK;IACzB,MAAMe,KAAK,GAAGH,CAAC,CAACI,IAAI,CAACF,GAAG,CAAC,GAAG,CAAC;IAC7BH,CAAC,CAACM,GAAG,CAACjB,KAAK,EAAEe,KAAK,CAAC;IACnB,OAAOA,KAAK;EACd,CAAC;EAED,MAAMG,IAAI,GAAGlB,KAAK,IAAI;IACpB,IAAIW,CAAC,CAACQ,GAAG,CAACnB,KAAK,CAAC,EACd,OAAOW,CAAC,CAACS,GAAG,CAACpB,KAAK,CAAC;IAErB,IAAI,CAACO,IAAI,EAAEN,IAAI,CAAC,GAAGF,MAAM,CAACC,KAAK,CAAC;IAChC,QAAQO,IAAI;MACV,KAAKrB,SAAS;QAAE;UACd,IAAImC,KAAK,GAAGrB,KAAK;UACjB,QAAQC,IAAI;YACV,KAAK,QAAQ;cACXM,IAAI,GAAGb,MAAM;cACb2B,KAAK,GAAGrB,KAAK,CAACJ,QAAQ,CAAC,CAAC;cACxB;YACF,KAAK,UAAU;YACf,KAAK,QAAQ;cACX,IAAIa,MAAM,EACR,MAAM,IAAIa,SAAS,CAAC,sBAAsB,GAAGrB,IAAI,CAAC;cACpDoB,KAAK,GAAG,IAAI;cACZ;YACF,KAAK,WAAW;cACd,OAAOR,EAAE,CAAC,CAAC5B,IAAI,CAAC,EAAEe,KAAK,CAAC;UAC5B;UACA,OAAOa,EAAE,CAAC,CAACN,IAAI,EAAEc,KAAK,CAAC,EAAErB,KAAK,CAAC;QACjC;MACA,KAAKb,KAAK;QAAE;UACV,IAAIc,IAAI,EAAE;YACR,IAAIsB,MAAM,GAAGvB,KAAK;YAClB,IAAIC,IAAI,KAAK,UAAU,EAAE;cACvBsB,MAAM,GAAG,IAAIC,UAAU,CAACxB,KAAK,CAACyB,MAAM,CAAC;YACvC,CAAC,MACI,IAAIxB,IAAI,KAAK,aAAa,EAAE;cAC/BsB,MAAM,GAAG,IAAIC,UAAU,CAACxB,KAAK,CAAC;YAChC;YACA,OAAOa,EAAE,CAAC,CAACZ,IAAI,EAAE,CAAC,GAAGsB,MAAM,CAAC,CAAC,EAAEvB,KAAK,CAAC;UACvC;UAEA,MAAM0B,GAAG,GAAG,EAAE;UACd,MAAMX,KAAK,GAAGF,EAAE,CAAC,CAACN,IAAI,EAAEmB,GAAG,CAAC,EAAE1B,KAAK,CAAC;UACpC,KAAK,MAAMqB,KAAK,IAAIrB,KAAK,EACvB0B,GAAG,CAACV,IAAI,CAACE,IAAI,CAACG,KAAK,CAAC,CAAC;UACvB,OAAON,KAAK;QACd;MACA,KAAK3B,MAAM;QAAE;UACX,IAAIa,IAAI,EAAE;YACR,QAAQA,IAAI;cACV,KAAK,QAAQ;gBACX,OAAOY,EAAE,CAAC,CAACZ,IAAI,EAAED,KAAK,CAACJ,QAAQ,CAAC,CAAC,CAAC,EAAEI,KAAK,CAAC;cAC5C,KAAK,SAAS;cACd,KAAK,QAAQ;cACb,KAAK,QAAQ;gBACX,OAAOa,EAAE,CAAC,CAACZ,IAAI,EAAED,KAAK,CAAC2B,OAAO,CAAC,CAAC,CAAC,EAAE3B,KAAK,CAAC;YAC7C;UACF;UAEA,IAAIU,IAAI,IAAK,QAAQ,IAAIV,KAAM,EAC7B,OAAOkB,IAAI,CAAClB,KAAK,CAAC4B,MAAM,CAAC,CAAC,CAAC;UAE7B,MAAMC,OAAO,GAAG,EAAE;UAClB,MAAMd,KAAK,GAAGF,EAAE,CAAC,CAACN,IAAI,EAAEsB,OAAO,CAAC,EAAE7B,KAAK,CAAC;UACxC,KAAK,MAAM8B,GAAG,IAAIjC,IAAI,CAACG,KAAK,CAAC,EAAE;YAC7B,IAAIS,MAAM,IAAI,CAACH,UAAU,CAACP,MAAM,CAACC,KAAK,CAAC8B,GAAG,CAAC,CAAC,CAAC,EAC3CD,OAAO,CAACb,IAAI,CAAC,CAACE,IAAI,CAACY,GAAG,CAAC,EAAEZ,IAAI,CAAClB,KAAK,CAAC8B,GAAG,CAAC,CAAC,CAAC,CAAC;UAC/C;UACA,OAAOf,KAAK;QACd;MACA,KAAK1B,IAAI;QACP,OAAOwB,EAAE,CAAC,CAACN,IAAI,EAAEP,KAAK,CAAC+B,WAAW,CAAC,CAAC,CAAC,EAAE/B,KAAK,CAAC;MAC/C,KAAKV,MAAM;QAAE;UACX,MAAM;YAAC0C,MAAM;YAAEC;UAAK,CAAC,GAAGjC,KAAK;UAC7B,OAAOa,EAAE,CAAC,CAACN,IAAI,EAAE;YAACyB,MAAM;YAAEC;UAAK,CAAC,CAAC,EAAEjC,KAAK,CAAC;QAC3C;MACA,KAAKT,GAAG;QAAE;UACR,MAAMsC,OAAO,GAAG,EAAE;UAClB,MAAMd,KAAK,GAAGF,EAAE,CAAC,CAACN,IAAI,EAAEsB,OAAO,CAAC,EAAE7B,KAAK,CAAC;UACxC,KAAK,MAAM,CAAC8B,GAAG,EAAET,KAAK,CAAC,IAAIrB,KAAK,EAAE;YAChC,IAAIS,MAAM,IAAI,EAAEH,UAAU,CAACP,MAAM,CAAC+B,GAAG,CAAC,CAAC,IAAIxB,UAAU,CAACP,MAAM,CAACsB,KAAK,CAAC,CAAC,CAAC,EACnEQ,OAAO,CAACb,IAAI,CAAC,CAACE,IAAI,CAACY,GAAG,CAAC,EAAEZ,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC;UAC1C;UACA,OAAON,KAAK;QACd;MACA,KAAKvB,GAAG;QAAE;UACR,MAAMqC,OAAO,GAAG,EAAE;UAClB,MAAMd,KAAK,GAAGF,EAAE,CAAC,CAACN,IAAI,EAAEsB,OAAO,CAAC,EAAE7B,KAAK,CAAC;UACxC,KAAK,MAAMqB,KAAK,IAAIrB,KAAK,EAAE;YACzB,IAAIS,MAAM,IAAI,CAACH,UAAU,CAACP,MAAM,CAACsB,KAAK,CAAC,CAAC,EACtCQ,OAAO,CAACb,IAAI,CAACE,IAAI,CAACG,KAAK,CAAC,CAAC;UAC7B;UACA,OAAON,KAAK;QACd;IACF;IAEA,MAAM;MAACmB;IAAO,CAAC,GAAGlC,KAAK;IACvB,OAAOa,EAAE,CAAC,CAACN,IAAI,EAAE;MAAC4B,IAAI,EAAElC,IAAI;MAAEiC;IAAO,CAAC,CAAC,EAAElC,KAAK,CAAC;EACjD,CAAC;EAED,OAAOkB,IAAI;AACb,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACC,OAAO,MAAMkB,SAAS,GAAGA,CAACpC,KAAK,EAAE;EAACU,IAAI;EAAE2B;AAAK,CAAC,GAAG,CAAC,CAAC,KAAK;EACvD,MAAMzB,CAAC,GAAG,EAAE;EACZ,OAAOJ,UAAU,CAAC,EAAEE,IAAI,IAAI2B,KAAK,CAAC,EAAE,CAAC,CAAC3B,IAAI,EAAE,IAAI4B,GAAG,CAAD,CAAC,EAAE1B,CAAC,CAAC,CAACZ,KAAK,CAAC,EAAEY,CAAC;AACnE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}