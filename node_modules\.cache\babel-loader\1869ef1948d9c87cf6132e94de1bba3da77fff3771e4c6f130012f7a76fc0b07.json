{"ast": null, "code": "import { dequal } from 'dequal';\n\n/**\n * @type {Set<string>}\n */\nconst codesWarned = new Set();\nclass AssertionError extends Error {\n  name = (/** @type {const} */'Assertion');\n  code = (/** @type {const} */'ERR_ASSERTION');\n\n  /**\n   * Create an assertion error.\n   *\n   * @param {string} message\n   *   Message explaining error.\n   * @param {unknown} actual\n   *   Value.\n   * @param {unknown} expected\n   *   Baseline.\n   * @param {string} operator\n   *   Name of equality operation.\n   * @param {boolean} generated\n   *   Whether `message` is a custom message or not\n   * @returns\n   *   Instance.\n   */\n  // eslint-disable-next-line max-params\n  constructor(message, actual, expected, operator, generated) {\n    super(message);\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n\n    /**\n     * @type {unknown}\n     */\n    this.actual = actual;\n\n    /**\n     * @type {unknown}\n     */\n    this.expected = expected;\n\n    /**\n     * @type {boolean}\n     */\n    this.generated = generated;\n\n    /**\n     * @type {string}\n     */\n    this.operator = operator;\n  }\n}\nclass DeprecationError extends Error {\n  name = (/** @type {const} */'DeprecationWarning');\n\n  /**\n   * Create a deprecation message.\n   *\n   * @param {string} message\n   *   Message explaining deprecation.\n   * @param {string | undefined} code\n   *   Deprecation identifier; deprecation messages will be generated only once per code.\n   * @returns\n   *   Instance.\n   */\n  constructor(message, code) {\n    super(message);\n\n    /**\n     * @type {string | undefined}\n     */\n    this.code = code;\n  }\n}\n\n/**\n * Wrap a function or class to show a deprecation message when first called.\n *\n * > 👉 **Important**: only shows a message when the `development` condition is\n * > used, does nothing in production.\n *\n * When the resulting wrapped `fn` is called, emits a warning once to\n * `console.error` (`stderr`).\n * If a code is given, one warning message will be emitted in total per code.\n *\n * @template {Function} T\n *   Function or class kind.\n * @param {T} fn\n *   Function or class.\n * @param {string} message\n *   Message explaining deprecation.\n * @param {string | null | undefined} [code]\n *   Deprecation identifier (optional); deprecation messages will be generated\n *   only once per code.\n * @returns {T}\n *   Wrapped `fn`.\n */\nexport function deprecate(fn, message, code) {\n  let warned = false;\n\n  // The wrapper will keep the same prototype as fn to maintain prototype chain\n  Object.setPrototypeOf(deprecated, fn);\n\n  // @ts-expect-error: it’s perfect, typescript…\n  return deprecated;\n\n  /**\n   * @this {unknown}\n   * @param  {...Array<unknown>} args\n   * @returns {unknown}\n   */\n  function deprecated(...args) {\n    if (!warned) {\n      warned = true;\n      if (typeof code === 'string' && codesWarned.has(code)) {\n        // Empty.\n      } else {\n        console.error(new DeprecationError(message, code || undefined));\n        if (typeof code === 'string') codesWarned.add(code);\n      }\n    }\n    return new.target ? Reflect.construct(fn, args, new.target) : Reflect.apply(fn, this, args);\n  }\n}\n\n/**\n * Assert deep strict equivalence.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @template {unknown} T\n *   Expected kind.\n * @param {unknown} actual\n *   Value.\n * @param {T} expected\n *   Baseline.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected values to be deeply equal'`).\n * @returns {asserts actual is T}\n *   Nothing; throws when `actual` is not deep strict equal to `expected`.\n * @throws {AssertionError}\n *   Throws when `actual` is not deep strict equal to `expected`.\n */\nexport function equal(actual, expected, message) {\n  assert(dequal(actual, expected), actual, expected, 'equal', 'Expected values to be deeply equal', message);\n}\n\n/**\n * Assert if `value` is truthy.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {unknown} value\n *   Value to assert.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected value to be truthy'`).\n * @returns {asserts value}\n *   Nothing; throws when `value` is falsey.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function ok(value, message) {\n  assert(Boolean(value), false, true, 'ok', 'Expected value to be truthy', message);\n}\n\n/**\n * Assert that a code path never happens.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Unreachable'`).\n * @returns {never}\n *   Nothing; always throws.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function unreachable(message) {\n  assert(false, false, true, 'ok', 'Unreachable', message);\n}\n\n/**\n * @param {boolean} bool\n *   Whether to skip this operation.\n * @param {unknown} actual\n *   Actual value.\n * @param {unknown} expected\n *   Expected value.\n * @param {string} operator\n *   Operator.\n * @param {string} defaultMessage\n *   Default message for operation.\n * @param {Error | string | null | undefined} userMessage\n *   User-provided message.\n * @returns {asserts bool}\n *   Nothing; throws when falsey.\n */\n// eslint-disable-next-line max-params\nfunction assert(bool, actual, expected, operator, defaultMessage, userMessage) {\n  if (!bool) {\n    throw userMessage instanceof Error ? userMessage : new AssertionError(userMessage || defaultMessage, actual, expected, operator, !userMessage);\n  }\n}", "map": {"version": 3, "names": ["dequal", "codesWarned", "Set", "AssertionError", "Error", "name", "code", "constructor", "message", "actual", "expected", "operator", "generated", "captureStackTrace", "DeprecationError", "deprecate", "fn", "warned", "Object", "setPrototypeOf", "deprecated", "args", "has", "console", "error", "undefined", "add", "new", "target", "Reflect", "construct", "apply", "equal", "assert", "ok", "value", "Boolean", "unreachable", "bool", "defaultMessage", "userMessage"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/devlop/lib/development.js"], "sourcesContent": ["import {dequal} from 'dequal'\n\n/**\n * @type {Set<string>}\n */\nconst codesWarned = new Set()\n\nclass AssertionError extends Error {\n  name = /** @type {const} */ ('Assertion')\n  code = /** @type {const} */ ('ERR_ASSERTION')\n\n  /**\n   * Create an assertion error.\n   *\n   * @param {string} message\n   *   Message explaining error.\n   * @param {unknown} actual\n   *   Value.\n   * @param {unknown} expected\n   *   Baseline.\n   * @param {string} operator\n   *   Name of equality operation.\n   * @param {boolean} generated\n   *   Whether `message` is a custom message or not\n   * @returns\n   *   Instance.\n   */\n  // eslint-disable-next-line max-params\n  constructor(message, actual, expected, operator, generated) {\n    super(message)\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor)\n    }\n\n    /**\n     * @type {unknown}\n     */\n    this.actual = actual\n\n    /**\n     * @type {unknown}\n     */\n    this.expected = expected\n\n    /**\n     * @type {boolean}\n     */\n    this.generated = generated\n\n    /**\n     * @type {string}\n     */\n    this.operator = operator\n  }\n}\n\nclass DeprecationE<PERSON>r extends Error {\n  name = /** @type {const} */ ('DeprecationWarning')\n\n  /**\n   * Create a deprecation message.\n   *\n   * @param {string} message\n   *   Message explaining deprecation.\n   * @param {string | undefined} code\n   *   Deprecation identifier; deprecation messages will be generated only once per code.\n   * @returns\n   *   Instance.\n   */\n  constructor(message, code) {\n    super(message)\n\n    /**\n     * @type {string | undefined}\n     */\n    this.code = code\n  }\n}\n\n/**\n * Wrap a function or class to show a deprecation message when first called.\n *\n * > 👉 **Important**: only shows a message when the `development` condition is\n * > used, does nothing in production.\n *\n * When the resulting wrapped `fn` is called, emits a warning once to\n * `console.error` (`stderr`).\n * If a code is given, one warning message will be emitted in total per code.\n *\n * @template {Function} T\n *   Function or class kind.\n * @param {T} fn\n *   Function or class.\n * @param {string} message\n *   Message explaining deprecation.\n * @param {string | null | undefined} [code]\n *   Deprecation identifier (optional); deprecation messages will be generated\n *   only once per code.\n * @returns {T}\n *   Wrapped `fn`.\n */\nexport function deprecate(fn, message, code) {\n  let warned = false\n\n  // The wrapper will keep the same prototype as fn to maintain prototype chain\n  Object.setPrototypeOf(deprecated, fn)\n\n  // @ts-expect-error: it’s perfect, typescript…\n  return deprecated\n\n  /**\n   * @this {unknown}\n   * @param  {...Array<unknown>} args\n   * @returns {unknown}\n   */\n  function deprecated(...args) {\n    if (!warned) {\n      warned = true\n\n      if (typeof code === 'string' && codesWarned.has(code)) {\n        // Empty.\n      } else {\n        console.error(new DeprecationError(message, code || undefined))\n\n        if (typeof code === 'string') codesWarned.add(code)\n      }\n    }\n\n    return new.target\n      ? Reflect.construct(fn, args, new.target)\n      : Reflect.apply(fn, this, args)\n  }\n}\n\n/**\n * Assert deep strict equivalence.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @template {unknown} T\n *   Expected kind.\n * @param {unknown} actual\n *   Value.\n * @param {T} expected\n *   Baseline.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected values to be deeply equal'`).\n * @returns {asserts actual is T}\n *   Nothing; throws when `actual` is not deep strict equal to `expected`.\n * @throws {AssertionError}\n *   Throws when `actual` is not deep strict equal to `expected`.\n */\nexport function equal(actual, expected, message) {\n  assert(\n    dequal(actual, expected),\n    actual,\n    expected,\n    'equal',\n    'Expected values to be deeply equal',\n    message\n  )\n}\n\n/**\n * Assert if `value` is truthy.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {unknown} value\n *   Value to assert.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected value to be truthy'`).\n * @returns {asserts value}\n *   Nothing; throws when `value` is falsey.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function ok(value, message) {\n  assert(\n    Boolean(value),\n    false,\n    true,\n    'ok',\n    'Expected value to be truthy',\n    message\n  )\n}\n\n/**\n * Assert that a code path never happens.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Unreachable'`).\n * @returns {never}\n *   Nothing; always throws.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function unreachable(message) {\n  assert(false, false, true, 'ok', 'Unreachable', message)\n}\n\n/**\n * @param {boolean} bool\n *   Whether to skip this operation.\n * @param {unknown} actual\n *   Actual value.\n * @param {unknown} expected\n *   Expected value.\n * @param {string} operator\n *   Operator.\n * @param {string} defaultMessage\n *   Default message for operation.\n * @param {Error | string | null | undefined} userMessage\n *   User-provided message.\n * @returns {asserts bool}\n *   Nothing; throws when falsey.\n */\n// eslint-disable-next-line max-params\nfunction assert(bool, actual, expected, operator, defaultMessage, userMessage) {\n  if (!bool) {\n    throw userMessage instanceof Error\n      ? userMessage\n      : new AssertionError(\n          userMessage || defaultMessage,\n          actual,\n          expected,\n          operator,\n          !userMessage\n        )\n  }\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,QAAQ;;AAE7B;AACA;AACA;AACA,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;AAE7B,MAAMC,cAAc,SAASC,KAAK,CAAC;EACjCC,IAAI,IAAG,oBAAsB,WAAW;EACxCC,IAAI,IAAG,oBAAsB,eAAe;;EAE5C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE;EACAC,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAE;IAC1D,KAAK,CAACJ,OAAO,CAAC;IAEd,IAAIJ,KAAK,CAACS,iBAAiB,EAAE;MAC3BT,KAAK,CAACS,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACN,WAAW,CAAC;IACjD;;IAEA;AACJ;AACA;IACI,IAAI,CAACE,MAAM,GAAGA,MAAM;;IAEpB;AACJ;AACA;IACI,IAAI,CAACC,QAAQ,GAAGA,QAAQ;;IAExB;AACJ;AACA;IACI,IAAI,CAACE,SAAS,GAAGA,SAAS;;IAE1B;AACJ;AACA;IACI,IAAI,CAACD,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAEA,MAAMG,gBAAgB,SAASV,KAAK,CAAC;EACnCC,IAAI,IAAG,oBAAsB,oBAAoB;;EAEjD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,WAAWA,CAACC,OAAO,EAAEF,IAAI,EAAE;IACzB,KAAK,CAACE,OAAO,CAAC;;IAEd;AACJ;AACA;IACI,IAAI,CAACF,IAAI,GAAGA,IAAI;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,SAASA,CAACC,EAAE,EAAER,OAAO,EAAEF,IAAI,EAAE;EAC3C,IAAIW,MAAM,GAAG,KAAK;;EAElB;EACAC,MAAM,CAACC,cAAc,CAACC,UAAU,EAAEJ,EAAE,CAAC;;EAErC;EACA,OAAOI,UAAU;;EAEjB;AACF;AACA;AACA;AACA;EACE,SAASA,UAAUA,CAAC,GAAGC,IAAI,EAAE;IAC3B,IAAI,CAACJ,MAAM,EAAE;MACXA,MAAM,GAAG,IAAI;MAEb,IAAI,OAAOX,IAAI,KAAK,QAAQ,IAAIL,WAAW,CAACqB,GAAG,CAAChB,IAAI,CAAC,EAAE;QACrD;MAAA,CACD,MAAM;QACLiB,OAAO,CAACC,KAAK,CAAC,IAAIV,gBAAgB,CAACN,OAAO,EAAEF,IAAI,IAAImB,SAAS,CAAC,CAAC;QAE/D,IAAI,OAAOnB,IAAI,KAAK,QAAQ,EAAEL,WAAW,CAACyB,GAAG,CAACpB,IAAI,CAAC;MACrD;IACF;IAEA,OAAOqB,GAAG,CAACC,MAAM,GACbC,OAAO,CAACC,SAAS,CAACd,EAAE,EAAEK,IAAI,EAAEM,GAAG,CAACC,MAAM,CAAC,GACvCC,OAAO,CAACE,KAAK,CAACf,EAAE,EAAE,IAAI,EAAEK,IAAI,CAAC;EACnC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,KAAKA,CAACvB,MAAM,EAAEC,QAAQ,EAAEF,OAAO,EAAE;EAC/CyB,MAAM,CACJjC,MAAM,CAACS,MAAM,EAAEC,QAAQ,CAAC,EACxBD,MAAM,EACNC,QAAQ,EACR,OAAO,EACP,oCAAoC,EACpCF,OACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0B,EAAEA,CAACC,KAAK,EAAE3B,OAAO,EAAE;EACjCyB,MAAM,CACJG,OAAO,CAACD,KAAK,CAAC,EACd,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,6BAA6B,EAC7B3B,OACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6B,WAAWA,CAAC7B,OAAO,EAAE;EACnCyB,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAEzB,OAAO,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyB,MAAMA,CAACK,IAAI,EAAE7B,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAE4B,cAAc,EAAEC,WAAW,EAAE;EAC7E,IAAI,CAACF,IAAI,EAAE;IACT,MAAME,WAAW,YAAYpC,KAAK,GAC9BoC,WAAW,GACX,IAAIrC,cAAc,CAChBqC,WAAW,IAAID,cAAc,EAC7B9B,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACR,CAAC6B,WACH,CAAC;EACP;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}