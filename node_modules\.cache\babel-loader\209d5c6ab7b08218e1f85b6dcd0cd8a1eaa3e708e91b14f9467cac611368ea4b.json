{"ast": null, "code": "export class EmailJSResponseStatus {\n  constructor(httpResponse) {\n    this.status = httpResponse.status;\n    this.text = httpResponse.responseText;\n  }\n}", "map": {"version": 3, "names": ["EmailJSResponseStatus", "constructor", "httpResponse", "status", "text", "responseText"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/emailjs-com/es/models/EmailJSResponseStatus.js"], "sourcesContent": ["export class EmailJSResponseStatus {\n    constructor(httpResponse) {\n        this.status = httpResponse.status;\n        this.text = httpResponse.responseText;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,CAAC;EAC/BC,WAAWA,CAACC,YAAY,EAAE;IACtB,IAAI,CAACC,MAAM,GAAGD,YAAY,CAACC,MAAM;IACjC,IAAI,CAACC,IAAI,GAAGF,YAAY,CAACG,YAAY;EACzC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}