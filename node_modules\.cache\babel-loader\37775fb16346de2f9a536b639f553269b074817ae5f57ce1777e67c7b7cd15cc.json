{"ast": null, "code": "import { CHANNEL_EVENTS, CONNECTION_STATE, DEFAULT_HEADERS, DEFAULT_TIMEOUT, SOCKET_STATES, TRANSPORTS, VSN, WS_CLOSE_NORMAL } from './lib/constants';\nimport Serializer from './lib/serializer';\nimport Timer from './lib/timer';\nimport { httpEndpointURL } from './lib/transformers';\nimport RealtimeChannel from './RealtimeChannel';\nconst noop = () => {};\nconst NATIVE_WEBSOCKET_AVAILABLE = typeof WebSocket !== 'undefined';\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nexport default class RealtimeClient {\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket.\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers The optional headers to pass when connecting.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n   * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n   */\n  constructor(endPoint, options) {\n    var _a;\n    this.accessTokenValue = null;\n    this.apiKey = null;\n    this.channels = [];\n    this.endPoint = '';\n    this.httpEndpoint = '';\n    this.headers = DEFAULT_HEADERS;\n    this.params = {};\n    this.timeout = DEFAULT_TIMEOUT;\n    this.heartbeatIntervalMs = 30000;\n    this.heartbeatTimer = undefined;\n    this.pendingHeartbeatRef = null;\n    this.ref = 0;\n    this.logger = noop;\n    this.conn = null;\n    this.sendBuffer = [];\n    this.serializer = new Serializer();\n    this.stateChangeCallbacks = {\n      open: [],\n      close: [],\n      error: [],\n      message: []\n    };\n    this.accessToken = null;\n    /**\n     * Use either custom fetch, if provided, or default fetch to make HTTP requests\n     *\n     * @internal\n     */\n    this._resolveFetch = customFetch => {\n      let _fetch;\n      if (customFetch) {\n        _fetch = customFetch;\n      } else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({\n          default: fetch\n        }) => fetch(...args));\n      } else {\n        _fetch = fetch;\n      }\n      return (...args) => _fetch(...args);\n    };\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`;\n    this.httpEndpoint = httpEndpointURL(endPoint);\n    if (options === null || options === void 0 ? void 0 : options.transport) {\n      this.transport = options.transport;\n    } else {\n      this.transport = null;\n    }\n    if (options === null || options === void 0 ? void 0 : options.params) this.params = options.params;\n    if (options === null || options === void 0 ? void 0 : options.headers) this.headers = Object.assign(Object.assign({}, this.headers), options.headers);\n    if (options === null || options === void 0 ? void 0 : options.timeout) this.timeout = options.timeout;\n    if (options === null || options === void 0 ? void 0 : options.logger) this.logger = options.logger;\n    if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs) this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n    const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n    if (accessTokenValue) {\n      this.accessTokenValue = accessTokenValue;\n      this.apiKey = accessTokenValue;\n    }\n    this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs) ? options.reconnectAfterMs : tries => {\n      return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n    };\n    this.encode = (options === null || options === void 0 ? void 0 : options.encode) ? options.encode : (payload, callback) => {\n      return callback(JSON.stringify(payload));\n    };\n    this.decode = (options === null || options === void 0 ? void 0 : options.decode) ? options.decode : this.serializer.decode.bind(this.serializer);\n    this.reconnectTimer = new Timer(async () => {\n      this.disconnect();\n      this.connect();\n    }, this.reconnectAfterMs);\n    this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n    if (options === null || options === void 0 ? void 0 : options.worker) {\n      if (typeof window !== 'undefined' && !window.Worker) {\n        throw new Error('Web Worker is not supported');\n      }\n      this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n      this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n    }\n    this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n  }\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect() {\n    if (this.conn) {\n      return;\n    }\n    if (this.transport) {\n      this.conn = new this.transport(this.endpointURL(), undefined, {\n        headers: this.headers\n      });\n      return;\n    }\n    if (NATIVE_WEBSOCKET_AVAILABLE) {\n      this.conn = new WebSocket(this.endpointURL());\n      this.setupConnection();\n      return;\n    }\n    this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n      close: () => {\n        this.conn = null;\n      }\n    });\n    import('ws').then(({\n      default: WS\n    }) => {\n      this.conn = new WS(this.endpointURL(), undefined, {\n        headers: this.headers\n      });\n      this.setupConnection();\n    });\n  }\n  /**\n   * Returns the URL of the websocket.\n   * @returns string The URL of the websocket.\n   */\n  endpointURL() {\n    return this._appendParams(this.endPoint, Object.assign({}, this.params, {\n      vsn: VSN\n    }));\n  }\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code, reason) {\n    if (this.conn) {\n      this.conn.onclose = function () {}; // noop\n      if (code) {\n        this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n      } else {\n        this.conn.close();\n      }\n      this.conn = null;\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n      this.reconnectTimer.reset();\n    }\n  }\n  /**\n   * Returns all created channels\n   */\n  getChannels() {\n    return this.channels;\n  }\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  async removeChannel(channel) {\n    const status = await channel.unsubscribe();\n    if (this.channels.length === 0) {\n      this.disconnect();\n    }\n    return status;\n  }\n  /**\n   * Unsubscribes and removes all channels\n   */\n  async removeAllChannels() {\n    const values_1 = await Promise.all(this.channels.map(channel => channel.unsubscribe()));\n    this.disconnect();\n    return values_1;\n  }\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind, msg, data) {\n    this.logger(kind, msg, data);\n  }\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState() {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting;\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open;\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing;\n      default:\n        return CONNECTION_STATE.Closed;\n    }\n  }\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected() {\n    return this.connectionState() === CONNECTION_STATE.Open;\n  }\n  channel(topic, params = {\n    config: {}\n  }) {\n    const chan = new RealtimeChannel(`realtime:${topic}`, params, this);\n    this.channels.push(chan);\n    return chan;\n  }\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data) {\n    const {\n      topic,\n      event,\n      payload,\n      ref\n    } = data;\n    const callback = () => {\n      this.encode(data, result => {\n        var _a;\n        (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n      });\n    };\n    this.log('push', `${topic} ${event} (${ref})`, payload);\n    if (this.isConnected()) {\n      callback();\n    } else {\n      this.sendBuffer.push(callback);\n    }\n  }\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * If param is null it will use the `accessToken` callback function or the token set on the client.\n   *\n   * On callback used, it will set the value of the token internal to the client.\n   *\n   * @param token A JWT string to override the token set on the client.\n   */\n  async setAuth(token = null) {\n    let tokenToSend = token || this.accessToken && (await this.accessToken()) || this.accessTokenValue;\n    if (tokenToSend) {\n      let parsed = null;\n      try {\n        parsed = JSON.parse(atob(tokenToSend.split('.')[1]));\n      } catch (_error) {}\n      if (parsed && parsed.exp) {\n        let now = Math.floor(Date.now() / 1000);\n        let valid = now - parsed.exp < 0;\n        if (!valid) {\n          this.log('auth', `InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n          return Promise.reject(`InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n        }\n      }\n      this.accessTokenValue = tokenToSend;\n      this.channels.forEach(channel => {\n        tokenToSend && channel.updateJoinPayload({\n          access_token: tokenToSend\n        });\n        if (channel.joinedOnce && channel._isJoined()) {\n          channel._push(CHANNEL_EVENTS.access_token, {\n            access_token: tokenToSend\n          });\n        }\n      });\n    }\n  }\n  /**\n   * Sends a heartbeat message if the socket is connected.\n   */\n  async sendHeartbeat() {\n    var _a;\n    if (!this.isConnected()) {\n      return;\n    }\n    if (this.pendingHeartbeatRef) {\n      this.pendingHeartbeatRef = null;\n      this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n      (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(WS_CLOSE_NORMAL, 'hearbeat timeout');\n      return;\n    }\n    this.pendingHeartbeatRef = this._makeRef();\n    this.push({\n      topic: 'phoenix',\n      event: 'heartbeat',\n      payload: {},\n      ref: this.pendingHeartbeatRef\n    });\n    this.setAuth();\n  }\n  /**\n   * Flushes send buffer\n   */\n  flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach(callback => callback());\n      this.sendBuffer = [];\n    }\n  }\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef() {\n    let newRef = this.ref + 1;\n    if (newRef === this.ref) {\n      this.ref = 0;\n    } else {\n      this.ref = newRef;\n    }\n    return this.ref.toString();\n  }\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic) {\n    let dupChannel = this.channels.find(c => c.topic === topic && (c._isJoined() || c._isJoining()));\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`);\n      dupChannel.unsubscribe();\n    }\n  }\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel) {\n    this.channels = this.channels.filter(c => c._joinRef() !== channel._joinRef());\n  }\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  setupConnection() {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer';\n      this.conn.onopen = () => this._onConnOpen();\n      this.conn.onerror = error => this._onConnError(error);\n      this.conn.onmessage = event => this._onConnMessage(event);\n      this.conn.onclose = event => this._onConnClose(event);\n    }\n  }\n  /** @internal */\n  _onConnMessage(rawMessage) {\n    this.decode(rawMessage.data, msg => {\n      let {\n        topic,\n        event,\n        payload,\n        ref\n      } = msg;\n      if (ref && ref === this.pendingHeartbeatRef) {\n        this.pendingHeartbeatRef = null;\n      }\n      this.log('receive', `${payload.status || ''} ${topic} ${event} ${ref && '(' + ref + ')' || ''}`, payload);\n      this.channels.filter(channel => channel._isMember(topic)).forEach(channel => channel._trigger(event, payload, ref));\n      this.stateChangeCallbacks.message.forEach(callback => callback(msg));\n    });\n  }\n  /** @internal */\n  async _onConnOpen() {\n    this.log('transport', `connected to ${this.endpointURL()}`);\n    this.flushSendBuffer();\n    this.reconnectTimer.reset();\n    if (!this.worker) {\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n      this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n    } else {\n      if (this.workerUrl) {\n        this.log('worker', `starting worker for from ${this.workerUrl}`);\n      } else {\n        this.log('worker', `starting default worker`);\n      }\n      const objectUrl = this._workerObjectUrl(this.workerUrl);\n      this.workerRef = new Worker(objectUrl);\n      this.workerRef.onerror = error => {\n        this.log('worker', 'worker error', error.message);\n        this.workerRef.terminate();\n      };\n      this.workerRef.onmessage = event => {\n        if (event.data.event === 'keepAlive') {\n          this.sendHeartbeat();\n        }\n      };\n      this.workerRef.postMessage({\n        event: 'start',\n        interval: this.heartbeatIntervalMs\n      });\n    }\n    this.stateChangeCallbacks.open.forEach(callback => callback());\n  }\n  /** @internal */\n  _onConnClose(event) {\n    this.log('transport', 'close', event);\n    this._triggerChanError();\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n    this.reconnectTimer.scheduleTimeout();\n    this.stateChangeCallbacks.close.forEach(callback => callback(event));\n  }\n  /** @internal */\n  _onConnError(error) {\n    this.log('transport', error.message);\n    this._triggerChanError();\n    this.stateChangeCallbacks.error.forEach(callback => callback(error));\n  }\n  /** @internal */\n  _triggerChanError() {\n    this.channels.forEach(channel => channel._trigger(CHANNEL_EVENTS.error));\n  }\n  /** @internal */\n  _appendParams(url, params) {\n    if (Object.keys(params).length === 0) {\n      return url;\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?';\n    const query = new URLSearchParams(params);\n    return `${url}${prefix}${query}`;\n  }\n  _workerObjectUrl(url) {\n    let result_url;\n    if (url) {\n      result_url = url;\n    } else {\n      const blob = new Blob([WORKER_SCRIPT], {\n        type: 'application/javascript'\n      });\n      result_url = URL.createObjectURL(blob);\n    }\n    return result_url;\n  }\n}\nclass WSWebSocketDummy {\n  constructor(address, _protocols, options) {\n    this.binaryType = 'arraybuffer';\n    this.onclose = () => {};\n    this.onerror = () => {};\n    this.onmessage = () => {};\n    this.onopen = () => {};\n    this.readyState = SOCKET_STATES.connecting;\n    this.send = () => {};\n    this.url = null;\n    this.url = address;\n    this.close = options.close;\n  }\n}", "map": {"version": 3, "names": ["CHANNEL_EVENTS", "CONNECTION_STATE", "DEFAULT_HEADERS", "DEFAULT_TIMEOUT", "SOCKET_STATES", "TRANSPORTS", "VSN", "WS_CLOSE_NORMAL", "Serializer", "Timer", "httpEndpointURL", "RealtimeChannel", "noop", "NATIVE_WEBSOCKET_AVAILABLE", "WebSocket", "WORKER_SCRIPT", "RealtimeClient", "constructor", "endPoint", "options", "accessTokenValue", "<PERSON><PERSON><PERSON><PERSON>", "channels", "httpEndpoint", "headers", "params", "timeout", "heartbeatIntervalMs", "heartbeatTimer", "undefined", "pendingHeartbeatRef", "ref", "logger", "conn", "send<PERSON><PERSON><PERSON>", "serializer", "stateChangeCallbacks", "open", "close", "error", "message", "accessToken", "_resolveFetch", "customFetch", "_fetch", "fetch", "args", "then", "default", "websocket", "transport", "Object", "assign", "_a", "apikey", "reconnectAfterMs", "tries", "encode", "payload", "callback", "JSON", "stringify", "decode", "bind", "reconnectTimer", "disconnect", "connect", "worker", "window", "Worker", "Error", "workerUrl", "endpointURL", "setupConnection", "WSWebSocketDummy", "WS", "_appendParams", "vsn", "code", "reason", "onclose", "clearInterval", "reset", "getChannels", "removeChannel", "channel", "status", "unsubscribe", "length", "removeAllChannels", "values_1", "Promise", "all", "map", "log", "kind", "msg", "data", "connectionState", "readyState", "connecting", "Connecting", "Open", "closing", "Closing", "Closed", "isConnected", "topic", "config", "chan", "push", "event", "result", "send", "setAuth", "token", "tokenToSend", "parsed", "parse", "atob", "split", "_error", "exp", "now", "Math", "floor", "Date", "valid", "reject", "for<PERSON>ach", "updateJoinPayload", "access_token", "joinedOnce", "_isJoined", "_push", "sendHeartbeat", "_makeRef", "flushSendBuffer", "newRef", "toString", "_leaveOpenTopic", "dup<PERSON><PERSON><PERSON>", "find", "c", "_isJoining", "_remove", "filter", "_joinRef", "binaryType", "onopen", "_onConnOpen", "onerror", "_onConnError", "onmessage", "_onConnMessage", "_onConnClose", "rawMessage", "_isMember", "_trigger", "setInterval", "objectUrl", "_workerObjectUrl", "workerRef", "terminate", "postMessage", "interval", "_trigger<PERSON>hanError", "scheduleTimeout", "url", "keys", "prefix", "match", "query", "URLSearchParams", "result_url", "blob", "Blob", "type", "URL", "createObjectURL", "address", "_protocols"], "sources": ["C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@supabase\\realtime-js\\src\\RealtimeClient.ts"], "sourcesContent": ["import type { WebSocket as WSWebSocket } from 'ws'\n\nimport {\n  CHANNEL_EVENTS,\n  CONNECTION_STATE,\n  DEFAULT_HEADERS,\n  DEFAULT_TIMEOUT,\n  SOCKET_STATES,\n  TRANSPORTS,\n  VSN,\n  WS_CLOSE_NORMAL,\n} from './lib/constants'\nimport Serializer from './lib/serializer'\nimport Timer from './lib/timer'\n\nimport { httpEndpointURL } from './lib/transformers'\nimport RealtimeChannel from './RealtimeChannel'\nimport type { RealtimeChannelOptions } from './RealtimeChannel'\n\ntype Fetch = typeof fetch\n\nexport type Channel = {\n  name: string\n  inserted_at: string\n  updated_at: string\n  id: number\n}\n\nexport type RealtimeClientOptions = {\n  transport?: WebSocketLikeConstructor\n  timeout?: number\n  heartbeatIntervalMs?: number\n  logger?: Function\n  encode?: Function\n  decode?: Function\n  reconnectAfterMs?: Function\n  headers?: { [key: string]: string }\n  params?: { [key: string]: any }\n  log_level?: 'info' | 'debug' | 'warn' | 'error'\n  fetch?: Fetch\n  worker?: boolean\n  workerUrl?: string\n  accessToken?: () => Promise<string | null>\n}\n\nexport type RealtimeMessage = {\n  topic: string\n  event: string\n  payload: any\n  ref: string\n  join_ref?: string\n}\n\nexport type RealtimeRemoveChannelResponse = 'ok' | 'timed out' | 'error'\n\nconst noop = () => {}\n\nexport interface WebSocketLikeConstructor {\n  new (\n    address: string | URL,\n    _ignored?: any,\n    options?: { headers: Object | undefined }\n  ): WebSocketLike\n}\n\nexport type WebSocketLike = WebSocket | WSWebSocket | WSWebSocketDummy\n\nexport interface WebSocketLikeError {\n  error: any\n  message: string\n  type: string\n}\n\nconst NATIVE_WEBSOCKET_AVAILABLE = typeof WebSocket !== 'undefined'\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`\nexport default class RealtimeClient {\n  accessTokenValue: string | null = null\n  apiKey: string | null = null\n  channels: RealtimeChannel[] = []\n  endPoint: string = ''\n  httpEndpoint: string = ''\n  headers?: { [key: string]: string } = DEFAULT_HEADERS\n  params?: { [key: string]: string } = {}\n  timeout: number = DEFAULT_TIMEOUT\n  transport: WebSocketLikeConstructor | null\n  heartbeatIntervalMs: number = 30000\n  heartbeatTimer: ReturnType<typeof setInterval> | undefined = undefined\n  pendingHeartbeatRef: string | null = null\n  ref: number = 0\n  reconnectTimer: Timer\n  logger: Function = noop\n  encode: Function\n  decode: Function\n  reconnectAfterMs: Function\n  conn: WebSocketLike | null = null\n  sendBuffer: Function[] = []\n  serializer: Serializer = new Serializer()\n  stateChangeCallbacks: {\n    open: Function[]\n    close: Function[]\n    error: Function[]\n    message: Function[]\n  } = {\n    open: [],\n    close: [],\n    error: [],\n    message: [],\n  }\n  fetch: Fetch\n  accessToken: (() => Promise<string | null>) | null = null\n  worker?: boolean\n  workerUrl?: string\n  workerRef?: Worker\n\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket.\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers The optional headers to pass when connecting.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n   * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n   */\n  constructor(endPoint: string, options?: RealtimeClientOptions) {\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`\n    this.httpEndpoint = httpEndpointURL(endPoint)\n    if (options?.transport) {\n      this.transport = options.transport\n    } else {\n      this.transport = null\n    }\n    if (options?.params) this.params = options.params\n    if (options?.headers) this.headers = { ...this.headers, ...options.headers }\n    if (options?.timeout) this.timeout = options.timeout\n    if (options?.logger) this.logger = options.logger\n    if (options?.heartbeatIntervalMs)\n      this.heartbeatIntervalMs = options.heartbeatIntervalMs\n\n    const accessTokenValue = options?.params?.apikey\n    if (accessTokenValue) {\n      this.accessTokenValue = accessTokenValue\n      this.apiKey = accessTokenValue\n    }\n\n    this.reconnectAfterMs = options?.reconnectAfterMs\n      ? options.reconnectAfterMs\n      : (tries: number) => {\n          return [1000, 2000, 5000, 10000][tries - 1] || 10000\n        }\n    this.encode = options?.encode\n      ? options.encode\n      : (payload: JSON, callback: Function) => {\n          return callback(JSON.stringify(payload))\n        }\n    this.decode = options?.decode\n      ? options.decode\n      : this.serializer.decode.bind(this.serializer)\n    this.reconnectTimer = new Timer(async () => {\n      this.disconnect()\n      this.connect()\n    }, this.reconnectAfterMs)\n\n    this.fetch = this._resolveFetch(options?.fetch)\n    if (options?.worker) {\n      if (typeof window !== 'undefined' && !window.Worker) {\n        throw new Error('Web Worker is not supported')\n      }\n      this.worker = options?.worker || false\n      this.workerUrl = options?.workerUrl\n    }\n    this.accessToken = options?.accessToken || null\n  }\n\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect(): void {\n    if (this.conn) {\n      return\n    }\n\n    if (this.transport) {\n      this.conn = new this.transport(this.endpointURL(), undefined, {\n        headers: this.headers,\n      })\n      return\n    }\n\n    if (NATIVE_WEBSOCKET_AVAILABLE) {\n      this.conn = new WebSocket(this.endpointURL())\n      this.setupConnection()\n      return\n    }\n\n    this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n      close: () => {\n        this.conn = null\n      },\n    })\n\n    import('ws').then(({ default: WS }) => {\n      this.conn = new WS(this.endpointURL(), undefined, {\n        headers: this.headers,\n      })\n      this.setupConnection()\n    })\n  }\n\n  /**\n   * Returns the URL of the websocket.\n   * @returns string The URL of the websocket.\n   */\n  endpointURL(): string {\n    return this._appendParams(\n      this.endPoint,\n      Object.assign({}, this.params, { vsn: VSN })\n    )\n  }\n\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code?: number, reason?: string): void {\n    if (this.conn) {\n      this.conn.onclose = function () {} // noop\n      if (code) {\n        this.conn.close(code, reason ?? '')\n      } else {\n        this.conn.close()\n      }\n      this.conn = null\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n      this.reconnectTimer.reset()\n    }\n  }\n\n  /**\n   * Returns all created channels\n   */\n  getChannels(): RealtimeChannel[] {\n    return this.channels\n  }\n\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  async removeChannel(\n    channel: RealtimeChannel\n  ): Promise<RealtimeRemoveChannelResponse> {\n    const status = await channel.unsubscribe()\n    if (this.channels.length === 0) {\n      this.disconnect()\n    }\n    return status\n  }\n\n  /**\n   * Unsubscribes and removes all channels\n   */\n  async removeAllChannels(): Promise<RealtimeRemoveChannelResponse[]> {\n    const values_1 = await Promise.all(\n      this.channels.map((channel) => channel.unsubscribe())\n    )\n    this.disconnect()\n    return values_1\n  }\n\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind: string, msg: string, data?: any) {\n    this.logger(kind, msg, data)\n  }\n\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState(): CONNECTION_STATE {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing\n      default:\n        return CONNECTION_STATE.Closed\n    }\n  }\n\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected(): boolean {\n    return this.connectionState() === CONNECTION_STATE.Open\n  }\n\n  channel(\n    topic: string,\n    params: RealtimeChannelOptions = { config: {} }\n  ): RealtimeChannel {\n    const chan = new RealtimeChannel(`realtime:${topic}`, params, this)\n    this.channels.push(chan)\n    return chan\n  }\n\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data: RealtimeMessage): void {\n    const { topic, event, payload, ref } = data\n    const callback = () => {\n      this.encode(data, (result: any) => {\n        this.conn?.send(result)\n      })\n    }\n    this.log('push', `${topic} ${event} (${ref})`, payload)\n    if (this.isConnected()) {\n      callback()\n    } else {\n      this.sendBuffer.push(callback)\n    }\n  }\n\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * If param is null it will use the `accessToken` callback function or the token set on the client.\n   *\n   * On callback used, it will set the value of the token internal to the client.\n   *\n   * @param token A JWT string to override the token set on the client.\n   */\n  async setAuth(token: string | null = null): Promise<void> {\n    let tokenToSend =\n      token ||\n      (this.accessToken && (await this.accessToken())) ||\n      this.accessTokenValue\n\n    if (tokenToSend) {\n      let parsed = null\n      try {\n        parsed = JSON.parse(atob(tokenToSend.split('.')[1]))\n      } catch (_error) {}\n      if (parsed && parsed.exp) {\n        let now = Math.floor(Date.now() / 1000)\n        let valid = now - parsed.exp < 0\n        if (!valid) {\n          this.log(\n            'auth',\n            `InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`\n          )\n          return Promise.reject(\n            `InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`\n          )\n        }\n      }\n\n      this.accessTokenValue = tokenToSend\n      this.channels.forEach((channel) => {\n        tokenToSend && channel.updateJoinPayload({ access_token: tokenToSend })\n\n        if (channel.joinedOnce && channel._isJoined()) {\n          channel._push(CHANNEL_EVENTS.access_token, {\n            access_token: tokenToSend,\n          })\n        }\n      })\n    }\n  }\n  /**\n   * Sends a heartbeat message if the socket is connected.\n   */\n  async sendHeartbeat() {\n    if (!this.isConnected()) {\n      return\n    }\n    if (this.pendingHeartbeatRef) {\n      this.pendingHeartbeatRef = null\n      this.log(\n        'transport',\n        'heartbeat timeout. Attempting to re-establish connection'\n      )\n      this.conn?.close(WS_CLOSE_NORMAL, 'hearbeat timeout')\n      return\n    }\n    this.pendingHeartbeatRef = this._makeRef()\n    this.push({\n      topic: 'phoenix',\n      event: 'heartbeat',\n      payload: {},\n      ref: this.pendingHeartbeatRef,\n    })\n    this.setAuth()\n  }\n\n  /**\n   * Flushes send buffer\n   */\n  flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach((callback) => callback())\n      this.sendBuffer = []\n    }\n  }\n\n  /**\n   * Use either custom fetch, if provided, or default fetch to make HTTP requests\n   *\n   * @internal\n   */\n  _resolveFetch = (customFetch?: Fetch): Fetch => {\n    let _fetch: Fetch\n    if (customFetch) {\n      _fetch = customFetch\n    } else if (typeof fetch === 'undefined') {\n      _fetch = (...args) =>\n        import('@supabase/node-fetch' as any).then(({ default: fetch }) =>\n          fetch(...args)\n        )\n    } else {\n      _fetch = fetch\n    }\n    return (...args) => _fetch(...args)\n  }\n\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef(): string {\n    let newRef = this.ref + 1\n    if (newRef === this.ref) {\n      this.ref = 0\n    } else {\n      this.ref = newRef\n    }\n\n    return this.ref.toString()\n  }\n\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic: string): void {\n    let dupChannel = this.channels.find(\n      (c) => c.topic === topic && (c._isJoined() || c._isJoining())\n    )\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`)\n      dupChannel.unsubscribe()\n    }\n  }\n\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel: RealtimeChannel) {\n    this.channels = this.channels.filter(\n      (c: RealtimeChannel) => c._joinRef() !== channel._joinRef()\n    )\n  }\n\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  private setupConnection(): void {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer'\n      this.conn.onopen = () => this._onConnOpen()\n      this.conn.onerror = (error: WebSocketLikeError) =>\n        this._onConnError(error as WebSocketLikeError)\n      this.conn.onmessage = (event: any) => this._onConnMessage(event)\n      this.conn.onclose = (event: any) => this._onConnClose(event)\n    }\n  }\n\n  /** @internal */\n  private _onConnMessage(rawMessage: { data: any }) {\n    this.decode(rawMessage.data, (msg: RealtimeMessage) => {\n      let { topic, event, payload, ref } = msg\n\n      if (ref && ref === this.pendingHeartbeatRef) {\n        this.pendingHeartbeatRef = null\n      }\n\n      this.log(\n        'receive',\n        `${payload.status || ''} ${topic} ${event} ${\n          (ref && '(' + ref + ')') || ''\n        }`,\n        payload\n      )\n      this.channels\n        .filter((channel: RealtimeChannel) => channel._isMember(topic))\n        .forEach((channel: RealtimeChannel) =>\n          channel._trigger(event, payload, ref)\n        )\n      this.stateChangeCallbacks.message.forEach((callback) => callback(msg))\n    })\n  }\n\n  /** @internal */\n  private async _onConnOpen() {\n    this.log('transport', `connected to ${this.endpointURL()}`)\n    this.flushSendBuffer()\n    this.reconnectTimer.reset()\n    if (!this.worker) {\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n      this.heartbeatTimer = setInterval(\n        () => this.sendHeartbeat(),\n        this.heartbeatIntervalMs\n      )\n    } else {\n      if (this.workerUrl) {\n        this.log('worker', `starting worker for from ${this.workerUrl}`)\n      } else {\n        this.log('worker', `starting default worker`)\n      }\n\n      const objectUrl = this._workerObjectUrl(this.workerUrl!)\n      this.workerRef = new Worker(objectUrl)\n      this.workerRef.onerror = (error) => {\n        this.log('worker', 'worker error', error.message)\n        this.workerRef!.terminate()\n      }\n      this.workerRef.onmessage = (event) => {\n        if (event.data.event === 'keepAlive') {\n          this.sendHeartbeat()\n        }\n      }\n      this.workerRef.postMessage({\n        event: 'start',\n        interval: this.heartbeatIntervalMs,\n      })\n    }\n\n    this.stateChangeCallbacks.open.forEach((callback) => callback())!\n  }\n\n  /** @internal */\n\n  private _onConnClose(event: any) {\n    this.log('transport', 'close', event)\n    this._triggerChanError()\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n    this.reconnectTimer.scheduleTimeout()\n    this.stateChangeCallbacks.close.forEach((callback) => callback(event))\n  }\n\n  /** @internal */\n  private _onConnError(error: WebSocketLikeError) {\n    this.log('transport', error.message)\n    this._triggerChanError()\n    this.stateChangeCallbacks.error.forEach((callback) => callback(error))\n  }\n\n  /** @internal */\n  private _triggerChanError() {\n    this.channels.forEach((channel: RealtimeChannel) =>\n      channel._trigger(CHANNEL_EVENTS.error)\n    )\n  }\n\n  /** @internal */\n  private _appendParams(\n    url: string,\n    params: { [key: string]: string }\n  ): string {\n    if (Object.keys(params).length === 0) {\n      return url\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?'\n    const query = new URLSearchParams(params)\n\n    return `${url}${prefix}${query}`\n  }\n\n  private _workerObjectUrl(url: string | undefined): string {\n    let result_url: string\n    if (url) {\n      result_url = url\n    } else {\n      const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' })\n      result_url = URL.createObjectURL(blob)\n    }\n    return result_url\n  }\n}\n\nclass WSWebSocketDummy {\n  binaryType: string = 'arraybuffer'\n  close: Function\n  onclose: Function = () => {}\n  onerror: Function = () => {}\n  onmessage: Function = () => {}\n  onopen: Function = () => {}\n  readyState: number = SOCKET_STATES.connecting\n  send: Function = () => {}\n  url: string | URL | null = null\n\n  constructor(\n    address: string,\n    _protocols: undefined,\n    options: { close: Function }\n  ) {\n    this.url = address\n    this.close = options.close\n  }\n}\n"], "mappings": "AAEA,SACEA,cAAc,EACdC,gBAAgB,EAChBC,eAAe,EACfC,eAAe,EACfC,aAAa,EACbC,UAAU,EACVC,GAAG,EACHC,eAAe,QACV,iBAAiB;AACxB,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,KAAK,MAAM,aAAa;AAE/B,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,eAAe,MAAM,mBAAmB;AAuC/C,MAAMC,IAAI,GAAGA,CAAA,KAAK,CAAE,CAAC;AAkBrB,MAAMC,0BAA0B,GAAG,OAAOC,SAAS,KAAK,WAAW;AACnE,MAAMC,aAAa,GAAG;;;;;MAKhB;AACN,eAAc,MAAOC,cAAc;EAuCjC;;;;;;;;;;;;;;;;;EAiBAC,YAAYC,QAAgB,EAAEC,OAA+B;;IAvD7D,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,MAAM,GAAkB,IAAI;IAC5B,KAAAC,QAAQ,GAAsB,EAAE;IAChC,KAAAJ,QAAQ,GAAW,EAAE;IACrB,KAAAK,YAAY,GAAW,EAAE;IACzB,KAAAC,OAAO,GAA+BtB,eAAe;IACrD,KAAAuB,MAAM,GAA+B,EAAE;IACvC,KAAAC,OAAO,GAAWvB,eAAe;IAEjC,KAAAwB,mBAAmB,GAAW,KAAK;IACnC,KAAAC,cAAc,GAA+CC,SAAS;IACtE,KAAAC,mBAAmB,GAAkB,IAAI;IACzC,KAAAC,GAAG,GAAW,CAAC;IAEf,KAAAC,MAAM,GAAapB,IAAI;IAIvB,KAAAqB,IAAI,GAAyB,IAAI;IACjC,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,UAAU,GAAe,IAAI3B,UAAU,EAAE;IACzC,KAAA4B,oBAAoB,GAKhB;MACFC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;KACV;IAED,KAAAC,WAAW,GAA0C,IAAI;IA0TzD;;;;;IAKA,KAAAC,aAAa,GAAIC,WAAmB,IAAW;MAC7C,IAAIC,MAAa;MACjB,IAAID,WAAW,EAAE;QACfC,MAAM,GAAGD,WAAW;OACrB,MAAM,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;QACvCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KACf,MAAM,CAAC,sBAA6B,CAAC,CAACC,IAAI,CAAC,CAAC;UAAEC,OAAO,EAAEH;QAAK,CAAE,KAC5DA,KAAK,CAAC,GAAGC,IAAI,CAAC,CACf;OACJ,MAAM;QACLF,MAAM,GAAGC,KAAK;;MAEhB,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;IACrC,CAAC;IArTC,IAAI,CAAC5B,QAAQ,GAAG,GAAGA,QAAQ,IAAIb,UAAU,CAAC4C,SAAS,EAAE;IACrD,IAAI,CAAC1B,YAAY,GAAGb,eAAe,CAACQ,QAAQ,CAAC;IAC7C,IAAIC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+B,SAAS,EAAE;MACtB,IAAI,CAACA,SAAS,GAAG/B,OAAO,CAAC+B,SAAS;KACnC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI;;IAEvB,IAAI/B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGN,OAAO,CAACM,MAAM;IACjD,IAAIN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,OAAO,EAAE,IAAI,CAACA,OAAO,GAAA2B,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAAC5B,OAAO,GAAKL,OAAO,CAACK,OAAO,CAAE;IAC5E,IAAIL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,OAAO,EAAE,IAAI,CAACA,OAAO,GAAGP,OAAO,CAACO,OAAO;IACpD,IAAIP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGb,OAAO,CAACa,MAAM;IACjD,IAAIb,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,mBAAmB,EAC9B,IAAI,CAACA,mBAAmB,GAAGR,OAAO,CAACQ,mBAAmB;IAExD,MAAMP,gBAAgB,GAAG,CAAAiC,EAAA,GAAAlC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,MAAM,cAAA4B,EAAA,uBAAAA,EAAA,CAAEC,MAAM;IAChD,IAAIlC,gBAAgB,EAAE;MACpB,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,MAAM,GAAGD,gBAAgB;;IAGhC,IAAI,CAACmC,gBAAgB,GAAG,CAAApC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoC,gBAAgB,IAC7CpC,OAAO,CAACoC,gBAAgB,GACvBC,KAAa,IAAI;MAChB,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAACA,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK;IACtD,CAAC;IACL,IAAI,CAACC,MAAM,GAAG,CAAAtC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,MAAM,IACzBtC,OAAO,CAACsC,MAAM,GACd,CAACC,OAAa,EAAEC,QAAkB,KAAI;MACpC,OAAOA,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC,CAAC;IAC1C,CAAC;IACL,IAAI,CAACI,MAAM,GAAG,CAAA3C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2C,MAAM,IACzB3C,OAAO,CAAC2C,MAAM,GACd,IAAI,CAAC3B,UAAU,CAAC2B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5B,UAAU,CAAC;IAChD,IAAI,CAAC6B,cAAc,GAAG,IAAIvD,KAAK,CAAC,YAAW;MACzC,IAAI,CAACwD,UAAU,EAAE;MACjB,IAAI,CAACC,OAAO,EAAE;IAChB,CAAC,EAAE,IAAI,CAACX,gBAAgB,CAAC;IAEzB,IAAI,CAACV,KAAK,GAAG,IAAI,CAACH,aAAa,CAACvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0B,KAAK,CAAC;IAC/C,IAAI1B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgD,MAAM,EAAE;MACnB,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;QACnD,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;;MAEhD,IAAI,CAACH,MAAM,GAAG,CAAAhD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgD,MAAM,KAAI,KAAK;MACtC,IAAI,CAACI,SAAS,GAAGpD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoD,SAAS;;IAErC,IAAI,CAAC9B,WAAW,GAAG,CAAAtB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsB,WAAW,KAAI,IAAI;EACjD;EAEA;;;EAGAyB,OAAOA,CAAA;IACL,IAAI,IAAI,CAACjC,IAAI,EAAE;MACb;;IAGF,IAAI,IAAI,CAACiB,SAAS,EAAE;MAClB,IAAI,CAACjB,IAAI,GAAG,IAAI,IAAI,CAACiB,SAAS,CAAC,IAAI,CAACsB,WAAW,EAAE,EAAE3C,SAAS,EAAE;QAC5DL,OAAO,EAAE,IAAI,CAACA;OACf,CAAC;MACF;;IAGF,IAAIX,0BAA0B,EAAE;MAC9B,IAAI,CAACoB,IAAI,GAAG,IAAInB,SAAS,CAAC,IAAI,CAAC0D,WAAW,EAAE,CAAC;MAC7C,IAAI,CAACC,eAAe,EAAE;MACtB;;IAGF,IAAI,CAACxC,IAAI,GAAG,IAAIyC,gBAAgB,CAAC,IAAI,CAACF,WAAW,EAAE,EAAE3C,SAAS,EAAE;MAC9DS,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACL,IAAI,GAAG,IAAI;MAClB;KACD,CAAC;IAEF,MAAM,CAAC,IAAI,CAAC,CAACc,IAAI,CAAC,CAAC;MAAEC,OAAO,EAAE2B;IAAE,CAAE,KAAI;MACpC,IAAI,CAAC1C,IAAI,GAAG,IAAI0C,EAAE,CAAC,IAAI,CAACH,WAAW,EAAE,EAAE3C,SAAS,EAAE;QAChDL,OAAO,EAAE,IAAI,CAACA;OACf,CAAC;MACF,IAAI,CAACiD,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEA;;;;EAIAD,WAAWA,CAAA;IACT,OAAO,IAAI,CAACI,aAAa,CACvB,IAAI,CAAC1D,QAAQ,EACbiC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC3B,MAAM,EAAE;MAAEoD,GAAG,EAAEvE;IAAG,CAAE,CAAC,CAC7C;EACH;EAEA;;;;;;EAMA2D,UAAUA,CAACa,IAAa,EAAEC,MAAe;IACvC,IAAI,IAAI,CAAC9C,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAC+C,OAAO,GAAG,aAAa,CAAC,EAAC;MACnC,IAAIF,IAAI,EAAE;QACR,IAAI,CAAC7C,IAAI,CAACK,KAAK,CAACwC,IAAI,EAAEC,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,EAAE,CAAC;OACpC,MAAM;QACL,IAAI,CAAC9C,IAAI,CAACK,KAAK,EAAE;;MAEnB,IAAI,CAACL,IAAI,GAAG,IAAI;MAChB;MACA,IAAI,CAACL,cAAc,IAAIqD,aAAa,CAAC,IAAI,CAACrD,cAAc,CAAC;MACzD,IAAI,CAACoC,cAAc,CAACkB,KAAK,EAAE;;EAE/B;EAEA;;;EAGAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC7D,QAAQ;EACtB;EAEA;;;;EAIA,MAAM8D,aAAaA,CACjBC,OAAwB;IAExB,MAAMC,MAAM,GAAG,MAAMD,OAAO,CAACE,WAAW,EAAE;IAC1C,IAAI,IAAI,CAACjE,QAAQ,CAACkE,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACvB,UAAU,EAAE;;IAEnB,OAAOqB,MAAM;EACf;EAEA;;;EAGA,MAAMG,iBAAiBA,CAAA;IACrB,MAAMC,QAAQ,GAAG,MAAMC,OAAO,CAACC,GAAG,CAChC,IAAI,CAACtE,QAAQ,CAACuE,GAAG,CAAER,OAAO,IAAKA,OAAO,CAACE,WAAW,EAAE,CAAC,CACtD;IACD,IAAI,CAACtB,UAAU,EAAE;IACjB,OAAOyB,QAAQ;EACjB;EAEA;;;;;EAKAI,GAAGA,CAACC,IAAY,EAAEC,GAAW,EAAEC,IAAU;IACvC,IAAI,CAACjE,MAAM,CAAC+D,IAAI,EAAEC,GAAG,EAAEC,IAAI,CAAC;EAC9B;EAEA;;;EAGAC,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACjE,IAAI,IAAI,IAAI,CAACA,IAAI,CAACkE,UAAU;MACvC,KAAK/F,aAAa,CAACgG,UAAU;QAC3B,OAAOnG,gBAAgB,CAACoG,UAAU;MACpC,KAAKjG,aAAa,CAACiC,IAAI;QACrB,OAAOpC,gBAAgB,CAACqG,IAAI;MAC9B,KAAKlG,aAAa,CAACmG,OAAO;QACxB,OAAOtG,gBAAgB,CAACuG,OAAO;MACjC;QACE,OAAOvG,gBAAgB,CAACwG,MAAM;;EAEpC;EAEA;;;EAGAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACR,eAAe,EAAE,KAAKjG,gBAAgB,CAACqG,IAAI;EACzD;EAEAjB,OAAOA,CACLsB,KAAa,EACblF,MAAA,GAAiC;IAAEmF,MAAM,EAAE;EAAE,CAAE;IAE/C,MAAMC,IAAI,GAAG,IAAIlG,eAAe,CAAC,YAAYgG,KAAK,EAAE,EAAElF,MAAM,EAAE,IAAI,CAAC;IACnE,IAAI,CAACH,QAAQ,CAACwF,IAAI,CAACD,IAAI,CAAC;IACxB,OAAOA,IAAI;EACb;EAEA;;;;;EAKAC,IAAIA,CAACb,IAAqB;IACxB,MAAM;MAAEU,KAAK;MAAEI,KAAK;MAAErD,OAAO;MAAE3B;IAAG,CAAE,GAAGkE,IAAI;IAC3C,MAAMtC,QAAQ,GAAGA,CAAA,KAAK;MACpB,IAAI,CAACF,MAAM,CAACwC,IAAI,EAAGe,MAAW,IAAI;;QAChC,CAAA3D,EAAA,OAAI,CAACpB,IAAI,cAAAoB,EAAA,uBAAAA,EAAA,CAAE4D,IAAI,CAACD,MAAM,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAAClB,GAAG,CAAC,MAAM,EAAE,GAAGa,KAAK,IAAII,KAAK,KAAKhF,GAAG,GAAG,EAAE2B,OAAO,CAAC;IACvD,IAAI,IAAI,CAACgD,WAAW,EAAE,EAAE;MACtB/C,QAAQ,EAAE;KACX,MAAM;MACL,IAAI,CAACzB,UAAU,CAAC4E,IAAI,CAACnD,QAAQ,CAAC;;EAElC;EAEA;;;;;;;;;EASA,MAAMuD,OAAOA,CAACC,KAAA,GAAuB,IAAI;IACvC,IAAIC,WAAW,GACbD,KAAK,IACJ,IAAI,CAAC1E,WAAW,KAAK,MAAM,IAAI,CAACA,WAAW,EAAE,CAAE,IAChD,IAAI,CAACrB,gBAAgB;IAEvB,IAAIgG,WAAW,EAAE;MACf,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAI;QACFA,MAAM,GAAGzD,IAAI,CAAC0D,KAAK,CAACC,IAAI,CAACH,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACrD,CAAC,OAAOC,MAAM,EAAE;MACjB,IAAIJ,MAAM,IAAIA,MAAM,CAACK,GAAG,EAAE;QACxB,IAAIC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACH,GAAG,EAAE,GAAG,IAAI,CAAC;QACvC,IAAII,KAAK,GAAGJ,GAAG,GAAGN,MAAM,CAACK,GAAG,GAAG,CAAC;QAChC,IAAI,CAACK,KAAK,EAAE;UACV,IAAI,CAACjC,GAAG,CACN,MAAM,EACN,iEAAiEuB,MAAM,CAACK,GAAG,EAAE,CAC9E;UACD,OAAO/B,OAAO,CAACqC,MAAM,CACnB,iEAAiEX,MAAM,CAACK,GAAG,EAAE,CAC9E;;;MAIL,IAAI,CAACtG,gBAAgB,GAAGgG,WAAW;MACnC,IAAI,CAAC9F,QAAQ,CAAC2G,OAAO,CAAE5C,OAAO,IAAI;QAChC+B,WAAW,IAAI/B,OAAO,CAAC6C,iBAAiB,CAAC;UAAEC,YAAY,EAAEf;QAAW,CAAE,CAAC;QAEvE,IAAI/B,OAAO,CAAC+C,UAAU,IAAI/C,OAAO,CAACgD,SAAS,EAAE,EAAE;UAC7ChD,OAAO,CAACiD,KAAK,CAACtI,cAAc,CAACmI,YAAY,EAAE;YACzCA,YAAY,EAAEf;WACf,CAAC;;MAEN,CAAC,CAAC;;EAEN;EACA;;;EAGA,MAAMmB,aAAaA,CAAA;;IACjB,IAAI,CAAC,IAAI,CAAC7B,WAAW,EAAE,EAAE;MACvB;;IAEF,IAAI,IAAI,CAAC5E,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACgE,GAAG,CACN,WAAW,EACX,0DAA0D,CAC3D;MACD,CAAAzC,EAAA,OAAI,CAACpB,IAAI,cAAAoB,EAAA,uBAAAA,EAAA,CAAEf,KAAK,CAAC/B,eAAe,EAAE,kBAAkB,CAAC;MACrD;;IAEF,IAAI,CAACuB,mBAAmB,GAAG,IAAI,CAAC0G,QAAQ,EAAE;IAC1C,IAAI,CAAC1B,IAAI,CAAC;MACRH,KAAK,EAAE,SAAS;MAChBI,KAAK,EAAE,WAAW;MAClBrD,OAAO,EAAE,EAAE;MACX3B,GAAG,EAAE,IAAI,CAACD;KACX,CAAC;IACF,IAAI,CAACoF,OAAO,EAAE;EAChB;EAEA;;;EAGAuB,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC/B,WAAW,EAAE,IAAI,IAAI,CAACxE,UAAU,CAACsD,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAACtD,UAAU,CAAC+F,OAAO,CAAEtE,QAAQ,IAAKA,QAAQ,EAAE,CAAC;MACjD,IAAI,CAACzB,UAAU,GAAG,EAAE;;EAExB;EAsBA;;;;;EAKAsG,QAAQA,CAAA;IACN,IAAIE,MAAM,GAAG,IAAI,CAAC3G,GAAG,GAAG,CAAC;IACzB,IAAI2G,MAAM,KAAK,IAAI,CAAC3G,GAAG,EAAE;MACvB,IAAI,CAACA,GAAG,GAAG,CAAC;KACb,MAAM;MACL,IAAI,CAACA,GAAG,GAAG2G,MAAM;;IAGnB,OAAO,IAAI,CAAC3G,GAAG,CAAC4G,QAAQ,EAAE;EAC5B;EAEA;;;;;EAKAC,eAAeA,CAACjC,KAAa;IAC3B,IAAIkC,UAAU,GAAG,IAAI,CAACvH,QAAQ,CAACwH,IAAI,CAChCC,CAAC,IAAKA,CAAC,CAACpC,KAAK,KAAKA,KAAK,KAAKoC,CAAC,CAACV,SAAS,EAAE,IAAIU,CAAC,CAACC,UAAU,EAAE,CAAC,CAC9D;IACD,IAAIH,UAAU,EAAE;MACd,IAAI,CAAC/C,GAAG,CAAC,WAAW,EAAE,4BAA4Ba,KAAK,GAAG,CAAC;MAC3DkC,UAAU,CAACtD,WAAW,EAAE;;EAE5B;EAEA;;;;;;;EAOA0D,OAAOA,CAAC5D,OAAwB;IAC9B,IAAI,CAAC/D,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4H,MAAM,CACjCH,CAAkB,IAAKA,CAAC,CAACI,QAAQ,EAAE,KAAK9D,OAAO,CAAC8D,QAAQ,EAAE,CAC5D;EACH;EAEA;;;;;EAKQ1E,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACxC,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACmH,UAAU,GAAG,aAAa;MACpC,IAAI,CAACnH,IAAI,CAACoH,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,EAAE;MAC3C,IAAI,CAACrH,IAAI,CAACsH,OAAO,GAAIhH,KAAyB,IAC5C,IAAI,CAACiH,YAAY,CAACjH,KAA2B,CAAC;MAChD,IAAI,CAACN,IAAI,CAACwH,SAAS,GAAI1C,KAAU,IAAK,IAAI,CAAC2C,cAAc,CAAC3C,KAAK,CAAC;MAChE,IAAI,CAAC9E,IAAI,CAAC+C,OAAO,GAAI+B,KAAU,IAAK,IAAI,CAAC4C,YAAY,CAAC5C,KAAK,CAAC;;EAEhE;EAEA;EACQ2C,cAAcA,CAACE,UAAyB;IAC9C,IAAI,CAAC9F,MAAM,CAAC8F,UAAU,CAAC3D,IAAI,EAAGD,GAAoB,IAAI;MACpD,IAAI;QAAEW,KAAK;QAAEI,KAAK;QAAErD,OAAO;QAAE3B;MAAG,CAAE,GAAGiE,GAAG;MAExC,IAAIjE,GAAG,IAAIA,GAAG,KAAK,IAAI,CAACD,mBAAmB,EAAE;QAC3C,IAAI,CAACA,mBAAmB,GAAG,IAAI;;MAGjC,IAAI,CAACgE,GAAG,CACN,SAAS,EACT,GAAGpC,OAAO,CAAC4B,MAAM,IAAI,EAAE,IAAIqB,KAAK,IAAII,KAAK,IACtChF,GAAG,IAAI,GAAG,GAAGA,GAAG,GAAG,GAAG,IAAK,EAC9B,EAAE,EACF2B,OAAO,CACR;MACD,IAAI,CAACpC,QAAQ,CACV4H,MAAM,CAAE7D,OAAwB,IAAKA,OAAO,CAACwE,SAAS,CAAClD,KAAK,CAAC,CAAC,CAC9DsB,OAAO,CAAE5C,OAAwB,IAChCA,OAAO,CAACyE,QAAQ,CAAC/C,KAAK,EAAErD,OAAO,EAAE3B,GAAG,CAAC,CACtC;MACH,IAAI,CAACK,oBAAoB,CAACI,OAAO,CAACyF,OAAO,CAAEtE,QAAQ,IAAKA,QAAQ,CAACqC,GAAG,CAAC,CAAC;IACxE,CAAC,CAAC;EACJ;EAEA;EACQ,MAAMsD,WAAWA,CAAA;IACvB,IAAI,CAACxD,GAAG,CAAC,WAAW,EAAE,gBAAgB,IAAI,CAACtB,WAAW,EAAE,EAAE,CAAC;IAC3D,IAAI,CAACiE,eAAe,EAAE;IACtB,IAAI,CAACzE,cAAc,CAACkB,KAAK,EAAE;IAC3B,IAAI,CAAC,IAAI,CAACf,MAAM,EAAE;MAChB,IAAI,CAACvC,cAAc,IAAIqD,aAAa,CAAC,IAAI,CAACrD,cAAc,CAAC;MACzD,IAAI,CAACA,cAAc,GAAGmI,WAAW,CAC/B,MAAM,IAAI,CAACxB,aAAa,EAAE,EAC1B,IAAI,CAAC5G,mBAAmB,CACzB;KACF,MAAM;MACL,IAAI,IAAI,CAAC4C,SAAS,EAAE;QAClB,IAAI,CAACuB,GAAG,CAAC,QAAQ,EAAE,4BAA4B,IAAI,CAACvB,SAAS,EAAE,CAAC;OACjE,MAAM;QACL,IAAI,CAACuB,GAAG,CAAC,QAAQ,EAAE,yBAAyB,CAAC;;MAG/C,MAAMkE,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC1F,SAAU,CAAC;MACxD,IAAI,CAAC2F,SAAS,GAAG,IAAI7F,MAAM,CAAC2F,SAAS,CAAC;MACtC,IAAI,CAACE,SAAS,CAACX,OAAO,GAAIhH,KAAK,IAAI;QACjC,IAAI,CAACuD,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAEvD,KAAK,CAACC,OAAO,CAAC;QACjD,IAAI,CAAC0H,SAAU,CAACC,SAAS,EAAE;MAC7B,CAAC;MACD,IAAI,CAACD,SAAS,CAACT,SAAS,GAAI1C,KAAK,IAAI;QACnC,IAAIA,KAAK,CAACd,IAAI,CAACc,KAAK,KAAK,WAAW,EAAE;UACpC,IAAI,CAACwB,aAAa,EAAE;;MAExB,CAAC;MACD,IAAI,CAAC2B,SAAS,CAACE,WAAW,CAAC;QACzBrD,KAAK,EAAE,OAAO;QACdsD,QAAQ,EAAE,IAAI,CAAC1I;OAChB,CAAC;;IAGJ,IAAI,CAACS,oBAAoB,CAACC,IAAI,CAAC4F,OAAO,CAAEtE,QAAQ,IAAKA,QAAQ,EAAE,CAAE;EACnE;EAEA;EAEQgG,YAAYA,CAAC5C,KAAU;IAC7B,IAAI,CAACjB,GAAG,CAAC,WAAW,EAAE,OAAO,EAAEiB,KAAK,CAAC;IACrC,IAAI,CAACuD,iBAAiB,EAAE;IACxB,IAAI,CAAC1I,cAAc,IAAIqD,aAAa,CAAC,IAAI,CAACrD,cAAc,CAAC;IACzD,IAAI,CAACoC,cAAc,CAACuG,eAAe,EAAE;IACrC,IAAI,CAACnI,oBAAoB,CAACE,KAAK,CAAC2F,OAAO,CAAEtE,QAAQ,IAAKA,QAAQ,CAACoD,KAAK,CAAC,CAAC;EACxE;EAEA;EACQyC,YAAYA,CAACjH,KAAyB;IAC5C,IAAI,CAACuD,GAAG,CAAC,WAAW,EAAEvD,KAAK,CAACC,OAAO,CAAC;IACpC,IAAI,CAAC8H,iBAAiB,EAAE;IACxB,IAAI,CAAClI,oBAAoB,CAACG,KAAK,CAAC0F,OAAO,CAAEtE,QAAQ,IAAKA,QAAQ,CAACpB,KAAK,CAAC,CAAC;EACxE;EAEA;EACQ+H,iBAAiBA,CAAA;IACvB,IAAI,CAAChJ,QAAQ,CAAC2G,OAAO,CAAE5C,OAAwB,IAC7CA,OAAO,CAACyE,QAAQ,CAAC9J,cAAc,CAACuC,KAAK,CAAC,CACvC;EACH;EAEA;EACQqC,aAAaA,CACnB4F,GAAW,EACX/I,MAAiC;IAEjC,IAAI0B,MAAM,CAACsH,IAAI,CAAChJ,MAAM,CAAC,CAAC+D,MAAM,KAAK,CAAC,EAAE;MACpC,OAAOgF,GAAG;;IAEZ,MAAME,MAAM,GAAGF,GAAG,CAACG,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;IAC1C,MAAMC,KAAK,GAAG,IAAIC,eAAe,CAACpJ,MAAM,CAAC;IAEzC,OAAO,GAAG+I,GAAG,GAAGE,MAAM,GAAGE,KAAK,EAAE;EAClC;EAEQX,gBAAgBA,CAACO,GAAuB;IAC9C,IAAIM,UAAkB;IACtB,IAAIN,GAAG,EAAE;MACPM,UAAU,GAAGN,GAAG;KACjB,MAAM;MACL,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACjK,aAAa,CAAC,EAAE;QAAEkK,IAAI,EAAE;MAAwB,CAAE,CAAC;MAC1EH,UAAU,GAAGI,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;;IAExC,OAAOD,UAAU;EACnB;;AAGF,MAAMpG,gBAAgB;EAWpBzD,YACEmK,OAAe,EACfC,UAAqB,EACrBlK,OAA4B;IAb9B,KAAAiI,UAAU,GAAW,aAAa;IAElC,KAAApE,OAAO,GAAa,MAAK,CAAE,CAAC;IAC5B,KAAAuE,OAAO,GAAa,MAAK,CAAE,CAAC;IAC5B,KAAAE,SAAS,GAAa,MAAK,CAAE,CAAC;IAC9B,KAAAJ,MAAM,GAAa,MAAK,CAAE,CAAC;IAC3B,KAAAlD,UAAU,GAAW/F,aAAa,CAACgG,UAAU;IAC7C,KAAAa,IAAI,GAAa,MAAK,CAAE,CAAC;IACzB,KAAAuD,GAAG,GAAwB,IAAI;IAO7B,IAAI,CAACA,GAAG,GAAGY,OAAO;IAClB,IAAI,CAAC9I,KAAK,GAAGnB,OAAO,CAACmB,KAAK;EAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}