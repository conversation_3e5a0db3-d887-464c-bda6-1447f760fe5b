{"ast": null, "code": "/**\n * @import {Effects, State, TokenType} from 'micromark-util-types'\n */\n\nimport { markdownSpace } from 'micromark-util-character';\n\n// To do: implement `spaceOrTab`, `spaceOrTabMinMax`, `spaceOrTabWithOptions`.\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   spaces in markdown are often optional, in which case this factory can be\n *     used and `ok` will be switched to whether spaces were found or not\n * *   one line ending or space can be detected with `markdownSpace(code)` right\n *     before using `factorySpace`\n *\n * ###### Examples\n *\n * Where `␉` represents a tab (plus how much it expands) and `␠` represents a\n * single space.\n *\n * ```markdown\n * ␉\n * ␠␠␠␠\n * ␉␠\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {TokenType} type\n *   Type (`' \\t'`).\n * @param {number | undefined} [max=Infinity]\n *   Max (exclusive).\n * @returns {State}\n *   Start state.\n */\nexport function factorySpace(effects, ok, type, max) {\n  const limit = max ? max - 1 : Number.POSITIVE_INFINITY;\n  let size = 0;\n  return start;\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownSpace(code)) {\n      effects.enter(type);\n      return prefix(code);\n    }\n    return ok(code);\n  }\n\n  /** @type {State} */\n  function prefix(code) {\n    if (markdownSpace(code) && size++ < limit) {\n      effects.consume(code);\n      return prefix;\n    }\n    effects.exit(type);\n    return ok(code);\n  }\n}", "map": {"version": 3, "names": ["markdownSpace", "factorySpace", "effects", "ok", "type", "max", "limit", "Number", "POSITIVE_INFINITY", "size", "start", "code", "enter", "prefix", "consume", "exit"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark-factory-space/dev/index.js"], "sourcesContent": ["/**\n * @import {Effects, State, TokenType} from 'micromark-util-types'\n */\n\nimport {markdownSpace} from 'micromark-util-character'\n\n// To do: implement `spaceOrTab`, `spaceOrTabMinMax`, `spaceOrTabWithOptions`.\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   spaces in markdown are often optional, in which case this factory can be\n *     used and `ok` will be switched to whether spaces were found or not\n * *   one line ending or space can be detected with `markdownSpace(code)` right\n *     before using `factorySpace`\n *\n * ###### Examples\n *\n * Where `␉` represents a tab (plus how much it expands) and `␠` represents a\n * single space.\n *\n * ```markdown\n * ␉\n * ␠␠␠␠\n * ␉␠\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {TokenType} type\n *   Type (`' \\t'`).\n * @param {number | undefined} [max=Infinity]\n *   Max (exclusive).\n * @returns {State}\n *   Start state.\n */\nexport function factorySpace(effects, ok, type, max) {\n  const limit = max ? max - 1 : Number.POSITIVE_INFINITY\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownSpace(code)) {\n      effects.enter(type)\n      return prefix(code)\n    }\n\n    return ok(code)\n  }\n\n  /** @type {State} */\n  function prefix(code) {\n    if (markdownSpace(code) && size++ < limit) {\n      effects.consume(code)\n      return prefix\n    }\n\n    effects.exit(type)\n    return ok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,aAAa,QAAO,0BAA0B;;AAEtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,EAAE,EAAEC,IAAI,EAAEC,GAAG,EAAE;EACnD,MAAMC,KAAK,GAAGD,GAAG,GAAGA,GAAG,GAAG,CAAC,GAAGE,MAAM,CAACC,iBAAiB;EACtD,IAAIC,IAAI,GAAG,CAAC;EAEZ,OAAOC,KAAK;;EAEZ;EACA,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIX,aAAa,CAACW,IAAI,CAAC,EAAE;MACvBT,OAAO,CAACU,KAAK,CAACR,IAAI,CAAC;MACnB,OAAOS,MAAM,CAACF,IAAI,CAAC;IACrB;IAEA,OAAOR,EAAE,CAACQ,IAAI,CAAC;EACjB;;EAEA;EACA,SAASE,MAAMA,CAACF,IAAI,EAAE;IACpB,IAAIX,aAAa,CAACW,IAAI,CAAC,IAAIF,IAAI,EAAE,GAAGH,KAAK,EAAE;MACzCJ,OAAO,CAACY,OAAO,CAACH,IAAI,CAAC;MACrB,OAAOE,MAAM;IACf;IAEAX,OAAO,CAACa,IAAI,CAACX,IAAI,CAAC;IAClB,OAAOD,EAAE,CAACQ,IAAI,CAAC;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}