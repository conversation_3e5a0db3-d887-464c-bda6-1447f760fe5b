{"ast": null, "code": "import { init } from './methods/init/init';\nimport { send } from './methods/send/send';\nimport { sendForm } from './methods/sendForm/sendForm';\nexport { init, send, sendForm };\nexport default {\n  init,\n  send,\n  sendForm\n};", "map": {"version": 3, "names": ["init", "send", "sendForm"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/emailjs-com/es/index.js"], "sourcesContent": ["import { init } from './methods/init/init';\nimport { send } from './methods/send/send';\nimport { sendForm } from './methods/sendForm/sendForm';\nexport { init, send, sendForm };\nexport default {\n    init,\n    send,\n    sendForm,\n};\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASF,IAAI,EAAEC,IAAI,EAAEC,QAAQ;AAC7B,eAAe;EACXF,IAAI;EACJC,IAAI;EACJC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}