{"ast": null, "code": "/**\n * HTML URL properties.\n *\n * Each key is a property name and each value is a list of tag names it applies\n * to or `null` if it applies to all elements.\n *\n * @type {Record<string, Array<string> | null>}\n */\nexport const urlAttributes = {\n  action: ['form'],\n  cite: ['blockquote', 'del', 'ins', 'q'],\n  data: ['object'],\n  formAction: ['button', 'input'],\n  href: ['a', 'area', 'base', 'link'],\n  icon: ['menuitem'],\n  itemId: null,\n  manifest: ['html'],\n  ping: ['a', 'area'],\n  poster: ['video'],\n  src: ['audio', 'embed', 'iframe', 'img', 'input', 'script', 'source', 'track', 'video']\n};", "map": {"version": 3, "names": ["urlAttributes", "action", "cite", "data", "formAction", "href", "icon", "itemId", "manifest", "ping", "poster", "src"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/html-url-attributes/lib/index.js"], "sourcesContent": ["/**\n * HTML URL properties.\n *\n * Each key is a property name and each value is a list of tag names it applies\n * to or `null` if it applies to all elements.\n *\n * @type {Record<string, Array<string> | null>}\n */\nexport const urlAttributes = {\n  action: ['form'],\n  cite: ['blockquote', 'del', 'ins', 'q'],\n  data: ['object'],\n  formAction: ['button', 'input'],\n  href: ['a', 'area', 'base', 'link'],\n  icon: ['menuitem'],\n  itemId: null,\n  manifest: ['html'],\n  ping: ['a', 'area'],\n  poster: ['video'],\n  src: [\n    'audio',\n    'embed',\n    'iframe',\n    'img',\n    'input',\n    'script',\n    'source',\n    'track',\n    'video'\n  ]\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,aAAa,GAAG;EAC3BC,MAAM,EAAE,CAAC,MAAM,CAAC;EAChBC,IAAI,EAAE,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;EACvCC,IAAI,EAAE,CAAC,QAAQ,CAAC;EAChBC,UAAU,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;EAC/BC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACnCC,IAAI,EAAE,CAAC,UAAU,CAAC;EAClBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CAAC,MAAM,CAAC;EAClBC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC;EACnBC,MAAM,EAAE,CAAC,OAAO,CAAC;EACjBC,GAAG,EAAE,CACH,OAAO,EACP,OAAO,EACP,QAAQ,EACR,KAAK,EACL,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO;AAEX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}