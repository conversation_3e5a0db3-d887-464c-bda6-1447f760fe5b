{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   Event,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factoryDestination } from 'micromark-factory-destination';\nimport { factoryLabel } from 'micromark-factory-label';\nimport { factoryTitle } from 'micromark-factory-title';\nimport { factoryWhitespace } from 'micromark-factory-whitespace';\nimport { markdownLineEndingOrSpace } from 'micromark-util-character';\nimport { push, splice } from 'micromark-util-chunked';\nimport { normalizeIdentifier } from 'micromark-util-normalize-identifier';\nimport { resolveAll } from 'micromark-util-resolve-all';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const labelEnd = {\n  name: 'labelEnd',\n  resolveAll: resolveAllLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  tokenize: tokenizeLabelEnd\n};\n\n/** @type {Construct} */\nconst resourceConstruct = {\n  tokenize: tokenizeResource\n};\n/** @type {Construct} */\nconst referenceFullConstruct = {\n  tokenize: tokenizeReferenceFull\n};\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {\n  tokenize: tokenizeReferenceCollapsed\n};\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1;\n  /** @type {Array<Event>} */\n  const newEvents = [];\n  while (++index < events.length) {\n    const token = events[index][1];\n    newEvents.push(events[index]);\n    if (token.type === types.labelImage || token.type === types.labelLink || token.type === types.labelEnd) {\n      // Remove the marker.\n      const offset = token.type === types.labelImage ? 4 : 2;\n      token.type = types.data;\n      index += offset;\n    }\n  }\n\n  // If the events are equal, we don't have to copy newEvents to events\n  if (events.length !== newEvents.length) {\n    splice(events, 0, events.length, newEvents);\n  }\n  return events;\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length;\n  let offset = 0;\n  /** @type {Token} */\n  let token;\n  /** @type {number | undefined} */\n  let open;\n  /** @type {number | undefined} */\n  let close;\n  /** @type {Array<Event>} */\n  let media;\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1];\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (token.type === types.link || token.type === types.labelLink && token._inactive) {\n        break;\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === types.labelLink) {\n        token._inactive = true;\n      }\n    } else if (close) {\n      if (events[index][0] === 'enter' && (token.type === types.labelImage || token.type === types.labelLink) && !token._balanced) {\n        open = index;\n        if (token.type !== types.labelLink) {\n          offset = 2;\n          break;\n        }\n      }\n    } else if (token.type === types.labelEnd) {\n      close = index;\n    }\n  }\n  assert(open !== undefined, '`open` is supposed to be found');\n  assert(close !== undefined, '`close` is supposed to be found');\n  const group = {\n    type: events[open][1].type === types.labelLink ? types.link : types.image,\n    start: {\n      ...events[open][1].start\n    },\n    end: {\n      ...events[events.length - 1][1].end\n    }\n  };\n  const label = {\n    type: types.label,\n    start: {\n      ...events[open][1].start\n    },\n    end: {\n      ...events[close][1].end\n    }\n  };\n  const text = {\n    type: types.labelText,\n    start: {\n      ...events[open + offset + 2][1].end\n    },\n    end: {\n      ...events[close - 2][1].start\n    }\n  };\n  media = [['enter', group, context], ['enter', label, context]];\n\n  // Opening marker.\n  media = push(media, events.slice(open + 1, open + offset + 3));\n\n  // Text open.\n  media = push(media, [['enter', text, context]]);\n\n  // Always populated by defaults.\n  assert(context.parser.constructs.insideSpan.null, 'expected `insideSpan.null` to be populated');\n  // Between.\n  media = push(media, resolveAll(context.parser.constructs.insideSpan.null, events.slice(open + offset + 4, close - 3), context));\n\n  // Text close, marker close, label close.\n  media = push(media, [['exit', text, context], events[close - 2], events[close - 1], ['exit', label, context]]);\n\n  // Reference, resource, or so.\n  media = push(media, events.slice(close + 1));\n\n  // Media close.\n  media = push(media, [['exit', group, context]]);\n  splice(events, open, events.length, media);\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this;\n  let index = self.events.length;\n  /** @type {Token} */\n  let labelStart;\n  /** @type {boolean} */\n  let defined;\n\n  // Find an opening.\n  while (index--) {\n    if ((self.events[index][1].type === types.labelImage || self.events[index][1].type === types.labelLink) && !self.events[index][1]._balanced) {\n      labelStart = self.events[index][1];\n      break;\n    }\n  }\n  return start;\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`');\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code);\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code);\n    }\n    defined = self.parser.defined.includes(normalizeIdentifier(self.sliceSerialize({\n      start: labelStart.end,\n      end: self.now()\n    })));\n    effects.enter(types.labelEnd);\n    effects.enter(types.labelMarker);\n    effects.consume(code);\n    effects.exit(types.labelMarker);\n    effects.exit(types.labelEnd);\n    return after;\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === codes.leftParenthesis) {\n      return effects.attempt(resourceConstruct, labelEndOk, defined ? labelEndOk : labelEndNok)(code);\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === codes.leftSquareBracket) {\n      return effects.attempt(referenceFullConstruct, labelEndOk, defined ? referenceNotFull : labelEndNok)(code);\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code);\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(referenceCollapsedConstruct, labelEndOk, labelEndNok)(code);\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code);\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true;\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart;\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    assert(code === codes.leftParenthesis, 'expected left paren');\n    effects.enter(types.resource);\n    effects.enter(types.resourceMarker);\n    effects.consume(code);\n    effects.exit(types.resourceMarker);\n    return resourceBefore;\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, resourceOpen)(code) : resourceOpen(code);\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === codes.rightParenthesis) {\n      return resourceEnd(code);\n    }\n    return factoryDestination(effects, resourceDestinationAfter, resourceDestinationMissing, types.resourceDestination, types.resourceDestinationLiteral, types.resourceDestinationLiteralMarker, types.resourceDestinationRaw, types.resourceDestinationString, constants.linkResourceDestinationBalanceMax)(code);\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, resourceBetween)(code) : resourceEnd(code);\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code);\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (code === codes.quotationMark || code === codes.apostrophe || code === codes.leftParenthesis) {\n      return factoryTitle(effects, resourceTitleAfter, nok, types.resourceTitle, types.resourceTitleMarker, types.resourceTitleString)(code);\n    }\n    return resourceEnd(code);\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, resourceEnd)(code) : resourceEnd(code);\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === codes.rightParenthesis) {\n      effects.enter(types.resourceMarker);\n      effects.consume(code);\n      effects.exit(types.resourceMarker);\n      effects.exit(types.resource);\n      return ok;\n    }\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this;\n  return referenceFull;\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    assert(code === codes.leftSquareBracket, 'expected left bracket');\n    return factoryLabel.call(self, effects, referenceFullAfter, referenceFullMissing, types.reference, types.referenceMarker, types.referenceString)(code);\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(normalizeIdentifier(self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1))) ? ok(code) : nok(code);\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart;\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    assert(code === codes.leftSquareBracket, 'expected left bracket');\n    effects.enter(types.reference);\n    effects.enter(types.referenceMarker);\n    effects.consume(code);\n    effects.exit(types.referenceMarker);\n    return referenceCollapsedOpen;\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter(types.referenceMarker);\n      effects.consume(code);\n      effects.exit(types.referenceMarker);\n      effects.exit(types.reference);\n      return ok;\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factoryDestination", "factoryLabel", "factoryTitle", "factoryWhitespace", "markdownLineEndingOrSpace", "push", "splice", "normalizeIdentifier", "resolveAll", "codes", "constants", "types", "labelEnd", "name", "resolveAllLabelEnd", "resolveTo", "resolveToLabelEnd", "tokenize", "tokenizeLabelEnd", "resourceConstruct", "tokenizeResource", "referenceFullConstruct", "tokenizeReferenceFull", "referenceCollapsedConstruct", "tokenizeReferenceCollapsed", "events", "index", "newEvents", "length", "token", "type", "labelImage", "labelLink", "offset", "data", "context", "open", "close", "media", "link", "_inactive", "_balanced", "undefined", "group", "image", "start", "end", "label", "text", "labelText", "slice", "parser", "constructs", "insideSpan", "null", "effects", "nok", "self", "labelStart", "defined", "code", "rightSquareBracket", "labelEndNok", "includes", "sliceSerialize", "now", "enter", "labelMarker", "consume", "exit", "after", "leftParenthesis", "attempt", "labelEndOk", "leftSquareBracket", "referenceNotFull", "resourceStart", "resource", "resourceMarker", "resourceBefore", "resourceOpen", "rightParenthesis", "resourceEnd", "resourceDestinationAfter", "resourceDestinationMissing", "resourceDestination", "resourceDestinationLiteral", "resourceDestinationLiteralMarker", "resourceDestinationRaw", "resourceDestinationString", "linkResourceDestinationBalanceMax", "resourceBetween", "quotationMark", "apostrophe", "resourceTitleAfter", "resourceTitle", "resourceTitleMarker", "resourceTitleString", "referenceFull", "call", "referenceFullAfter", "referenceFullMissing", "reference", "<PERSON><PERSON><PERSON><PERSON>", "referenceString", "referenceCollapsedStart", "referenceCollapsedOpen"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark-core-commonmark/dev/lib/label-end.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Event,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {markdownLineEndingOrSpace} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const labelEnd = {\n  name: 'labelEnd',\n  resolveAll: resolveAllLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  tokenize: tokenizeLabelEnd\n}\n\n/** @type {Construct} */\nconst resourceConstruct = {tokenize: tokenizeResource}\n/** @type {Construct} */\nconst referenceFullConstruct = {tokenize: tokenizeReferenceFull}\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {tokenize: tokenizeReferenceCollapsed}\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1\n  /** @type {Array<Event>} */\n  const newEvents = []\n  while (++index < events.length) {\n    const token = events[index][1]\n    newEvents.push(events[index])\n\n    if (\n      token.type === types.labelImage ||\n      token.type === types.labelLink ||\n      token.type === types.labelEnd\n    ) {\n      // Remove the marker.\n      const offset = token.type === types.labelImage ? 4 : 2\n      token.type = types.data\n      index += offset\n    }\n  }\n\n  // If the events are equal, we don't have to copy newEvents to events\n  if (events.length !== newEvents.length) {\n    splice(events, 0, events.length, newEvents)\n  }\n\n  return events\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length\n  let offset = 0\n  /** @type {Token} */\n  let token\n  /** @type {number | undefined} */\n  let open\n  /** @type {number | undefined} */\n  let close\n  /** @type {Array<Event>} */\n  let media\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1]\n\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (\n        token.type === types.link ||\n        (token.type === types.labelLink && token._inactive)\n      ) {\n        break\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === types.labelLink) {\n        token._inactive = true\n      }\n    } else if (close) {\n      if (\n        events[index][0] === 'enter' &&\n        (token.type === types.labelImage || token.type === types.labelLink) &&\n        !token._balanced\n      ) {\n        open = index\n\n        if (token.type !== types.labelLink) {\n          offset = 2\n          break\n        }\n      }\n    } else if (token.type === types.labelEnd) {\n      close = index\n    }\n  }\n\n  assert(open !== undefined, '`open` is supposed to be found')\n  assert(close !== undefined, '`close` is supposed to be found')\n\n  const group = {\n    type: events[open][1].type === types.labelLink ? types.link : types.image,\n    start: {...events[open][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  const label = {\n    type: types.label,\n    start: {...events[open][1].start},\n    end: {...events[close][1].end}\n  }\n\n  const text = {\n    type: types.labelText,\n    start: {...events[open + offset + 2][1].end},\n    end: {...events[close - 2][1].start}\n  }\n\n  media = [\n    ['enter', group, context],\n    ['enter', label, context]\n  ]\n\n  // Opening marker.\n  media = push(media, events.slice(open + 1, open + offset + 3))\n\n  // Text open.\n  media = push(media, [['enter', text, context]])\n\n  // Always populated by defaults.\n  assert(\n    context.parser.constructs.insideSpan.null,\n    'expected `insideSpan.null` to be populated'\n  )\n  // Between.\n  media = push(\n    media,\n    resolveAll(\n      context.parser.constructs.insideSpan.null,\n      events.slice(open + offset + 4, close - 3),\n      context\n    )\n  )\n\n  // Text close, marker close, label close.\n  media = push(media, [\n    ['exit', text, context],\n    events[close - 2],\n    events[close - 1],\n    ['exit', label, context]\n  ])\n\n  // Reference, resource, or so.\n  media = push(media, events.slice(close + 1))\n\n  // Media close.\n  media = push(media, [['exit', group, context]])\n\n  splice(events, open, events.length, media)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  /** @type {Token} */\n  let labelStart\n  /** @type {boolean} */\n  let defined\n\n  // Find an opening.\n  while (index--) {\n    if (\n      (self.events[index][1].type === types.labelImage ||\n        self.events[index][1].type === types.labelLink) &&\n      !self.events[index][1]._balanced\n    ) {\n      labelStart = self.events[index][1]\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`')\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code)\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code)\n    }\n\n    defined = self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize({start: labelStart.end, end: self.now()})\n      )\n    )\n    effects.enter(types.labelEnd)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelEnd)\n    return after\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === codes.leftParenthesis) {\n      return effects.attempt(\n        resourceConstruct,\n        labelEndOk,\n        defined ? labelEndOk : labelEndNok\n      )(code)\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === codes.leftSquareBracket) {\n      return effects.attempt(\n        referenceFullConstruct,\n        labelEndOk,\n        defined ? referenceNotFull : labelEndNok\n      )(code)\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code)\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(\n      referenceCollapsedConstruct,\n      labelEndOk,\n      labelEndNok\n    )(code)\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code)\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    assert(code === codes.leftParenthesis, 'expected left paren')\n    effects.enter(types.resource)\n    effects.enter(types.resourceMarker)\n    effects.consume(code)\n    effects.exit(types.resourceMarker)\n    return resourceBefore\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceOpen)(code)\n      : resourceOpen(code)\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === codes.rightParenthesis) {\n      return resourceEnd(code)\n    }\n\n    return factoryDestination(\n      effects,\n      resourceDestinationAfter,\n      resourceDestinationMissing,\n      types.resourceDestination,\n      types.resourceDestinationLiteral,\n      types.resourceDestinationLiteralMarker,\n      types.resourceDestinationRaw,\n      types.resourceDestinationString,\n      constants.linkResourceDestinationBalanceMax\n    )(code)\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceBetween)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code)\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.leftParenthesis\n    ) {\n      return factoryTitle(\n        effects,\n        resourceTitleAfter,\n        nok,\n        types.resourceTitle,\n        types.resourceTitleMarker,\n        types.resourceTitleString\n      )(code)\n    }\n\n    return resourceEnd(code)\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceEnd)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === codes.rightParenthesis) {\n      effects.enter(types.resourceMarker)\n      effects.consume(code)\n      effects.exit(types.resourceMarker)\n      effects.exit(types.resource)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this\n\n  return referenceFull\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    return factoryLabel.call(\n      self,\n      effects,\n      referenceFullAfter,\n      referenceFullMissing,\n      types.reference,\n      types.referenceMarker,\n      types.referenceString\n    )(code)\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n      )\n    )\n      ? ok(code)\n      : nok(code)\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    effects.enter(types.reference)\n    effects.enter(types.referenceMarker)\n    effects.consume(code)\n    effects.exit(types.referenceMarker)\n    return referenceCollapsedOpen\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter(types.referenceMarker)\n      effects.consume(code)\n      effects.exit(types.referenceMarker)\n      effects.exit(types.reference)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,kBAAkB,QAAO,+BAA+B;AAChE,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,iBAAiB,QAAO,8BAA8B;AAC9D,SAAQC,yBAAyB,QAAO,0BAA0B;AAClE,SAAQC,IAAI,EAAEC,MAAM,QAAO,wBAAwB;AACnD,SAAQC,mBAAmB,QAAO,qCAAqC;AACvE,SAAQC,UAAU,QAAO,4BAA4B;AACrD,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBC,IAAI,EAAE,UAAU;EAChBL,UAAU,EAAEM,kBAAkB;EAC9BC,SAAS,EAAEC,iBAAiB;EAC5BC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA,MAAMC,iBAAiB,GAAG;EAACF,QAAQ,EAAEG;AAAgB,CAAC;AACtD;AACA,MAAMC,sBAAsB,GAAG;EAACJ,QAAQ,EAAEK;AAAqB,CAAC;AAChE;AACA,MAAMC,2BAA2B,GAAG;EAACN,QAAQ,EAAEO;AAA0B,CAAC;;AAE1E;AACA,SAASV,kBAAkBA,CAACW,MAAM,EAAE;EAClC,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd;EACA,MAAMC,SAAS,GAAG,EAAE;EACpB,OAAO,EAAED,KAAK,GAAGD,MAAM,CAACG,MAAM,EAAE;IAC9B,MAAMC,KAAK,GAAGJ,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9BC,SAAS,CAACtB,IAAI,CAACoB,MAAM,CAACC,KAAK,CAAC,CAAC;IAE7B,IACEG,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACoB,UAAU,IAC/BF,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACqB,SAAS,IAC9BH,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACC,QAAQ,EAC7B;MACA;MACA,MAAMqB,MAAM,GAAGJ,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACoB,UAAU,GAAG,CAAC,GAAG,CAAC;MACtDF,KAAK,CAACC,IAAI,GAAGnB,KAAK,CAACuB,IAAI;MACvBR,KAAK,IAAIO,MAAM;IACjB;EACF;;EAEA;EACA,IAAIR,MAAM,CAACG,MAAM,KAAKD,SAAS,CAACC,MAAM,EAAE;IACtCtB,MAAM,CAACmB,MAAM,EAAE,CAAC,EAAEA,MAAM,CAACG,MAAM,EAAED,SAAS,CAAC;EAC7C;EAEA,OAAOF,MAAM;AACf;;AAEA;AACA,SAAST,iBAAiBA,CAACS,MAAM,EAAEU,OAAO,EAAE;EAC1C,IAAIT,KAAK,GAAGD,MAAM,CAACG,MAAM;EACzB,IAAIK,MAAM,GAAG,CAAC;EACd;EACA,IAAIJ,KAAK;EACT;EACA,IAAIO,IAAI;EACR;EACA,IAAIC,KAAK;EACT;EACA,IAAIC,KAAK;;EAET;EACA,OAAOZ,KAAK,EAAE,EAAE;IACdG,KAAK,GAAGJ,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAExB,IAAIU,IAAI,EAAE;MACR;MACA,IACEP,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAAC4B,IAAI,IACxBV,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACqB,SAAS,IAAIH,KAAK,CAACW,SAAU,EACnD;QACA;MACF;;MAEA;MACA;MACA,IAAIf,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,IAAIG,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACqB,SAAS,EAAE;QAClEH,KAAK,CAACW,SAAS,GAAG,IAAI;MACxB;IACF,CAAC,MAAM,IAAIH,KAAK,EAAE;MAChB,IACEZ,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,KAC3BG,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACoB,UAAU,IAAIF,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACqB,SAAS,CAAC,IACnE,CAACH,KAAK,CAACY,SAAS,EAChB;QACAL,IAAI,GAAGV,KAAK;QAEZ,IAAIG,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACqB,SAAS,EAAE;UAClCC,MAAM,GAAG,CAAC;UACV;QACF;MACF;IACF,CAAC,MAAM,IAAIJ,KAAK,CAACC,IAAI,KAAKnB,KAAK,CAACC,QAAQ,EAAE;MACxCyB,KAAK,GAAGX,KAAK;IACf;EACF;EAEA3B,MAAM,CAACqC,IAAI,KAAKM,SAAS,EAAE,gCAAgC,CAAC;EAC5D3C,MAAM,CAACsC,KAAK,KAAKK,SAAS,EAAE,iCAAiC,CAAC;EAE9D,MAAMC,KAAK,GAAG;IACZb,IAAI,EAAEL,MAAM,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,CAACN,IAAI,KAAKnB,KAAK,CAACqB,SAAS,GAAGrB,KAAK,CAAC4B,IAAI,GAAG5B,KAAK,CAACiC,KAAK;IACzEC,KAAK,EAAE;MAAC,GAAGpB,MAAM,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,CAACS;IAAK,CAAC;IACjCC,GAAG,EAAE;MAAC,GAAGrB,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACkB;IAAG;EAC3C,CAAC;EAED,MAAMC,KAAK,GAAG;IACZjB,IAAI,EAAEnB,KAAK,CAACoC,KAAK;IACjBF,KAAK,EAAE;MAAC,GAAGpB,MAAM,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,CAACS;IAAK,CAAC;IACjCC,GAAG,EAAE;MAAC,GAAGrB,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC,CAACS;IAAG;EAC/B,CAAC;EAED,MAAME,IAAI,GAAG;IACXlB,IAAI,EAAEnB,KAAK,CAACsC,SAAS;IACrBJ,KAAK,EAAE;MAAC,GAAGpB,MAAM,CAACW,IAAI,GAAGH,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACa;IAAG,CAAC;IAC5CA,GAAG,EAAE;MAAC,GAAGrB,MAAM,CAACY,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACQ;IAAK;EACrC,CAAC;EAEDP,KAAK,GAAG,CACN,CAAC,OAAO,EAAEK,KAAK,EAAER,OAAO,CAAC,EACzB,CAAC,OAAO,EAAEY,KAAK,EAAEZ,OAAO,CAAC,CAC1B;;EAED;EACAG,KAAK,GAAGjC,IAAI,CAACiC,KAAK,EAAEb,MAAM,CAACyB,KAAK,CAACd,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,MAAM,GAAG,CAAC,CAAC,CAAC;;EAE9D;EACAK,KAAK,GAAGjC,IAAI,CAACiC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAEU,IAAI,EAAEb,OAAO,CAAC,CAAC,CAAC;;EAE/C;EACApC,MAAM,CACJoC,OAAO,CAACgB,MAAM,CAACC,UAAU,CAACC,UAAU,CAACC,IAAI,EACzC,4CACF,CAAC;EACD;EACAhB,KAAK,GAAGjC,IAAI,CACViC,KAAK,EACL9B,UAAU,CACR2B,OAAO,CAACgB,MAAM,CAACC,UAAU,CAACC,UAAU,CAACC,IAAI,EACzC7B,MAAM,CAACyB,KAAK,CAACd,IAAI,GAAGH,MAAM,GAAG,CAAC,EAAEI,KAAK,GAAG,CAAC,CAAC,EAC1CF,OACF,CACF,CAAC;;EAED;EACAG,KAAK,GAAGjC,IAAI,CAACiC,KAAK,EAAE,CAClB,CAAC,MAAM,EAAEU,IAAI,EAAEb,OAAO,CAAC,EACvBV,MAAM,CAACY,KAAK,GAAG,CAAC,CAAC,EACjBZ,MAAM,CAACY,KAAK,GAAG,CAAC,CAAC,EACjB,CAAC,MAAM,EAAEU,KAAK,EAAEZ,OAAO,CAAC,CACzB,CAAC;;EAEF;EACAG,KAAK,GAAGjC,IAAI,CAACiC,KAAK,EAAEb,MAAM,CAACyB,KAAK,CAACb,KAAK,GAAG,CAAC,CAAC,CAAC;;EAE5C;EACAC,KAAK,GAAGjC,IAAI,CAACiC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAEK,KAAK,EAAER,OAAO,CAAC,CAAC,CAAC;EAE/C7B,MAAM,CAACmB,MAAM,EAAEW,IAAI,EAAEX,MAAM,CAACG,MAAM,EAAEU,KAAK,CAAC;EAE1C,OAAOb,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASP,gBAAgBA,CAACqC,OAAO,EAAEzD,EAAE,EAAE0D,GAAG,EAAE;EAC1C,MAAMC,IAAI,GAAG,IAAI;EACjB,IAAI/B,KAAK,GAAG+B,IAAI,CAAChC,MAAM,CAACG,MAAM;EAC9B;EACA,IAAI8B,UAAU;EACd;EACA,IAAIC,OAAO;;EAEX;EACA,OAAOjC,KAAK,EAAE,EAAE;IACd,IACE,CAAC+B,IAAI,CAAChC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACI,IAAI,KAAKnB,KAAK,CAACoB,UAAU,IAC9C0B,IAAI,CAAChC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACI,IAAI,KAAKnB,KAAK,CAACqB,SAAS,KAChD,CAACyB,IAAI,CAAChC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACe,SAAS,EAChC;MACAiB,UAAU,GAAGD,IAAI,CAAChC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;MAClC;IACF;EACF;EAEA,OAAOmB,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACe,IAAI,EAAE;IACnB7D,MAAM,CAAC6D,IAAI,KAAKnD,KAAK,CAACoD,kBAAkB,EAAE,cAAc,CAAC;;IAEzD;IACA,IAAI,CAACH,UAAU,EAAE;MACf,OAAOF,GAAG,CAACI,IAAI,CAAC;IAClB;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIF,UAAU,CAAClB,SAAS,EAAE;MACxB,OAAOsB,WAAW,CAACF,IAAI,CAAC;IAC1B;IAEAD,OAAO,GAAGF,IAAI,CAACN,MAAM,CAACQ,OAAO,CAACI,QAAQ,CACpCxD,mBAAmB,CACjBkD,IAAI,CAACO,cAAc,CAAC;MAACnB,KAAK,EAAEa,UAAU,CAACZ,GAAG;MAAEA,GAAG,EAAEW,IAAI,CAACQ,GAAG,CAAC;IAAC,CAAC,CAC9D,CACF,CAAC;IACDV,OAAO,CAACW,KAAK,CAACvD,KAAK,CAACC,QAAQ,CAAC;IAC7B2C,OAAO,CAACW,KAAK,CAACvD,KAAK,CAACwD,WAAW,CAAC;IAChCZ,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;IACrBL,OAAO,CAACc,IAAI,CAAC1D,KAAK,CAACwD,WAAW,CAAC;IAC/BZ,OAAO,CAACc,IAAI,CAAC1D,KAAK,CAACC,QAAQ,CAAC;IAC5B,OAAO0D,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACV,IAAI,EAAE;IACnB;IACA;;IAEA;IACA,IAAIA,IAAI,KAAKnD,KAAK,CAAC8D,eAAe,EAAE;MAClC,OAAOhB,OAAO,CAACiB,OAAO,CACpBrD,iBAAiB,EACjBsD,UAAU,EACVd,OAAO,GAAGc,UAAU,GAAGX,WACzB,CAAC,CAACF,IAAI,CAAC;IACT;;IAEA;IACA,IAAIA,IAAI,KAAKnD,KAAK,CAACiE,iBAAiB,EAAE;MACpC,OAAOnB,OAAO,CAACiB,OAAO,CACpBnD,sBAAsB,EACtBoD,UAAU,EACVd,OAAO,GAAGgB,gBAAgB,GAAGb,WAC/B,CAAC,CAACF,IAAI,CAAC;IACT;;IAEA;IACA,OAAOD,OAAO,GAAGc,UAAU,CAACb,IAAI,CAAC,GAAGE,WAAW,CAACF,IAAI,CAAC;EACvD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASe,gBAAgBA,CAACf,IAAI,EAAE;IAC9B,OAAOL,OAAO,CAACiB,OAAO,CACpBjD,2BAA2B,EAC3BkD,UAAU,EACVX,WACF,CAAC,CAACF,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASa,UAAUA,CAACb,IAAI,EAAE;IACxB;IACA,OAAO9D,EAAE,CAAC8D,IAAI,CAAC;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,WAAWA,CAACF,IAAI,EAAE;IACzBF,UAAU,CAACjB,SAAS,GAAG,IAAI;IAC3B,OAAOe,GAAG,CAACI,IAAI,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASxC,gBAAgBA,CAACmC,OAAO,EAAEzD,EAAE,EAAE0D,GAAG,EAAE;EAC1C,OAAOoB,aAAa;;EAEpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,aAAaA,CAAChB,IAAI,EAAE;IAC3B7D,MAAM,CAAC6D,IAAI,KAAKnD,KAAK,CAAC8D,eAAe,EAAE,qBAAqB,CAAC;IAC7DhB,OAAO,CAACW,KAAK,CAACvD,KAAK,CAACkE,QAAQ,CAAC;IAC7BtB,OAAO,CAACW,KAAK,CAACvD,KAAK,CAACmE,cAAc,CAAC;IACnCvB,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;IACrBL,OAAO,CAACc,IAAI,CAAC1D,KAAK,CAACmE,cAAc,CAAC;IAClC,OAAOC,cAAc;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,cAAcA,CAACnB,IAAI,EAAE;IAC5B,OAAOxD,yBAAyB,CAACwD,IAAI,CAAC,GAClCzD,iBAAiB,CAACoD,OAAO,EAAEyB,YAAY,CAAC,CAACpB,IAAI,CAAC,GAC9CoB,YAAY,CAACpB,IAAI,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASoB,YAAYA,CAACpB,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAKnD,KAAK,CAACwE,gBAAgB,EAAE;MACnC,OAAOC,WAAW,CAACtB,IAAI,CAAC;IAC1B;IAEA,OAAO5D,kBAAkB,CACvBuD,OAAO,EACP4B,wBAAwB,EACxBC,0BAA0B,EAC1BzE,KAAK,CAAC0E,mBAAmB,EACzB1E,KAAK,CAAC2E,0BAA0B,EAChC3E,KAAK,CAAC4E,gCAAgC,EACtC5E,KAAK,CAAC6E,sBAAsB,EAC5B7E,KAAK,CAAC8E,yBAAyB,EAC/B/E,SAAS,CAACgF,iCACZ,CAAC,CAAC9B,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASuB,wBAAwBA,CAACvB,IAAI,EAAE;IACtC,OAAOxD,yBAAyB,CAACwD,IAAI,CAAC,GAClCzD,iBAAiB,CAACoD,OAAO,EAAEoC,eAAe,CAAC,CAAC/B,IAAI,CAAC,GACjDsB,WAAW,CAACtB,IAAI,CAAC;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASwB,0BAA0BA,CAACxB,IAAI,EAAE;IACxC,OAAOJ,GAAG,CAACI,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS+B,eAAeA,CAAC/B,IAAI,EAAE;IAC7B,IACEA,IAAI,KAAKnD,KAAK,CAACmF,aAAa,IAC5BhC,IAAI,KAAKnD,KAAK,CAACoF,UAAU,IACzBjC,IAAI,KAAKnD,KAAK,CAAC8D,eAAe,EAC9B;MACA,OAAOrE,YAAY,CACjBqD,OAAO,EACPuC,kBAAkB,EAClBtC,GAAG,EACH7C,KAAK,CAACoF,aAAa,EACnBpF,KAAK,CAACqF,mBAAmB,EACzBrF,KAAK,CAACsF,mBACR,CAAC,CAACrC,IAAI,CAAC;IACT;IAEA,OAAOsB,WAAW,CAACtB,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASkC,kBAAkBA,CAAClC,IAAI,EAAE;IAChC,OAAOxD,yBAAyB,CAACwD,IAAI,CAAC,GAClCzD,iBAAiB,CAACoD,OAAO,EAAE2B,WAAW,CAAC,CAACtB,IAAI,CAAC,GAC7CsB,WAAW,CAACtB,IAAI,CAAC;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsB,WAAWA,CAACtB,IAAI,EAAE;IACzB,IAAIA,IAAI,KAAKnD,KAAK,CAACwE,gBAAgB,EAAE;MACnC1B,OAAO,CAACW,KAAK,CAACvD,KAAK,CAACmE,cAAc,CAAC;MACnCvB,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;MACrBL,OAAO,CAACc,IAAI,CAAC1D,KAAK,CAACmE,cAAc,CAAC;MAClCvB,OAAO,CAACc,IAAI,CAAC1D,KAAK,CAACkE,QAAQ,CAAC;MAC5B,OAAO/E,EAAE;IACX;IAEA,OAAO0D,GAAG,CAACI,IAAI,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAStC,qBAAqBA,CAACiC,OAAO,EAAEzD,EAAE,EAAE0D,GAAG,EAAE;EAC/C,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOyC,aAAa;;EAEpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,aAAaA,CAACtC,IAAI,EAAE;IAC3B7D,MAAM,CAAC6D,IAAI,KAAKnD,KAAK,CAACiE,iBAAiB,EAAE,uBAAuB,CAAC;IACjE,OAAOzE,YAAY,CAACkG,IAAI,CACtB1C,IAAI,EACJF,OAAO,EACP6C,kBAAkB,EAClBC,oBAAoB,EACpB1F,KAAK,CAAC2F,SAAS,EACf3F,KAAK,CAAC4F,eAAe,EACrB5F,KAAK,CAAC6F,eACR,CAAC,CAAC5C,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASwC,kBAAkBA,CAACxC,IAAI,EAAE;IAChC,OAAOH,IAAI,CAACN,MAAM,CAACQ,OAAO,CAACI,QAAQ,CACjCxD,mBAAmB,CACjBkD,IAAI,CAACO,cAAc,CAACP,IAAI,CAAChC,MAAM,CAACgC,IAAI,CAAChC,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACzE,CACF,CAAC,GACGpD,EAAE,CAAC8D,IAAI,CAAC,GACRJ,GAAG,CAACI,IAAI,CAAC;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASyC,oBAAoBA,CAACzC,IAAI,EAAE;IAClC,OAAOJ,GAAG,CAACI,IAAI,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASpC,0BAA0BA,CAAC+B,OAAO,EAAEzD,EAAE,EAAE0D,GAAG,EAAE;EACpD,OAAOiD,uBAAuB;;EAE9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,uBAAuBA,CAAC7C,IAAI,EAAE;IACrC;IACA7D,MAAM,CAAC6D,IAAI,KAAKnD,KAAK,CAACiE,iBAAiB,EAAE,uBAAuB,CAAC;IACjEnB,OAAO,CAACW,KAAK,CAACvD,KAAK,CAAC2F,SAAS,CAAC;IAC9B/C,OAAO,CAACW,KAAK,CAACvD,KAAK,CAAC4F,eAAe,CAAC;IACpChD,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;IACrBL,OAAO,CAACc,IAAI,CAAC1D,KAAK,CAAC4F,eAAe,CAAC;IACnC,OAAOG,sBAAsB;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,sBAAsBA,CAAC9C,IAAI,EAAE;IACpC,IAAIA,IAAI,KAAKnD,KAAK,CAACoD,kBAAkB,EAAE;MACrCN,OAAO,CAACW,KAAK,CAACvD,KAAK,CAAC4F,eAAe,CAAC;MACpChD,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;MACrBL,OAAO,CAACc,IAAI,CAAC1D,KAAK,CAAC4F,eAAe,CAAC;MACnChD,OAAO,CAACc,IAAI,CAAC1D,KAAK,CAAC2F,SAAS,CAAC;MAC7B,OAAOxG,EAAE;IACX;IAEA,OAAO0D,GAAG,CAACI,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}