{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { codes, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start;\n\n  /**\n   * Start of a hard break (escape).\n   *\n   * ```markdown\n   * > | a\\\n   *      ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`');\n    effects.enter(types.hardBreakEscape);\n    effects.consume(code);\n    return after;\n  }\n\n  /**\n   * After `\\`, at eol.\n   *\n   * ```markdown\n   * > | a\\\n   *       ^\n   *   | b\n   * ```\n   *\n   *  @type {State}\n   */\n  function after(code) {\n    if (markdownLineEnding(code)) {\n      effects.exit(types.hardBreakEscape);\n      return ok(code);\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "markdownLineEnding", "codes", "types", "hardBreakEscape", "name", "tokenize", "tokenizeHardBreakEscape", "effects", "nok", "start", "code", "backslash", "enter", "consume", "after", "exit"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of a hard break (escape).\n   *\n   * ```markdown\n   * > | a\\\n   *      ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.hardBreakEscape)\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After `\\`, at eol.\n   *\n   * ```markdown\n   * > | a\\\n   *       ^\n   *   | b\n   * ```\n   *\n   *  @type {State}\n   */\n  function after(code) {\n    if (markdownLineEnding(code)) {\n      effects.exit(types.hardBreakEscape)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,KAAK,EAAEC,KAAK,QAAO,uBAAuB;;AAElD;AACA,OAAO,MAAMC,eAAe,GAAG;EAC7BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASA,uBAAuBA,CAACC,OAAO,EAAET,EAAE,EAAEU,GAAG,EAAE;EACjD,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBX,MAAM,CAACW,IAAI,KAAKT,KAAK,CAACU,SAAS,EAAE,eAAe,CAAC;IACjDJ,OAAO,CAACK,KAAK,CAACV,KAAK,CAACC,eAAe,CAAC;IACpCI,OAAO,CAACM,OAAO,CAACH,IAAI,CAAC;IACrB,OAAOI,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACJ,IAAI,EAAE;IACnB,IAAIV,kBAAkB,CAACU,IAAI,CAAC,EAAE;MAC5BH,OAAO,CAACQ,IAAI,CAACb,KAAK,CAACC,eAAe,CAAC;MACnC,OAAOL,EAAE,CAACY,IAAI,CAAC;IACjB;IAEA,OAAOF,GAAG,CAACE,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}