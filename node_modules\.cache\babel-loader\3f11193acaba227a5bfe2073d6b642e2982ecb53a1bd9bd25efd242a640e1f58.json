{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.camelCase = void 0;\nvar CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nvar HYPHEN_REGEX = /-([a-z])/g;\nvar NO_HYPHEN_REGEX = /^[^-]+$/;\nvar VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nvar MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n/**\n * Checks whether to skip camelCase.\n */\nvar skipCamelCase = function (property) {\n  return !property || NO_HYPHEN_REGEX.test(property) || CUSTOM_PROPERTY_REGEX.test(property);\n};\n/**\n * Replacer that capitalizes first character.\n */\nvar capitalize = function (match, character) {\n  return character.toUpperCase();\n};\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */\nvar trimHyphen = function (match, prefix) {\n  return \"\".concat(prefix, \"-\");\n};\n/**\n * CamelCases a CSS property.\n */\nvar camelCase = function (property, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (skipCamelCase(property)) {\n    return property;\n  }\n  property = property.toLowerCase();\n  if (options.reactCompat) {\n    // `-ms` vendor prefix should not be capitalized\n    property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n  } else {\n    // for non-React, remove first hyphen so vendor prefix is not capitalized\n    property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n  }\n  return property.replace(HYPHEN_REGEX, capitalize);\n};\nexports.camelCase = camelCase;", "map": {"version": 3, "names": ["CUSTOM_PROPERTY_REGEX", "HYPHEN_REGEX", "NO_HYPHEN_REGEX", "VENDOR_PREFIX_REGEX", "MS_VENDOR_PREFIX_REGEX", "skipCamelCase", "property", "test", "capitalize", "match", "character", "toUpperCase", "trimHyphen", "prefix", "concat", "camelCase", "options", "toLowerCase", "reactCompat", "replace", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\style-to-js\\src\\utilities.ts"], "sourcesContent": ["const CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nconst HYPHEN_REGEX = /-([a-z])/g;\nconst NO_HYPHEN_REGEX = /^[^-]+$/;\nconst VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nconst MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n\n/**\n * Checks whether to skip camelCase.\n */\nconst skipCamelCase = (property: string) =>\n  !property ||\n  NO_HYPHEN_REGEX.test(property) ||\n  CUSTOM_PROPERTY_REGEX.test(property);\n\n/**\n * Replacer that capitalizes first character.\n */\nconst capitalize = (match: string, character: string) =>\n  character.toUpperCase();\n\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */\nconst trimHyphen = (match: string, prefix: string) => `${prefix}-`;\n\n/**\n * CamelCase options.\n */\nexport interface CamelCaseOptions {\n  reactCompat?: boolean;\n}\n\n/**\n * CamelCases a CSS property.\n */\nexport const camelCase = (property: string, options: CamelCaseOptions = {}) => {\n  if (skipCamelCase(property)) {\n    return property;\n  }\n\n  property = property.toLowerCase();\n\n  if (options.reactCompat) {\n    // `-ms` vendor prefix should not be capitalized\n    property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n  } else {\n    // for non-React, remove first hyphen so vendor prefix is not capitalized\n    property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n  }\n\n  return property.replace(HYPHEN_REGEX, capitalize);\n};\n"], "mappings": ";;;;;;AAAA,IAAMA,qBAAqB,GAAG,oBAAoB;AAClD,IAAMC,YAAY,GAAG,WAAW;AAChC,IAAMC,eAAe,GAAG,SAAS;AACjC,IAAMC,mBAAmB,GAAG,4BAA4B;AACxD,IAAMC,sBAAsB,GAAG,SAAS;AAExC;;;AAGA,IAAMC,aAAa,GAAG,SAAAA,CAACC,QAAgB;EACrC,QAACA,QAAQ,IACTJ,eAAe,CAACK,IAAI,CAACD,QAAQ,CAAC,IAC9BN,qBAAqB,CAACO,IAAI,CAACD,QAAQ,CAAC;AAFpC,CAEoC;AAEtC;;;AAGA,IAAME,UAAU,GAAG,SAAAA,CAACC,KAAa,EAAEC,SAAiB;EAClD,OAAAA,SAAS,CAACC,WAAW,EAAE;AAAvB,CAAuB;AAEzB;;;AAGA,IAAMC,UAAU,GAAG,SAAAA,CAACH,KAAa,EAAEI,MAAc;EAAK,UAAAC,MAAA,CAAGD,MAAM,MAAG;AAAZ,CAAY;AASlE;;;AAGO,IAAME,SAAS,GAAG,SAAAA,CAACT,QAAgB,EAAEU,OAA8B;EAA9B,IAAAA,OAAA;IAAAA,OAAA,KAA8B;EAAA;EACxE,IAAIX,aAAa,CAACC,QAAQ,CAAC,EAAE;IAC3B,OAAOA,QAAQ;EACjB;EAEAA,QAAQ,GAAGA,QAAQ,CAACW,WAAW,EAAE;EAEjC,IAAID,OAAO,CAACE,WAAW,EAAE;IACvB;IACAZ,QAAQ,GAAGA,QAAQ,CAACa,OAAO,CAACf,sBAAsB,EAAEQ,UAAU,CAAC;EACjE,CAAC,MAAM;IACL;IACAN,QAAQ,GAAGA,QAAQ,CAACa,OAAO,CAAChB,mBAAmB,EAAES,UAAU,CAAC;EAC9D;EAEA,OAAON,QAAQ,CAACa,OAAO,CAAClB,YAAY,EAAEO,UAAU,CAAC;AACnD,CAAC;AAhBYY,OAAA,CAAAL,SAAS,GAAAA,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}