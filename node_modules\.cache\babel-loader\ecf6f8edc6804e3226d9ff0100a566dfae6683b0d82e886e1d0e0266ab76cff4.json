{"ast": null, "code": "export const validateParams = (userID, serviceID, templateID) => {\n  if (!userID) {\n    throw 'The user ID is required. Visit https://dashboard.emailjs.com/admin/integration';\n  }\n  if (!serviceID) {\n    throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n  }\n  if (!templateID) {\n    throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n  }\n  return true;\n};", "map": {"version": 3, "names": ["validateParams", "userID", "serviceID", "templateID"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/emailjs-com/es/utils/validateParams.js"], "sourcesContent": ["export const validateParams = (userID, serviceID, templateID) => {\n    if (!userID) {\n        throw 'The user ID is required. Visit https://dashboard.emailjs.com/admin/integration';\n    }\n    if (!serviceID) {\n        throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n    }\n    if (!templateID) {\n        throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n    }\n    return true;\n};\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,EAAEC,UAAU,KAAK;EAC7D,IAAI,CAACF,MAAM,EAAE;IACT,MAAM,gFAAgF;EAC1F;EACA,IAAI,CAACC,SAAS,EAAE;IACZ,MAAM,uEAAuE;EACjF;EACA,IAAI,CAACC,UAAU,EAAE;IACb,MAAM,kFAAkF;EAC5F;EACA,OAAO,IAAI;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}