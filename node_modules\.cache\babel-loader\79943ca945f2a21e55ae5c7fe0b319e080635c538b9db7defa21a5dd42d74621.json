{"ast": null, "code": "/**\n * @import {Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @typedef {import('micromark-util-types').Options} Options\n */\n\nimport { compile } from './lib/compile.js';\nimport { parse } from './lib/parse.js';\nimport { postprocess } from './lib/postprocess.js';\nimport { preprocess } from './lib/preprocess.js';\nexport { compile } from './lib/compile.js';\nexport { parse } from './lib/parse.js';\nexport { postprocess } from './lib/postprocess.js';\nexport { preprocess } from './lib/preprocess.js';\n\n/**\n * Compile markdown to HTML.\n *\n * > Note: which encodings are supported depends on the engine.\n * > For info on Node.js, see:\n * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n *\n * @overload\n * @param {Value} value\n *   Markdown to parse (`string` or `Uint8Array`).\n * @param {Encoding | null | undefined} encoding\n *   Character encoding to understand `value` as when it’s a `Uint8Array`\n *   (`string`, default: `'utf8'`).\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Compiled HTML.\n *\n * @overload\n * @param {Value} value\n *   Markdown to parse (`string` or `Uint8Array`).\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Compiled HTML.\n *\n * @param {Value} value\n *   Markdown to parse (`string` or `Uint8Array`).\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding to understand `value` as when it’s a `Uint8Array`\n *   (`string`, default: `'utf8'`).\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Compiled HTML.\n */\nexport function micromark(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding;\n    encoding = undefined;\n  }\n  return compile(options)(postprocess(parse(options).document().write(preprocess()(value, encoding, true))));\n}", "map": {"version": 3, "names": ["compile", "parse", "postprocess", "preprocess", "micromark", "value", "encoding", "options", "undefined", "document", "write"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark/dev/index.js"], "sourcesContent": ["/**\n * @import {Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @typedef {import('micromark-util-types').Options} Options\n */\n\nimport {compile} from './lib/compile.js'\nimport {parse} from './lib/parse.js'\nimport {postprocess} from './lib/postprocess.js'\nimport {preprocess} from './lib/preprocess.js'\n\nexport {compile} from './lib/compile.js'\nexport {parse} from './lib/parse.js'\nexport {postprocess} from './lib/postprocess.js'\nexport {preprocess} from './lib/preprocess.js'\n\n/**\n * Compile markdown to HTML.\n *\n * > Note: which encodings are supported depends on the engine.\n * > For info on Node.js, see:\n * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n *\n * @overload\n * @param {Value} value\n *   Markdown to parse (`string` or `Uint8Array`).\n * @param {Encoding | null | undefined} encoding\n *   Character encoding to understand `value` as when it’s a `Uint8Array`\n *   (`string`, default: `'utf8'`).\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Compiled HTML.\n *\n * @overload\n * @param {Value} value\n *   Markdown to parse (`string` or `Uint8Array`).\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Compiled HTML.\n *\n * @param {Value} value\n *   Markdown to parse (`string` or `Uint8Array`).\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding to understand `value` as when it’s a `Uint8Array`\n *   (`string`, default: `'utf8'`).\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Compiled HTML.\n */\nexport function micromark(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding\n    encoding = undefined\n  }\n\n  return compile(options)(\n    postprocess(\n      parse(options)\n        .document()\n        .write(preprocess()(value, encoding, true))\n    )\n  )\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAQA,OAAO,QAAO,kBAAkB;AACxC,SAAQC,KAAK,QAAO,gBAAgB;AACpC,SAAQC,WAAW,QAAO,sBAAsB;AAChD,SAAQC,UAAU,QAAO,qBAAqB;AAE9C,SAAQH,OAAO,QAAO,kBAAkB;AACxC,SAAQC,KAAK,QAAO,gBAAgB;AACpC,SAAQC,WAAW,QAAO,sBAAsB;AAChD,SAAQC,UAAU,QAAO,qBAAqB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAClD,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;IAChCC,OAAO,GAAGD,QAAQ;IAClBA,QAAQ,GAAGE,SAAS;EACtB;EAEA,OAAOR,OAAO,CAACO,OAAO,CAAC,CACrBL,WAAW,CACTD,KAAK,CAACM,OAAO,CAAC,CACXE,QAAQ,CAAC,CAAC,CACVC,KAAK,CAACP,UAAU,CAAC,CAAC,CAACE,KAAK,EAAEC,QAAQ,EAAE,IAAI,CAAC,CAC9C,CACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}