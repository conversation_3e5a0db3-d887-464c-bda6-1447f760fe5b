{"ast": null, "code": "import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\nconst findHTMLForm = form => {\n  let currentForm;\n  if (typeof form === 'string') {\n    currentForm = document.querySelector(form);\n  } else {\n    currentForm = form;\n  }\n  if (!currentForm || currentForm.nodeName !== 'FORM') {\n    throw 'The 3rd parameter is expected to be the HTML form element or the style selector of form';\n  }\n  return currentForm;\n};\n/**\n * Send a form the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {string | HTMLFormElement} form - the form element or selector\n * @param {string} userID - the EmailJS user ID\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const sendForm = (serviceID, templateID, form, userID) => {\n  const uID = userID || store._userID;\n  const currentForm = findHTMLForm(form);\n  validateParams(uID, serviceID, templateID);\n  const formData = new FormData(currentForm);\n  formData.append('lib_version', '3.2.0');\n  formData.append('service_id', serviceID);\n  formData.append('template_id', templateID);\n  formData.append('user_id', uID);\n  return sendPost('/api/v1.0/email/send-form', formData);\n};", "map": {"version": 3, "names": ["store", "validateParams", "sendPost", "findHTMLForm", "form", "currentForm", "document", "querySelector", "nodeName", "sendForm", "serviceID", "templateID", "userID", "uID", "_userID", "formData", "FormData", "append"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/emailjs-com/es/methods/sendForm/sendForm.js"], "sourcesContent": ["import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\nconst findHTMLForm = (form) => {\n    let currentForm;\n    if (typeof form === 'string') {\n        currentForm = document.querySelector(form);\n    }\n    else {\n        currentForm = form;\n    }\n    if (!currentForm || currentForm.nodeName !== 'FORM') {\n        throw 'The 3rd parameter is expected to be the HTML form element or the style selector of form';\n    }\n    return currentForm;\n};\n/**\n * Send a form the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {string | HTMLFormElement} form - the form element or selector\n * @param {string} userID - the EmailJS user ID\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const sendForm = (serviceID, templateID, form, userID) => {\n    const uID = userID || store._userID;\n    const currentForm = findHTMLForm(form);\n    validateParams(uID, serviceID, templateID);\n    const formData = new FormData(currentForm);\n    formData.append('lib_version', '3.2.0');\n    formData.append('service_id', serviceID);\n    formData.append('template_id', templateID);\n    formData.append('user_id', uID);\n    return sendPost('/api/v1.0/email/send-form', formData);\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,mBAAmB;AACzC,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,MAAMC,YAAY,GAAIC,IAAI,IAAK;EAC3B,IAAIC,WAAW;EACf,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAC1BC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAACH,IAAI,CAAC;EAC9C,CAAC,MACI;IACDC,WAAW,GAAGD,IAAI;EACtB;EACA,IAAI,CAACC,WAAW,IAAIA,WAAW,CAACG,QAAQ,KAAK,MAAM,EAAE;IACjD,MAAM,yFAAyF;EACnG;EACA,OAAOH,WAAW;AACtB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,QAAQ,GAAGA,CAACC,SAAS,EAAEC,UAAU,EAAEP,IAAI,EAAEQ,MAAM,KAAK;EAC7D,MAAMC,GAAG,GAAGD,MAAM,IAAIZ,KAAK,CAACc,OAAO;EACnC,MAAMT,WAAW,GAAGF,YAAY,CAACC,IAAI,CAAC;EACtCH,cAAc,CAACY,GAAG,EAAEH,SAAS,EAAEC,UAAU,CAAC;EAC1C,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAACX,WAAW,CAAC;EAC1CU,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC;EACvCF,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEP,SAAS,CAAC;EACxCK,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEN,UAAU,CAAC;EAC1CI,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEJ,GAAG,CAAC;EAC/B,OAAOX,QAAQ,CAAC,2BAA2B,EAAEa,QAAQ,CAAC;AAC1D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}