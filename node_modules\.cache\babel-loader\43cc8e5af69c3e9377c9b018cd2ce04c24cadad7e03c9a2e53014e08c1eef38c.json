{"ast": null, "code": "/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {InitialConstruct} */\nexport const content = {\n  tokenize: initializeContent\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(this.parser.constructs.contentInitial, afterContentStartConstruct, paragraphInitial);\n  /** @type {Token} */\n  let previous;\n  return contentStart;\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    assert(code === codes.eof || markdownLineEnding(code), 'expected eol or eof');\n    if (code === codes.eof) {\n      effects.consume(code);\n      return;\n    }\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return factorySpace(effects, contentStart, types.linePrefix);\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    assert(code !== codes.eof && !markdownLineEnding(code), 'expected anything other than a line ending or EOF');\n    effects.enter(types.paragraph);\n    return lineStart(code);\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(types.chunkText, {\n      contentType: constants.contentTypeText,\n      previous\n    });\n    if (previous) {\n      previous.next = token;\n    }\n    previous = token;\n    return data(code);\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === codes.eof) {\n      effects.exit(types.chunkText);\n      effects.exit(types.paragraph);\n      effects.consume(code);\n      return;\n    }\n    if (markdownLineEnding(code)) {\n      effects.consume(code);\n      effects.exit(types.chunkText);\n      return lineStart;\n    }\n\n    // Data.\n    effects.consume(code);\n    return data;\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factorySpace", "markdownLineEnding", "codes", "constants", "types", "content", "tokenize", "initializeContent", "effects", "contentStart", "attempt", "parser", "constructs", "contentInitial", "afterContentStartConstruct", "paragraphInitial", "previous", "code", "eof", "consume", "enter", "lineEnding", "exit", "linePrefix", "paragraph", "lineStart", "token", "chunkText", "contentType", "contentTypeText", "next", "data"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark/dev/lib/initialize/content.js"], "sourcesContent": ["/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const content = {tokenize: initializeContent}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  /** @type {Token} */\n  let previous\n\n  return contentStart\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, contentStart, types.linePrefix)\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected anything other than a line ending or EOF'\n    )\n    effects.enter(types.paragraph)\n    return lineStart(code)\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(types.chunkText, {\n      contentType: constants.contentTypeText,\n      previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n\n    return data(code)\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === codes.eof) {\n      effects.exit(types.chunkText)\n      effects.exit(types.paragraph)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      effects.exit(types.chunkText)\n      return lineStart\n    }\n\n    // Data.\n    effects.consume(code)\n    return data\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,OAAO,MAAMC,OAAO,GAAG;EAACC,QAAQ,EAAEC;AAAiB,CAAC;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAiBA,CAACC,OAAO,EAAE;EAClC,MAAMC,YAAY,GAAGD,OAAO,CAACE,OAAO,CAClC,IAAI,CAACC,MAAM,CAACC,UAAU,CAACC,cAAc,EACrCC,0BAA0B,EAC1BC,gBACF,CAAC;EACD;EACA,IAAIC,QAAQ;EAEZ,OAAOP,YAAY;;EAEnB;EACA,SAASK,0BAA0BA,CAACG,IAAI,EAAE;IACxClB,MAAM,CACJkB,IAAI,KAAKf,KAAK,CAACgB,GAAG,IAAIjB,kBAAkB,CAACgB,IAAI,CAAC,EAC9C,qBACF,CAAC;IAED,IAAIA,IAAI,KAAKf,KAAK,CAACgB,GAAG,EAAE;MACtBV,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;MACrB;IACF;IAEAT,OAAO,CAACY,KAAK,CAAChB,KAAK,CAACiB,UAAU,CAAC;IAC/Bb,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;IACrBT,OAAO,CAACc,IAAI,CAAClB,KAAK,CAACiB,UAAU,CAAC;IAC9B,OAAOrB,YAAY,CAACQ,OAAO,EAAEC,YAAY,EAAEL,KAAK,CAACmB,UAAU,CAAC;EAC9D;;EAEA;EACA,SAASR,gBAAgBA,CAACE,IAAI,EAAE;IAC9BlB,MAAM,CACJkB,IAAI,KAAKf,KAAK,CAACgB,GAAG,IAAI,CAACjB,kBAAkB,CAACgB,IAAI,CAAC,EAC/C,mDACF,CAAC;IACDT,OAAO,CAACY,KAAK,CAAChB,KAAK,CAACoB,SAAS,CAAC;IAC9B,OAAOC,SAAS,CAACR,IAAI,CAAC;EACxB;;EAEA;EACA,SAASQ,SAASA,CAACR,IAAI,EAAE;IACvB,MAAMS,KAAK,GAAGlB,OAAO,CAACY,KAAK,CAAChB,KAAK,CAACuB,SAAS,EAAE;MAC3CC,WAAW,EAAEzB,SAAS,CAAC0B,eAAe;MACtCb;IACF,CAAC,CAAC;IAEF,IAAIA,QAAQ,EAAE;MACZA,QAAQ,CAACc,IAAI,GAAGJ,KAAK;IACvB;IAEAV,QAAQ,GAAGU,KAAK;IAEhB,OAAOK,IAAI,CAACd,IAAI,CAAC;EACnB;;EAEA;EACA,SAASc,IAAIA,CAACd,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKf,KAAK,CAACgB,GAAG,EAAE;MACtBV,OAAO,CAACc,IAAI,CAAClB,KAAK,CAACuB,SAAS,CAAC;MAC7BnB,OAAO,CAACc,IAAI,CAAClB,KAAK,CAACoB,SAAS,CAAC;MAC7BhB,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;MACrB;IACF;IAEA,IAAIhB,kBAAkB,CAACgB,IAAI,CAAC,EAAE;MAC5BT,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;MACrBT,OAAO,CAACc,IAAI,CAAClB,KAAK,CAACuB,SAAS,CAAC;MAC7B,OAAOF,SAAS;IAClB;;IAEA;IACAjB,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;IACrB,OAAOc,IAAI;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}