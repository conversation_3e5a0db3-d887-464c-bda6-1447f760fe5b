{"ast": null, "code": "/**\n * @import {Info, Space} from 'property-information'\n */\n\n/**\n * @typedef Definition\n *   Definition of a schema.\n * @property {Record<string, string> | undefined} [attributes]\n *   Normalzed names to special attribute case.\n * @property {ReadonlyArray<string> | undefined} [mustUseProperty]\n *   Normalized names that must be set as properties.\n * @property {Record<string, number | null>} properties\n *   Property names to their types.\n * @property {Space | undefined} [space]\n *   Space.\n * @property {Transform} transform\n *   Transform a property name.\n */\n\n/**\n * @callback Transform\n *   Transform.\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Attribute.\n */\n\nimport { normalize } from '../normalize.js';\nimport { DefinedInfo } from './defined-info.js';\nimport { Schema } from './schema.js';\n\n/**\n * @param {Definition} definition\n *   Definition.\n * @returns {Schema}\n *   Schema.\n */\nexport function create(definition) {\n  /** @type {Record<string, Info>} */\n  const properties = {};\n  /** @type {Record<string, string>} */\n  const normals = {};\n  for (const [property, value] of Object.entries(definition.properties)) {\n    const info = new DefinedInfo(property, definition.transform(definition.attributes || {}, property), value, definition.space);\n    if (definition.mustUseProperty && definition.mustUseProperty.includes(property)) {\n      info.mustUseProperty = true;\n    }\n    properties[property] = info;\n    normals[normalize(property)] = property;\n    normals[normalize(info.attribute)] = property;\n  }\n  return new Schema(properties, normals, definition.space);\n}", "map": {"version": 3, "names": ["normalize", "DefinedInfo", "<PERSON><PERSON><PERSON>", "create", "definition", "properties", "normals", "property", "value", "Object", "entries", "info", "transform", "attributes", "space", "mustUseProperty", "includes", "attribute"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/property-information/lib/util/create.js"], "sourcesContent": ["/**\n * @import {Info, Space} from 'property-information'\n */\n\n/**\n * @typedef Definition\n *   Definition of a schema.\n * @property {Record<string, string> | undefined} [attributes]\n *   Normalzed names to special attribute case.\n * @property {ReadonlyArray<string> | undefined} [mustUseProperty]\n *   Normalized names that must be set as properties.\n * @property {Record<string, number | null>} properties\n *   Property names to their types.\n * @property {Space | undefined} [space]\n *   Space.\n * @property {Transform} transform\n *   Transform a property name.\n */\n\n/**\n * @callback Transform\n *   Transform.\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Attribute.\n */\n\nimport {normalize} from '../normalize.js'\nimport {DefinedInfo} from './defined-info.js'\nimport {Schema} from './schema.js'\n\n/**\n * @param {Definition} definition\n *   Definition.\n * @returns {Schema}\n *   Schema.\n */\nexport function create(definition) {\n  /** @type {Record<string, Info>} */\n  const properties = {}\n  /** @type {Record<string, string>} */\n  const normals = {}\n\n  for (const [property, value] of Object.entries(definition.properties)) {\n    const info = new DefinedInfo(\n      property,\n      definition.transform(definition.attributes || {}, property),\n      value,\n      definition.space\n    )\n\n    if (\n      definition.mustUseProperty &&\n      definition.mustUseProperty.includes(property)\n    ) {\n      info.mustUseProperty = true\n    }\n\n    properties[property] = info\n\n    normals[normalize(property)] = property\n    normals[normalize(info.attribute)] = property\n  }\n\n  return new Schema(properties, normals, definition.space)\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,SAAS,QAAO,iBAAiB;AACzC,SAAQC,WAAW,QAAO,mBAAmB;AAC7C,SAAQC,MAAM,QAAO,aAAa;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,UAAU,EAAE;EACjC;EACA,MAAMC,UAAU,GAAG,CAAC,CAAC;EACrB;EACA,MAAMC,OAAO,GAAG,CAAC,CAAC;EAElB,KAAK,MAAM,CAACC,QAAQ,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,UAAU,CAACC,UAAU,CAAC,EAAE;IACrE,MAAMM,IAAI,GAAG,IAAIV,WAAW,CAC1BM,QAAQ,EACRH,UAAU,CAACQ,SAAS,CAACR,UAAU,CAACS,UAAU,IAAI,CAAC,CAAC,EAAEN,QAAQ,CAAC,EAC3DC,KAAK,EACLJ,UAAU,CAACU,KACb,CAAC;IAED,IACEV,UAAU,CAACW,eAAe,IAC1BX,UAAU,CAACW,eAAe,CAACC,QAAQ,CAACT,QAAQ,CAAC,EAC7C;MACAI,IAAI,CAACI,eAAe,GAAG,IAAI;IAC7B;IAEAV,UAAU,CAACE,QAAQ,CAAC,GAAGI,IAAI;IAE3BL,OAAO,CAACN,SAAS,CAACO,QAAQ,CAAC,CAAC,GAAGA,QAAQ;IACvCD,OAAO,CAACN,SAAS,CAACW,IAAI,CAACM,SAAS,CAAC,CAAC,GAAGV,QAAQ;EAC/C;EAEA,OAAO,IAAIL,MAAM,CAACG,UAAU,EAAEC,OAAO,EAAEF,UAAU,CAACU,KAAK,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}