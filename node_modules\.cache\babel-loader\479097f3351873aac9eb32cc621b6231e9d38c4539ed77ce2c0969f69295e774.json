{"ast": null, "code": "import { getDefaultsFromPostinstall } from './postinstall.mjs';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\n */\nconst CONSTANTS = {\n  /**\n   * @define {boolean} Whether this is the client Node.js SDK.\n   */\n  NODE_CLIENT: false,\n  /**\n   * @define {boolean} Whether this is the Admin Node.js SDK.\n   */\n  NODE_ADMIN: false,\n  /**\n   * Firebase SDK Version\n   */\n  SDK_VERSION: '${JSCORE_VERSION}'\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Throws an error if the provided assertion is falsy\n */\nconst assert = function (assertion, message) {\n  if (!assertion) {\n    throw assertionError(message);\n  }\n};\n/**\n * Returns an Error object suitable for throwing.\n */\nconst assertionError = function (message) {\n  return new Error('Firebase Database (' + CONSTANTS.SDK_VERSION + ') INTERNAL ASSERT FAILED: ' + message);\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst stringToByteArray$1 = function (str) {\n  // TODO(user): Use native implementations if/when available\n  const out = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = c >> 6 | 192;\n      out[p++] = c & 63 | 128;\n    } else if ((c & 0xfc00) === 0xd800 && i + 1 < str.length && (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = c >> 18 | 240;\n      out[p++] = c >> 12 & 63 | 128;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    } else {\n      out[p++] = c >> 12 | 224;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    }\n  }\n  return out;\n};\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes) {\n  // TODO(user): Use native implementations if/when available\n  const out = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode((c1 & 31) << 6 | c2 & 63);\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u = ((c1 & 7) << 18 | (c2 & 63) << 12 | (c3 & 63) << 6 | c4 & 63) - 0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode((c1 & 15) << 12 | (c2 & 63) << 6 | c3 & 63);\n    }\n  }\n  return out.join('');\n};\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nconst base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input, webSafe) {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n    this.init_();\n    const byteToCharMap = webSafe ? this.byteToCharMapWebSafe_ : this.byteToCharMap_;\n    const output = [];\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n      const outByte1 = byte1 >> 2;\n      const outByte2 = (byte1 & 0x03) << 4 | byte2 >> 4;\n      let outByte3 = (byte2 & 0x0f) << 2 | byte3 >> 6;\n      let outByte4 = byte3 & 0x3f;\n      if (!haveByte3) {\n        outByte4 = 64;\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n      output.push(byteToCharMap[outByte1], byteToCharMap[outByte2], byteToCharMap[outByte3], byteToCharMap[outByte4]);\n    }\n    return output.join('');\n  },\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input, webSafe) {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray$1(input), webSafe);\n  },\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input, webSafe) {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input, webSafe) {\n    this.init_();\n    const charToByteMap = webSafe ? this.charToByteMapWebSafe_ : this.charToByteMap_;\n    const output = [];\n    for (let i = 0; i < input.length;) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n      const outByte1 = byte1 << 2 | byte2 >> 4;\n      output.push(outByte1);\n      if (byte3 !== 64) {\n        const outByte2 = byte2 << 4 & 0xf0 | byte3 >> 2;\n        output.push(outByte2);\n        if (byte4 !== 64) {\n          const outByte3 = byte3 << 6 & 0xc0 | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n    return output;\n  },\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n/**\n * An error encountered while decoding base64 string.\n */\nclass DecodeBase64StringError extends Error {\n  constructor() {\n    super(...arguments);\n    this.name = 'DecodeBase64StringError';\n  }\n}\n/**\n * URL-safe base64 encoding\n */\nconst base64Encode = function (str) {\n  const utf8Bytes = stringToByteArray$1(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nconst base64urlEncodeWithoutPadding = function (str) {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nconst base64Decode = function (str) {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nfunction deepCopy(value) {\n  return deepExtend(undefined, value);\n}\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nfunction deepExtend(target, source) {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source;\n      return new Date(dateValue.getTime());\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    target[prop] = deepExtend(target[prop], source[prop]);\n  }\n  return target;\n}\nfunction isValidKey(key) {\n  return key !== '__proto__';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nfunction getGlobal() {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst getDefaultsFromGlobal = () => getGlobal().__FIREBASE_DEFAULTS__;\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = () => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\nconst getDefaultsFromCookie = () => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nconst getDefaults = () => {\n  try {\n    return getDefaultsFromPostinstall() || getDefaultsFromGlobal() || getDefaultsFromEnvVariable() || getDefaultsFromCookie();\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nconst getDefaultEmulatorHost = productName => {\n  var _a, _b;\n  return (_b = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.emulatorHosts) === null || _b === void 0 ? void 0 : _b[productName];\n};\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nconst getDefaultEmulatorHostnameAndPort = productName => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nconst getDefaultAppConfig = () => {\n  var _a;\n  return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.config;\n};\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nconst getExperimentalSetting = name => {\n  var _a;\n  return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a[`_${name}`];\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Deferred {\n  constructor() {\n    this.reject = () => {};\n    this.resolve = () => {};\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve;\n      this.reject = reject;\n    });\n  }\n  /**\n   * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(callback) {\n    return (error, value) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n\n/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nfunction isCloudWorkstation(host) {\n  return host.endsWith('.cloudworkstations.dev');\n}\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nasync function pingServer(endpoint) {\n  const result = await fetch(endpoint, {\n    credentials: 'include'\n  });\n  return result.ok;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction createMockUserToken(token, projectId) {\n  if (token.uid) {\n    throw new Error('The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.');\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n  const payload = Object.assign({\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    }\n  }, token);\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [base64urlEncodeWithoutPadding(JSON.stringify(header)), base64urlEncodeWithoutPadding(JSON.stringify(payload)), signature].join('.');\n}\nconst emulatorStatus = {};\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary() {\n  const summary = {\n    prod: [],\n    emulator: []\n  };\n  for (const key of Object.keys(emulatorStatus)) {\n    if (emulatorStatus[key]) {\n      summary.emulator.push(key);\n    } else {\n      summary.prod.push(key);\n    }\n  }\n  return summary;\n}\nfunction getOrCreateEl(id) {\n  let parentDiv = document.getElementById(id);\n  let created = false;\n  if (!parentDiv) {\n    parentDiv = document.createElement('div');\n    parentDiv.setAttribute('id', id);\n    created = true;\n  }\n  return {\n    created,\n    element: parentDiv\n  };\n}\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nfunction updateEmulatorBanner(name, isRunningEmulator) {\n  if (typeof window === 'undefined' || typeof document === 'undefined' || !isCloudWorkstation(window.location.host) || emulatorStatus[name] === isRunningEmulator || emulatorStatus[name] ||\n  // If already set to use emulator, can't go back to prod.\n  previouslyDismissed) {\n    return;\n  }\n  emulatorStatus[name] = isRunningEmulator;\n  function prefixedId(id) {\n    return `__firebase__banner__${id}`;\n  }\n  const bannerId = '__firebase__banner';\n  const summary = getEmulatorSummary();\n  const showError = summary.prod.length > 0;\n  function tearDown() {\n    const element = document.getElementById(bannerId);\n    if (element) {\n      element.remove();\n    }\n  }\n  function setupBannerStyles(bannerEl) {\n    bannerEl.style.display = 'flex';\n    bannerEl.style.background = '#7faaf0';\n    bannerEl.style.position = 'fixed';\n    bannerEl.style.bottom = '5px';\n    bannerEl.style.left = '5px';\n    bannerEl.style.padding = '.5em';\n    bannerEl.style.borderRadius = '5px';\n    bannerEl.style.alignItems = 'center';\n  }\n  function setupIconStyles(prependIcon, iconId) {\n    prependIcon.setAttribute('width', '24');\n    prependIcon.setAttribute('id', iconId);\n    prependIcon.setAttribute('height', '24');\n    prependIcon.setAttribute('viewBox', '0 0 24 24');\n    prependIcon.setAttribute('fill', 'none');\n    prependIcon.style.marginLeft = '-6px';\n  }\n  function setupCloseBtn() {\n    const closeBtn = document.createElement('span');\n    closeBtn.style.cursor = 'pointer';\n    closeBtn.style.marginLeft = '16px';\n    closeBtn.style.fontSize = '24px';\n    closeBtn.innerHTML = ' &times;';\n    closeBtn.onclick = () => {\n      previouslyDismissed = true;\n      tearDown();\n    };\n    return closeBtn;\n  }\n  function setupLinkStyles(learnMoreLink, learnMoreId) {\n    learnMoreLink.setAttribute('id', learnMoreId);\n    learnMoreLink.innerText = 'Learn more';\n    learnMoreLink.href = 'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n    learnMoreLink.setAttribute('target', '__blank');\n    learnMoreLink.style.paddingLeft = '5px';\n    learnMoreLink.style.textDecoration = 'underline';\n  }\n  function setupDom() {\n    const banner = getOrCreateEl(bannerId);\n    const firebaseTextId = prefixedId('text');\n    const firebaseText = document.getElementById(firebaseTextId) || document.createElement('span');\n    const learnMoreId = prefixedId('learnmore');\n    const learnMoreLink = document.getElementById(learnMoreId) || document.createElement('a');\n    const prependIconId = prefixedId('preprendIcon');\n    const prependIcon = document.getElementById(prependIconId) || document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    if (banner.created) {\n      // update styles\n      const bannerEl = banner.element;\n      setupBannerStyles(bannerEl);\n      setupLinkStyles(learnMoreLink, learnMoreId);\n      const closeBtn = setupCloseBtn();\n      setupIconStyles(prependIcon, prependIconId);\n      bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n      document.body.appendChild(bannerEl);\n    }\n    if (showError) {\n      firebaseText.innerText = `Preview backend disconnected.`;\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n    } else {\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n      firebaseText.innerText = 'Preview backend running in this workspace.';\n    }\n    firebaseText.setAttribute('id', firebaseTextId);\n  }\n  if (document.readyState === 'loading') {\n    window.addEventListener('DOMContentLoaded', setupDom);\n  } else {\n    setupDom();\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nfunction getUA() {\n  if (typeof navigator !== 'undefined' && typeof navigator['userAgent'] === 'string') {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nfunction isMobileCordova() {\n  return typeof window !== 'undefined' &&\n  // @ts-ignore Setting up an broadly applicable index signature for Window\n  // just to deal with this case would probably be a bad idea.\n  !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) && /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA());\n}\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nfunction isNode() {\n  var _a;\n  const forceEnvironment = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n  try {\n    return Object.prototype.toString.call(global.process) === '[object process]';\n  } catch (e) {\n    return false;\n  }\n}\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nfunction isBrowser() {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n/**\n * Detect Web Worker context.\n */\nfunction isWebWorker() {\n  return typeof WorkerGlobalScope !== 'undefined' && typeof self !== 'undefined' && self instanceof WorkerGlobalScope;\n}\n/**\n * Detect Cloudflare Worker context.\n */\nfunction isCloudflareWorker() {\n  return typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers';\n}\nfunction isBrowserExtension() {\n  const runtime = typeof chrome === 'object' ? chrome.runtime : typeof browser === 'object' ? browser.runtime : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nfunction isReactNative() {\n  return typeof navigator === 'object' && navigator['product'] === 'ReactNative';\n}\n/** Detects Electron apps. */\nfunction isElectron() {\n  return getUA().indexOf('Electron/') >= 0;\n}\n/** Detects Internet Explorer. */\nfunction isIE() {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n/** Detects Universal Windows Platform apps. */\nfunction isUWP() {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nfunction isNodeSdk() {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n/** Returns true if we are running in Safari. */\nfunction isSafari() {\n  return !isNode() && !!navigator.userAgent && navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome');\n}\n/** Returns true if we are running in Safari or WebKit */\nfunction isSafariOrWebkit() {\n  return !isNode() && !!navigator.userAgent && (navigator.userAgent.includes('Safari') || navigator.userAgent.includes('WebKit')) && !navigator.userAgent.includes('Chrome');\n}\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nfunction isIndexedDBAvailable() {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nfunction validateIndexedDBOpenable() {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist = true;\n      const DB_CHECK_NAME = 'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n      request.onerror = () => {\n        var _a;\n        reject(((_a = request.error) === null || _a === void 0 ? void 0 : _a.message) || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nfunction areCookiesEnabled() {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\nconst ERROR_NAME = 'FirebaseError';\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nclass FirebaseError extends Error {\n  constructor(/** The error code for this error. */\n  code, message, /** Custom data for this error. */\n  customData) {\n    super(message);\n    this.code = code;\n    this.customData = customData;\n    /** The custom name for all FirebaseErrors. */\n    this.name = ERROR_NAME;\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\nclass ErrorFactory {\n  constructor(service, serviceName, errors) {\n    this.service = service;\n    this.serviceName = serviceName;\n    this.errors = errors;\n  }\n  create(code, ...data) {\n    const customData = data[0] || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n    return error;\n  }\n}\nfunction replaceTemplate(template, data) {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\nconst PATTERN = /\\{\\$([^}]+)}/g;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Evaluates a JSON string into a javascript object.\n *\n * @param {string} str A string containing JSON.\n * @return {*} The javascript object representing the specified JSON.\n */\nfunction jsonEval(str) {\n  return JSON.parse(str);\n}\n/**\n * Returns JSON representing a javascript object.\n * @param {*} data JavaScript object to be stringified.\n * @return {string} The JSON contents of the object.\n */\nfunction stringify(data) {\n  return JSON.stringify(data);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Decodes a Firebase auth. token into constituent parts.\n *\n * Notes:\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst decode = function (token) {\n  let header = {},\n    claims = {},\n    data = {},\n    signature = '';\n  try {\n    const parts = token.split('.');\n    header = jsonEval(base64Decode(parts[0]) || '');\n    claims = jsonEval(base64Decode(parts[1]) || '');\n    signature = parts[2];\n    data = claims['d'] || {};\n    delete claims['d'];\n  } catch (e) {}\n  return {\n    header,\n    claims,\n    data,\n    signature\n  };\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidTimestamp = function (token) {\n  const claims = decode(token).claims;\n  const now = Math.floor(new Date().getTime() / 1000);\n  let validSince = 0,\n    validUntil = 0;\n  if (typeof claims === 'object') {\n    if (claims.hasOwnProperty('nbf')) {\n      validSince = claims['nbf'];\n    } else if (claims.hasOwnProperty('iat')) {\n      validSince = claims['iat'];\n    }\n    if (claims.hasOwnProperty('exp')) {\n      validUntil = claims['exp'];\n    } else {\n      // token will expire after 24h by default\n      validUntil = validSince + 86400;\n    }\n  }\n  return !!now && !!validSince && !!validUntil && now >= validSince && now <= validUntil;\n};\n/**\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\n *\n * Notes:\n * - May return null if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst issuedAtTime = function (token) {\n  const claims = decode(token).claims;\n  if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\n    return claims['iat'];\n  }\n  return null;\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidFormat = function (token) {\n  const decoded = decode(token),\n    claims = decoded.claims;\n  return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\n};\n/**\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isAdmin = function (token) {\n  const claims = decode(token).claims;\n  return typeof claims === 'object' && claims['admin'] === true;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction contains(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction safeGet(obj, key) {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\nfunction isEmpty(obj) {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction map(obj, fn, contextObj) {\n  const res = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res;\n}\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n    const aProp = a[k];\n    const bProp = b[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isObject(thing) {\n  return thing !== null && typeof thing === 'object';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\n * @internal\n */\nfunction promiseWithTimeout(promise, timeInMS = 2000) {\n  const deferredPromise = new Deferred();\n  setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\n  promise.then(deferredPromise.resolve, deferredPromise.reject);\n  return deferredPromise.promise;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\n * params object (e.g. {arg: 'val', arg2: 'val2'})\n * Note: You must prepend it with ? when adding it to a URL.\n */\nfunction querystring(querystringParams) {\n  const params = [];\n  for (const [key, value] of Object.entries(querystringParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(arrayVal => {\n        params.push(encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal));\n      });\n    } else {\n      params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n    }\n  }\n  return params.length ? '&' + params.join('&') : '';\n}\n/**\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\n * (e.g. {arg: 'val', arg2: 'val2'})\n */\nfunction querystringDecode(querystring) {\n  const obj = {};\n  const tokens = querystring.replace(/^\\?/, '').split('&');\n  tokens.forEach(token => {\n    if (token) {\n      const [key, value] = token.split('=');\n      obj[decodeURIComponent(key)] = decodeURIComponent(value);\n    }\n  });\n  return obj;\n}\n/**\n * Extract the query string part of a URL, including the leading question mark (if present).\n */\nfunction extractQuerystring(url) {\n  const queryStart = url.indexOf('?');\n  if (!queryStart) {\n    return '';\n  }\n  const fragmentStart = url.indexOf('#', queryStart);\n  return url.substring(queryStart, fragmentStart > 0 ? fragmentStart : undefined);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview SHA-1 cryptographic hash.\n * Variable names follow the notation in FIPS PUB 180-3:\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\n *\n * Usage:\n *   var sha1 = new sha1();\n *   sha1.update(bytes);\n *   var hash = sha1.digest();\n *\n * Performance:\n *   Chrome 23:   ~400 Mbit/s\n *   Firefox 16:  ~250 Mbit/s\n *\n */\n/**\n * SHA-1 cryptographic hash constructor.\n *\n * The properties declared here are discussed in the above algorithm document.\n * @constructor\n * @final\n * @struct\n */\nclass Sha1 {\n  constructor() {\n    /**\n     * Holds the previous values of accumulated variables a-e in the compress_\n     * function.\n     * @private\n     */\n    this.chain_ = [];\n    /**\n     * A buffer holding the partially computed hash result.\n     * @private\n     */\n    this.buf_ = [];\n    /**\n     * An array of 80 bytes, each a part of the message to be hashed.  Referred to\n     * as the message schedule in the docs.\n     * @private\n     */\n    this.W_ = [];\n    /**\n     * Contains data needed to pad messages less than 64 bytes.\n     * @private\n     */\n    this.pad_ = [];\n    /**\n     * @private {number}\n     */\n    this.inbuf_ = 0;\n    /**\n     * @private {number}\n     */\n    this.total_ = 0;\n    this.blockSize = 512 / 8;\n    this.pad_[0] = 128;\n    for (let i = 1; i < this.blockSize; ++i) {\n      this.pad_[i] = 0;\n    }\n    this.reset();\n  }\n  reset() {\n    this.chain_[0] = 0x67452301;\n    this.chain_[1] = 0xefcdab89;\n    this.chain_[2] = 0x98badcfe;\n    this.chain_[3] = 0x10325476;\n    this.chain_[4] = 0xc3d2e1f0;\n    this.inbuf_ = 0;\n    this.total_ = 0;\n  }\n  /**\n   * Internal compress helper function.\n   * @param buf Block to compress.\n   * @param offset Offset of the block in the buffer.\n   * @private\n   */\n  compress_(buf, offset) {\n    if (!offset) {\n      offset = 0;\n    }\n    const W = this.W_;\n    // get 16 big endian words\n    if (typeof buf === 'string') {\n      for (let i = 0; i < 16; i++) {\n        // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\n        // have a bug that turns the post-increment ++ operator into pre-increment\n        // during JIT compilation.  We have code that depends heavily on SHA-1 for\n        // correctness and which is affected by this bug, so I've removed all uses\n        // of post-increment ++ in which the result value is used.  We can revert\n        // this change once the Safari bug\n        // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\n        // most clients have been updated.\n        W[i] = buf.charCodeAt(offset) << 24 | buf.charCodeAt(offset + 1) << 16 | buf.charCodeAt(offset + 2) << 8 | buf.charCodeAt(offset + 3);\n        offset += 4;\n      }\n    } else {\n      for (let i = 0; i < 16; i++) {\n        W[i] = buf[offset] << 24 | buf[offset + 1] << 16 | buf[offset + 2] << 8 | buf[offset + 3];\n        offset += 4;\n      }\n    }\n    // expand to 80 words\n    for (let i = 16; i < 80; i++) {\n      const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n      W[i] = (t << 1 | t >>> 31) & 0xffffffff;\n    }\n    let a = this.chain_[0];\n    let b = this.chain_[1];\n    let c = this.chain_[2];\n    let d = this.chain_[3];\n    let e = this.chain_[4];\n    let f, k;\n    // TODO(user): Try to unroll this loop to speed up the computation.\n    for (let i = 0; i < 80; i++) {\n      if (i < 40) {\n        if (i < 20) {\n          f = d ^ b & (c ^ d);\n          k = 0x5a827999;\n        } else {\n          f = b ^ c ^ d;\n          k = 0x6ed9eba1;\n        }\n      } else {\n        if (i < 60) {\n          f = b & c | d & (b | c);\n          k = 0x8f1bbcdc;\n        } else {\n          f = b ^ c ^ d;\n          k = 0xca62c1d6;\n        }\n      }\n      const t = (a << 5 | a >>> 27) + f + e + k + W[i] & 0xffffffff;\n      e = d;\n      d = c;\n      c = (b << 30 | b >>> 2) & 0xffffffff;\n      b = a;\n      a = t;\n    }\n    this.chain_[0] = this.chain_[0] + a & 0xffffffff;\n    this.chain_[1] = this.chain_[1] + b & 0xffffffff;\n    this.chain_[2] = this.chain_[2] + c & 0xffffffff;\n    this.chain_[3] = this.chain_[3] + d & 0xffffffff;\n    this.chain_[4] = this.chain_[4] + e & 0xffffffff;\n  }\n  update(bytes, length) {\n    // TODO(johnlenz): tighten the function signature and remove this check\n    if (bytes == null) {\n      return;\n    }\n    if (length === undefined) {\n      length = bytes.length;\n    }\n    const lengthMinusBlock = length - this.blockSize;\n    let n = 0;\n    // Using local instead of member variables gives ~5% speedup on Firefox 16.\n    const buf = this.buf_;\n    let inbuf = this.inbuf_;\n    // The outer while loop should execute at most twice.\n    while (n < length) {\n      // When we have no data in the block to top up, we can directly process the\n      // input buffer (assuming it contains sufficient data). This gives ~25%\n      // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\n      // the data is provided in large chunks (or in multiples of 64 bytes).\n      if (inbuf === 0) {\n        while (n <= lengthMinusBlock) {\n          this.compress_(bytes, n);\n          n += this.blockSize;\n        }\n      }\n      if (typeof bytes === 'string') {\n        while (n < length) {\n          buf[inbuf] = bytes.charCodeAt(n);\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      } else {\n        while (n < length) {\n          buf[inbuf] = bytes[n];\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      }\n    }\n    this.inbuf_ = inbuf;\n    this.total_ += length;\n  }\n  /** @override */\n  digest() {\n    const digest = [];\n    let totalBits = this.total_ * 8;\n    // Add pad 0x80 0x00*.\n    if (this.inbuf_ < 56) {\n      this.update(this.pad_, 56 - this.inbuf_);\n    } else {\n      this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\n    }\n    // Add # bits.\n    for (let i = this.blockSize - 1; i >= 56; i--) {\n      this.buf_[i] = totalBits & 255;\n      totalBits /= 256; // Don't use bit-shifting here!\n    }\n    this.compress_(this.buf_);\n    let n = 0;\n    for (let i = 0; i < 5; i++) {\n      for (let j = 24; j >= 0; j -= 8) {\n        digest[n] = this.chain_[i] >> j & 255;\n        ++n;\n      }\n    }\n    return digest;\n  }\n}\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nfunction createSubscribe(executor, onNoObservers) {\n  const proxy = new ObserverProxy(executor, onNoObservers);\n  return proxy.subscribe.bind(proxy);\n}\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy {\n  /**\n   * @param executor Function which can make calls to a single Observer\n   *     as a proxy.\n   * @param onNoObservers Callback when count of Observers goes to zero.\n   */\n  constructor(executor, onNoObservers) {\n    this.observers = [];\n    this.unsubscribes = [];\n    this.observerCount = 0;\n    // Micro-task scheduling by calling task.then().\n    this.task = Promise.resolve();\n    this.finalized = false;\n    this.onNoObservers = onNoObservers;\n    // Call the executor asynchronously so subscribers that are called\n    // synchronously after the creation of the subscribe function\n    // can still receive the very first value generated in the executor.\n    this.task.then(() => {\n      executor(this);\n    }).catch(e => {\n      this.error(e);\n    });\n  }\n  next(value) {\n    this.forEachObserver(observer => {\n      observer.next(value);\n    });\n  }\n  error(error) {\n    this.forEachObserver(observer => {\n      observer.error(error);\n    });\n    this.close(error);\n  }\n  complete() {\n    this.forEachObserver(observer => {\n      observer.complete();\n    });\n    this.close();\n  }\n  /**\n   * Subscribe function that can be used to add an Observer to the fan-out list.\n   *\n   * - We require that no event is sent to a subscriber synchronously to their\n   *   call to subscribe().\n   */\n  subscribe(nextOrObserver, error, complete) {\n    let observer;\n    if (nextOrObserver === undefined && error === undefined && complete === undefined) {\n      throw new Error('Missing Observer.');\n    }\n    // Assemble an Observer object when passed as callback functions.\n    if (implementsAnyMethods(nextOrObserver, ['next', 'error', 'complete'])) {\n      observer = nextOrObserver;\n    } else {\n      observer = {\n        next: nextOrObserver,\n        error,\n        complete\n      };\n    }\n    if (observer.next === undefined) {\n      observer.next = noop;\n    }\n    if (observer.error === undefined) {\n      observer.error = noop;\n    }\n    if (observer.complete === undefined) {\n      observer.complete = noop;\n    }\n    const unsub = this.unsubscribeOne.bind(this, this.observers.length);\n    // Attempt to subscribe to a terminated Observable - we\n    // just respond to the Observer with the final error or complete\n    // event.\n    if (this.finalized) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.task.then(() => {\n        try {\n          if (this.finalError) {\n            observer.error(this.finalError);\n          } else {\n            observer.complete();\n          }\n        } catch (e) {\n          // nothing\n        }\n        return;\n      });\n    }\n    this.observers.push(observer);\n    return unsub;\n  }\n  // Unsubscribe is synchronous - we guarantee that no events are sent to\n  // any unsubscribed Observer.\n  unsubscribeOne(i) {\n    if (this.observers === undefined || this.observers[i] === undefined) {\n      return;\n    }\n    delete this.observers[i];\n    this.observerCount -= 1;\n    if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n      this.onNoObservers(this);\n    }\n  }\n  forEachObserver(fn) {\n    if (this.finalized) {\n      // Already closed by previous event....just eat the additional values.\n      return;\n    }\n    // Since sendOne calls asynchronously - there is no chance that\n    // this.observers will become undefined.\n    for (let i = 0; i < this.observers.length; i++) {\n      this.sendOne(i, fn);\n    }\n  }\n  // Call the Observer via one of it's callback function. We are careful to\n  // confirm that the observe has not been unsubscribed since this asynchronous\n  // function had been queued.\n  sendOne(i, fn) {\n    // Execute the callback asynchronously\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      if (this.observers !== undefined && this.observers[i] !== undefined) {\n        try {\n          fn(this.observers[i]);\n        } catch (e) {\n          // Ignore exceptions raised in Observers or missing methods of an\n          // Observer.\n          // Log error to console. b/31404806\n          if (typeof console !== 'undefined' && console.error) {\n            console.error(e);\n          }\n        }\n      }\n    });\n  }\n  close(err) {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    if (err !== undefined) {\n      this.finalError = err;\n    }\n    // Proxy is no longer needed - garbage collect references\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      this.observers = undefined;\n      this.onNoObservers = undefined;\n    });\n  }\n}\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(fn, onError) {\n  return (...args) => {\n    Promise.resolve(true).then(() => {\n      fn(...args);\n    }).catch(error => {\n      if (onError) {\n        onError(error);\n      }\n    });\n  };\n}\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(obj, methods) {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n  for (const method of methods) {\n    if (method in obj && typeof obj[method] === 'function') {\n      return true;\n    }\n  }\n  return false;\n}\nfunction noop() {\n  // do nothing\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Check to make sure the appropriate number of arguments are provided for a public function.\n * Throws an error if it fails.\n *\n * @param fnName The function name\n * @param minCount The minimum number of arguments to allow for the function call\n * @param maxCount The maximum number of argument to allow for the function call\n * @param argCount The actual number of arguments provided.\n */\nconst validateArgCount = function (fnName, minCount, maxCount, argCount) {\n  let argError;\n  if (argCount < minCount) {\n    argError = 'at least ' + minCount;\n  } else if (argCount > maxCount) {\n    argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\n  }\n  if (argError) {\n    const error = fnName + ' failed: Was called with ' + argCount + (argCount === 1 ? ' argument.' : ' arguments.') + ' Expects ' + argError + '.';\n    throw new Error(error);\n  }\n};\n/**\n * Generates a string to prefix an error message about failed argument validation\n *\n * @param fnName The function name\n * @param argName The name of the argument\n * @return The prefix to add to the error thrown for validation.\n */\nfunction errorPrefix(fnName, argName) {\n  return `${fnName} failed: ${argName} argument `;\n}\n/**\n * @param fnName\n * @param argumentNumber\n * @param namespace\n * @param optional\n */\nfunction validateNamespace(fnName, namespace, optional) {\n  if (optional && !namespace) {\n    return;\n  }\n  if (typeof namespace !== 'string') {\n    //TODO: I should do more validation here. We only allow certain chars in namespaces.\n    throw new Error(errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.');\n  }\n}\nfunction validateCallback(fnName, argumentName,\n// eslint-disable-next-line @typescript-eslint/ban-types\ncallback, optional) {\n  if (optional && !callback) {\n    return;\n  }\n  if (typeof callback !== 'function') {\n    throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid function.');\n  }\n}\nfunction validateContextObject(fnName, argumentName, context, optional) {\n  if (optional && !context) {\n    return;\n  }\n  if (typeof context !== 'object' || context === null) {\n    throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid context object.');\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\n// so it's been modified.\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\n// use 2 characters in JavaScript.  All 4-byte UTF-8 characters begin with a first\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\n// pair).\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\n/**\n * @param {string} str\n * @return {Array}\n */\nconst stringToByteArray = function (str) {\n  const out = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    // Is this the lead surrogate in a surrogate pair?\n    if (c >= 0xd800 && c <= 0xdbff) {\n      const high = c - 0xd800; // the high 10 bits.\n      i++;\n      assert(i < str.length, 'Surrogate pair missing trail surrogate.');\n      const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\n      c = 0x10000 + (high << 10) + low;\n    }\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = c >> 6 | 192;\n      out[p++] = c & 63 | 128;\n    } else if (c < 65536) {\n      out[p++] = c >> 12 | 224;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    } else {\n      out[p++] = c >> 18 | 240;\n      out[p++] = c >> 12 & 63 | 128;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    }\n  }\n  return out;\n};\n/**\n * Calculate length without actually converting; useful for doing cheaper validation.\n * @param {string} str\n * @return {number}\n */\nconst stringLength = function (str) {\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i);\n    if (c < 128) {\n      p++;\n    } else if (c < 2048) {\n      p += 2;\n    } else if (c >= 0xd800 && c <= 0xdbff) {\n      // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\n      p += 4;\n      i++; // skip trail surrogate.\n    } else {\n      p += 3;\n    }\n  }\n  return p;\n};\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nconst MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nconst RANDOM_FACTOR = 0.5;\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nfunction calculateBackoffMillis(backoffCount, intervalMillis = DEFAULT_INTERVAL_MILLIS, backoffFactor = DEFAULT_BACKOFF_FACTOR) {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n  // A fraction of the backoff value to add/subtract.\n  // Deviation: changes multiplication order to improve readability.\n  RANDOM_FACTOR * currBaseValue * (\n  // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n  // if we add or subtract.\n  Math.random() - 0.5) * 2);\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provide English ordinal letters after a number\n */\nfunction ordinal(i) {\n  if (!Number.isFinite(i)) {\n    return `${i}`;\n  }\n  return i + indicator(i);\n}\nfunction indicator(i) {\n  i = Math.abs(i);\n  const cent = i % 100;\n  if (cent >= 10 && cent <= 20) {\n    return 'th';\n  }\n  const dec = i % 10;\n  if (dec === 1) {\n    return 'st';\n  }\n  if (dec === 2) {\n    return 'nd';\n  }\n  if (dec === 3) {\n    return 'rd';\n  }\n  return 'th';\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getModularInstance(service) {\n  if (service && service._delegate) {\n    return service._delegate;\n  } else {\n    return service;\n  }\n}\nexport { CONSTANTS, DecodeBase64StringError, Deferred, ErrorFactory, FirebaseError, MAX_VALUE_MILLIS, RANDOM_FACTOR, Sha1, areCookiesEnabled, assert, assertionError, async, base64, base64Decode, base64Encode, base64urlEncodeWithoutPadding, calculateBackoffMillis, contains, createMockUserToken, createSubscribe, decode, deepCopy, deepEqual, deepExtend, errorPrefix, extractQuerystring, getDefaultAppConfig, getDefaultEmulatorHost, getDefaultEmulatorHostnameAndPort, getDefaults, getExperimentalSetting, getGlobal, getModularInstance, getUA, isAdmin, isBrowser, isBrowserExtension, isCloudWorkstation, isCloudflareWorker, isElectron, isEmpty, isIE, isIndexedDBAvailable, isMobileCordova, isNode, isNodeSdk, isReactNative, isSafari, isSafariOrWebkit, isUWP, isValidFormat, isValidTimestamp, isWebWorker, issuedAtTime, jsonEval, map, ordinal, pingServer, promiseWithTimeout, querystring, querystringDecode, safeGet, stringLength, stringToByteArray, stringify, updateEmulatorBanner, validateArgCount, validateCallback, validateContextObject, validateIndexedDBOpenable, validateNamespace };", "map": {"version": 3, "names": ["CONSTANTS", "NODE_CLIENT", "NODE_ADMIN", "SDK_VERSION", "assert", "assertion", "message", "assertionError", "Error", "stringToByteArray$1", "stringToByteArray", "str", "out", "p", "i", "length", "c", "charCodeAt", "byteArrayToString", "bytes", "pos", "c1", "String", "fromCharCode", "c2", "c3", "c4", "u", "join", "base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "init_", "byteToCharMap", "output", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte1", "outByte2", "outByte3", "outByte4", "push", "encodeString", "btoa", "decodeString", "decodeStringToByteArray", "charToByteMap", "char<PERSON>t", "haveByte4", "byte4", "DecodeBase64StringError", "constructor", "name", "base64Encode", "utf8Bytes", "base64urlEncodeWithoutPadding", "replace", "base64Decode", "e", "console", "error", "deepCopy", "value", "deepExtend", "undefined", "target", "source", "Object", "Date", "dateValue", "getTime", "prop", "hasOwnProperty", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "getGlobal", "self", "window", "global", "getDefaultsFromGlobal", "__FIREBASE_DEFAULTS__", "getDefaultsFromEnvVariable", "process", "env", "defaultsJsonString", "JSON", "parse", "getDefaultsFromCookie", "document", "match", "cookie", "decoded", "getDefaults", "getDefaultsFromPostinstall", "info", "getDefaultEmulatorHost", "productName", "_a", "_b", "emulatorHosts", "getDefaultEmulatorHostnameAndPort", "host", "separatorIndex", "lastIndexOf", "port", "parseInt", "substring", "getDefaultAppConfig", "config", "getExperimentalSetting", "Deferred", "reject", "resolve", "promise", "Promise", "wrapCallback", "callback", "catch", "isCloudWorkstation", "endsWith", "pingServer", "endpoint", "result", "fetch", "credentials", "ok", "createMockUserToken", "token", "projectId", "uid", "header", "alg", "type", "project", "iat", "sub", "user_id", "payload", "assign", "iss", "aud", "exp", "auth_time", "firebase", "sign_in_provider", "identities", "signature", "stringify", "emulatorStatus", "getEmulatorSummary", "summary", "prod", "emulator", "keys", "getOrCreateEl", "id", "parentDiv", "getElementById", "created", "createElement", "setAttribute", "element", "previouslyDismissed", "updateEmulatorBanner", "isRunningEmulator", "location", "prefixedId", "bannerId", "showError", "tearDown", "remove", "setupBannerStyles", "bannerEl", "style", "display", "background", "position", "bottom", "left", "padding", "borderRadius", "alignItems", "setupIconStyles", "prependIcon", "iconId", "marginLeft", "setupCloseBtn", "closeBtn", "cursor", "fontSize", "innerHTML", "onclick", "setupLinkStyles", "learnMoreLink", "learnMoreId", "innerText", "href", "paddingLeft", "textDecoration", "setupDom", "banner", "firebaseTextId", "firebaseText", "prependIconId", "createElementNS", "append", "body", "append<PERSON><PERSON><PERSON>", "readyState", "addEventListener", "getUA", "navigator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test", "isNode", "forceEnvironment", "prototype", "toString", "call", "<PERSON><PERSON><PERSON><PERSON>", "isWebWorker", "WorkerGlobalScope", "isCloudflareWorker", "userAgent", "isBrowserExtension", "runtime", "chrome", "browser", "isReactNative", "isElectron", "indexOf", "isIE", "ua", "isUWP", "isNodeSdk", "<PERSON><PERSON><PERSON><PERSON>", "includes", "isSafariOrWebkit", "isIndexedDBAvailable", "indexedDB", "validateIndexedDBOpenable", "preExist", "DB_CHECK_NAME", "request", "open", "onsuccess", "close", "deleteDatabase", "onupgradeneeded", "onerror", "areCookiesEnabled", "cookieEnabled", "ERROR_NAME", "FirebaseError", "code", "customData", "setPrototypeOf", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "fullMessage", "PATTERN", "_", "jsonEval", "decode", "claims", "parts", "split", "isValidTimestamp", "now", "Math", "floor", "validSince", "validUntil", "issuedAtTime", "isValidFormat", "isAdmin", "contains", "obj", "safeGet", "isEmpty", "map", "fn", "contextObj", "res", "deepEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "k", "aProp", "bProp", "isObject", "thing", "promiseWithTimeout", "timeInMS", "deferred<PERSON><PERSON><PERSON>", "setTimeout", "then", "querystring", "querystringParams", "params", "entries", "for<PERSON>ach", "arrayVal", "encodeURIComponent", "querystringDecode", "tokens", "decodeURIComponent", "extractQuerystring", "url", "queryStart", "fragmentStart", "Sha1", "chain_", "buf_", "W_", "pad_", "inbuf_", "total_", "blockSize", "reset", "compress_", "buf", "offset", "W", "t", "d", "f", "update", "lengthMinusBlock", "n", "inbuf", "digest", "totalBits", "j", "createSubscribe", "executor", "onNoObservers", "proxy", "ObserverProxy", "subscribe", "bind", "observers", "unsubscribes", "observerCount", "task", "finalized", "next", "forEachObserver", "observer", "complete", "nextOrObserver", "implementsAnyMethods", "noop", "unsub", "unsubscribeOne", "finalError", "sendOne", "err", "async", "onError", "args", "methods", "method", "validateArgCount", "fnName", "minCount", "maxCount", "argCount", "arg<PERSON><PERSON>r", "errorPrefix", "argName", "validateNamespace", "namespace", "optional", "validate<PERSON><PERSON>back", "argumentName", "validateContextObject", "context", "high", "low", "stringLength", "DEFAULT_INTERVAL_MILLIS", "DEFAULT_BACKOFF_FACTOR", "MAX_VALUE_MILLIS", "RANDOM_FACTOR", "calculateBackoffMillis", "backoffCount", "<PERSON><PERSON><PERSON><PERSON>", "backoffFactor", "currBaseValue", "pow", "randomWait", "round", "random", "min", "ordinal", "Number", "isFinite", "indicator", "abs", "cent", "dec", "getModularInstance", "_delegate"], "sources": ["C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\constants.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\assert.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\crypt.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\deepCopy.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\global.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\defaults.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\deferred.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\url.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\emulator.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\environment.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\errors.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\json.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\jwt.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\obj.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\promise.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\query.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\sha1.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\subscribe.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\validation.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\utf8.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\exponential_backoff.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\formatters.ts", "C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@firebase\\util\\src\\compat.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\n */\n\nexport const CONSTANTS = {\n  /**\n   * @define {boolean} Whether this is the client Node.js SDK.\n   */\n  NODE_CLIENT: false,\n  /**\n   * @define {boolean} Whether this is the Admin Node.js SDK.\n   */\n  NODE_ADMIN: false,\n\n  /**\n   * Firebase SDK Version\n   */\n  SDK_VERSION: '${JSCORE_VERSION}'\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\n\n/**\n * Throws an error if the provided assertion is falsy\n */\nexport const assert = function (assertion: unknown, message: string): void {\n  if (!assertion) {\n    throw assertionError(message);\n  }\n};\n\n/**\n * Returns an Error object suitable for throwing.\n */\nexport const assertionError = function (message: string): Error {\n  return new Error(\n    'Firebase Database (' +\n      CONSTANTS.SDK_VERSION +\n      ') INTERNAL ASSERT FAILED: ' +\n      message\n  );\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nexport function deepCopy<T>(value: T): T {\n  return deepExtend(undefined, value) as T;\n}\n\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nexport function deepExtend(target: unknown, source: unknown): unknown {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source as Date;\n      return new Date(dateValue.getTime());\n\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    (target as Record<string, unknown>)[prop] = deepExtend(\n      (target as Record<string, unknown>)[prop],\n      (source as Record<string, unknown>)[prop]\n    );\n  }\n\n  return target;\n}\n\nfunction isValidKey(key: string): boolean {\n  return key !== '__proto__';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nexport function getGlobal(): typeof globalThis {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { getGlobal } from './global';\nimport { getDefaultsFromPostinstall } from './postinstall';\n\n/**\n * Keys for experimental properties on the `FirebaseDefaults` object.\n * @public\n */\nexport type ExperimentalKey = 'authTokenSyncURL' | 'authIdTokenMaxAge';\n\n/**\n * An object that can be injected into the environment as __FIREBASE_DEFAULTS__,\n * either as a property of globalThis, a shell environment variable, or a\n * cookie.\n *\n * This object can be used to automatically configure and initialize\n * a Firebase app as well as any emulators.\n *\n * @public\n */\nexport interface FirebaseDefaults {\n  config?: Record<string, string>;\n  emulatorHosts?: Record<string, string>;\n  _authTokenSyncURL?: string;\n  _authIdTokenMaxAge?: number;\n  /**\n   * Override Firebase's runtime environment detection and\n   * force the SDK to act as if it were in the specified environment.\n   */\n  forceEnvironment?: 'browser' | 'node';\n  [key: string]: unknown;\n}\n\ndeclare global {\n  // Need `var` for this to work.\n  // eslint-disable-next-line no-var\n  var __FIREBASE_DEFAULTS__: FirebaseDefaults | undefined;\n}\n\nconst getDefaultsFromGlobal = (): FirebaseDefaults | undefined =>\n  getGlobal().__FIREBASE_DEFAULTS__;\n\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = (): FirebaseDefaults | undefined => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\n\nconst getDefaultsFromCookie = (): FirebaseDefaults | undefined => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nexport const getDefaults = (): FirebaseDefaults | undefined => {\n  try {\n    return (\n      getDefaultsFromPostinstall() ||\n      getDefaultsFromGlobal() ||\n      getDefaultsFromEnvVariable() ||\n      getDefaultsFromCookie()\n    );\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nexport const getDefaultEmulatorHost = (\n  productName: string\n): string | undefined => getDefaults()?.emulatorHosts?.[productName];\n\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nexport const getDefaultEmulatorHostnameAndPort = (\n  productName: string\n): [hostname: string, port: number] | undefined => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nexport const getDefaultAppConfig = (): Record<string, string> | undefined =>\n  getDefaults()?.config;\n\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nexport const getExperimentalSetting = <T extends ExperimentalKey>(\n  name: T\n): FirebaseDefaults[`_${T}`] =>\n  getDefaults()?.[`_${name}`] as FirebaseDefaults[`_${T}`];\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class Deferred<R> {\n  promise: Promise<R>;\n  reject: (value?: unknown) => void = () => {};\n  resolve: (value?: unknown) => void = () => {};\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve as (value?: unknown) => void;\n      this.reject = reject as (value?: unknown) => void;\n    });\n  }\n\n  /**\n   * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(\n    callback?: (error?: unknown, value?: unknown) => void\n  ): (error: unknown, value?: unknown) => void {\n    return (error, value?) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nexport function isCloudWorkstation(host: string): boolean {\n  return host.endsWith('.cloudworkstations.dev');\n}\n\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nexport async function pingServer(endpoint: string): Promise<boolean> {\n  const result = await fetch(endpoint, {\n    credentials: 'include'\n  });\n  return result.ok;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64urlEncodeWithoutPadding } from './crypt';\nimport { isCloudWorkstation } from './url';\n\n// Firebase Auth tokens contain snake_case claims following the JWT standard / convention.\n/* eslint-disable camelcase */\n\nexport type FirebaseSignInProvider =\n  | 'custom'\n  | 'email'\n  | 'password'\n  | 'phone'\n  | 'anonymous'\n  | 'google.com'\n  | 'facebook.com'\n  | 'github.com'\n  | 'twitter.com'\n  | 'microsoft.com'\n  | 'apple.com';\n\ninterface FirebaseIdToken {\n  // Always set to https://securetoken.google.com/PROJECT_ID\n  iss: string;\n\n  // Always set to PROJECT_ID\n  aud: string;\n\n  // The user's unique ID\n  sub: string;\n\n  // The token issue time, in seconds since epoch\n  iat: number;\n\n  // The token expiry time, normally 'iat' + 3600\n  exp: number;\n\n  // The user's unique ID. Must be equal to 'sub'\n  user_id: string;\n\n  // The time the user authenticated, normally 'iat'\n  auth_time: number;\n\n  // The sign in provider, only set when the provider is 'anonymous'\n  provider_id?: 'anonymous';\n\n  // The user's primary email\n  email?: string;\n\n  // The user's email verification status\n  email_verified?: boolean;\n\n  // The user's primary phone number\n  phone_number?: string;\n\n  // The user's display name\n  name?: string;\n\n  // The user's profile photo URL\n  picture?: string;\n\n  // Information on all identities linked to this user\n  firebase: {\n    // The primary sign-in provider\n    sign_in_provider: FirebaseSignInProvider;\n\n    // A map of providers to the user's list of unique identifiers from\n    // each provider\n    identities?: { [provider in FirebaseSignInProvider]?: string[] };\n  };\n\n  // Custom claims set by the developer\n  [claim: string]: unknown;\n\n  uid?: never; // Try to catch a common mistake of \"uid\" (should be \"sub\" instead).\n}\n\nexport type EmulatorMockTokenOptions = ({ user_id: string } | { sub: string }) &\n  Partial<FirebaseIdToken>;\n\nexport function createMockUserToken(\n  token: EmulatorMockTokenOptions,\n  projectId?: string\n): string {\n  if (token.uid) {\n    throw new Error(\n      'The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.'\n    );\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n\n  const payload: FirebaseIdToken = {\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    },\n\n    // Override with user options\n    ...token\n  };\n\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [\n    base64urlEncodeWithoutPadding(JSON.stringify(header)),\n    base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n    signature\n  ].join('.');\n}\n\ninterface EmulatorStatusMap {\n  [name: string]: boolean;\n}\nconst emulatorStatus: EmulatorStatusMap = {};\n\ninterface EmulatorSummary {\n  prod: string[];\n  emulator: string[];\n}\n\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary(): EmulatorSummary {\n  const summary: EmulatorSummary = {\n    prod: [],\n    emulator: []\n  };\n  for (const key of Object.keys(emulatorStatus)) {\n    if (emulatorStatus[key]) {\n      summary.emulator.push(key);\n    } else {\n      summary.prod.push(key);\n    }\n  }\n  return summary;\n}\n\nfunction getOrCreateEl(id: string): { created: boolean; element: HTMLElement } {\n  let parentDiv = document.getElementById(id);\n  let created = false;\n  if (!parentDiv) {\n    parentDiv = document.createElement('div');\n    parentDiv.setAttribute('id', id);\n    created = true;\n  }\n  return { created, element: parentDiv };\n}\n\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nexport function updateEmulatorBanner(\n  name: string,\n  isRunningEmulator: boolean\n): void {\n  if (\n    typeof window === 'undefined' ||\n    typeof document === 'undefined' ||\n    !isCloudWorkstation(window.location.host) ||\n    emulatorStatus[name] === isRunningEmulator ||\n    emulatorStatus[name] || // If already set to use emulator, can't go back to prod.\n    previouslyDismissed\n  ) {\n    return;\n  }\n\n  emulatorStatus[name] = isRunningEmulator;\n\n  function prefixedId(id: string): string {\n    return `__firebase__banner__${id}`;\n  }\n  const bannerId = '__firebase__banner';\n  const summary = getEmulatorSummary();\n  const showError = summary.prod.length > 0;\n\n  function tearDown(): void {\n    const element = document.getElementById(bannerId);\n    if (element) {\n      element.remove();\n    }\n  }\n\n  function setupBannerStyles(bannerEl: HTMLElement): void {\n    bannerEl.style.display = 'flex';\n    bannerEl.style.background = '#7faaf0';\n    bannerEl.style.position = 'fixed';\n    bannerEl.style.bottom = '5px';\n    bannerEl.style.left = '5px';\n    bannerEl.style.padding = '.5em';\n    bannerEl.style.borderRadius = '5px';\n    bannerEl.style.alignItems = 'center';\n  }\n\n  function setupIconStyles(prependIcon: SVGElement, iconId: string): void {\n    prependIcon.setAttribute('width', '24');\n    prependIcon.setAttribute('id', iconId);\n    prependIcon.setAttribute('height', '24');\n    prependIcon.setAttribute('viewBox', '0 0 24 24');\n    prependIcon.setAttribute('fill', 'none');\n    prependIcon.style.marginLeft = '-6px';\n  }\n\n  function setupCloseBtn(): HTMLSpanElement {\n    const closeBtn = document.createElement('span');\n    closeBtn.style.cursor = 'pointer';\n    closeBtn.style.marginLeft = '16px';\n    closeBtn.style.fontSize = '24px';\n    closeBtn.innerHTML = ' &times;';\n    closeBtn.onclick = () => {\n      previouslyDismissed = true;\n      tearDown();\n    };\n    return closeBtn;\n  }\n\n  function setupLinkStyles(\n    learnMoreLink: HTMLAnchorElement,\n    learnMoreId: string\n  ): void {\n    learnMoreLink.setAttribute('id', learnMoreId);\n    learnMoreLink.innerText = 'Learn more';\n    learnMoreLink.href =\n      'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n    learnMoreLink.setAttribute('target', '__blank');\n    learnMoreLink.style.paddingLeft = '5px';\n    learnMoreLink.style.textDecoration = 'underline';\n  }\n\n  function setupDom(): void {\n    const banner = getOrCreateEl(bannerId);\n    const firebaseTextId = prefixedId('text');\n    const firebaseText: HTMLSpanElement =\n      document.getElementById(firebaseTextId) || document.createElement('span');\n    const learnMoreId = prefixedId('learnmore');\n    const learnMoreLink: HTMLAnchorElement =\n      (document.getElementById(learnMoreId) as HTMLAnchorElement) ||\n      document.createElement('a');\n    const prependIconId = prefixedId('preprendIcon');\n    const prependIcon: SVGElement =\n      (document.getElementById(\n        prependIconId\n      ) as HTMLOrSVGElement as SVGElement) ||\n      document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    if (banner.created) {\n      // update styles\n      const bannerEl = banner.element;\n      setupBannerStyles(bannerEl);\n      setupLinkStyles(learnMoreLink, learnMoreId);\n      const closeBtn = setupCloseBtn();\n      setupIconStyles(prependIcon, prependIconId);\n      bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n      document.body.appendChild(bannerEl);\n    }\n\n    if (showError) {\n      firebaseText.innerText = `Preview backend disconnected.`;\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n    } else {\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n      firebaseText.innerText = 'Preview backend running in this workspace.';\n    }\n    firebaseText.setAttribute('id', firebaseTextId);\n  }\n  if (document.readyState === 'loading') {\n    window.addEventListener('DOMContentLoaded', setupDom);\n  } else {\n    setupDom();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/** Returns true if we are running in Safari or WebKit */\nexport function isSafariOrWebkit(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    (navigator.userAgent.includes('Safari') ||\n      navigator.userAgent.includes('WebKit')) &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Evaluates a JSON string into a javascript object.\n *\n * @param {string} str A string containing JSON.\n * @return {*} The javascript object representing the specified JSON.\n */\nexport function jsonEval(str: string): unknown {\n  return JSON.parse(str);\n}\n\n/**\n * Returns JSON representing a javascript object.\n * @param {*} data JavaScript object to be stringified.\n * @return {string} The JSON contents of the object.\n */\nexport function stringify(data: unknown): string {\n  return JSON.stringify(data);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { jsonEval } from './json';\n\ninterface Claims {\n  [key: string]: {};\n}\n\ninterface DecodedToken {\n  header: object;\n  claims: Claims;\n  data: object;\n  signature: string;\n}\n\n/**\n * Decodes a Firebase auth. token into constituent parts.\n *\n * Notes:\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const decode = function (token: string): DecodedToken {\n  let header = {},\n    claims: Claims = {},\n    data = {},\n    signature = '';\n\n  try {\n    const parts = token.split('.');\n    header = jsonEval(base64Decode(parts[0]) || '') as object;\n    claims = jsonEval(base64Decode(parts[1]) || '') as Claims;\n    signature = parts[2];\n    data = claims['d'] || {};\n    delete claims['d'];\n  } catch (e) {}\n\n  return {\n    header,\n    claims,\n    data,\n    signature\n  };\n};\n\ninterface DecodedToken {\n  header: object;\n  claims: Claims;\n  data: object;\n  signature: string;\n}\n\n/**\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const isValidTimestamp = function (token: string): boolean {\n  const claims: Claims = decode(token).claims;\n  const now: number = Math.floor(new Date().getTime() / 1000);\n  let validSince: number = 0,\n    validUntil: number = 0;\n\n  if (typeof claims === 'object') {\n    if (claims.hasOwnProperty('nbf')) {\n      validSince = claims['nbf'] as number;\n    } else if (claims.hasOwnProperty('iat')) {\n      validSince = claims['iat'] as number;\n    }\n\n    if (claims.hasOwnProperty('exp')) {\n      validUntil = claims['exp'] as number;\n    } else {\n      // token will expire after 24h by default\n      validUntil = validSince + 86400;\n    }\n  }\n\n  return (\n    !!now &&\n    !!validSince &&\n    !!validUntil &&\n    now >= validSince &&\n    now <= validUntil\n  );\n};\n\n/**\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\n *\n * Notes:\n * - May return null if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const issuedAtTime = function (token: string): number | null {\n  const claims: Claims = decode(token).claims;\n  if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\n    return claims['iat'] as number;\n  }\n  return null;\n};\n\n/**\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const isValidFormat = function (token: string): boolean {\n  const decoded = decode(token),\n    claims = decoded.claims;\n\n  return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\n};\n\n/**\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const isAdmin = function (token: string): boolean {\n  const claims: Claims = decode(token).claims;\n  return typeof claims === 'object' && claims['admin'] === true;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function contains<T extends object>(obj: T, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function safeGet<T extends object, K extends keyof T>(\n  obj: T,\n  key: K\n): T[K] | undefined {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\n\nexport function isEmpty(obj: object): obj is {} {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function map<K extends string, V, U>(\n  obj: { [key in K]: V },\n  fn: (value: V, key: K, obj: { [key in K]: V }) => U,\n  contextObj?: unknown\n): { [key in K]: U } {\n  const res: Partial<{ [key in K]: U }> = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res as { [key in K]: U };\n}\n\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nexport function deepEqual(a: object, b: object): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n\n    const aProp = (a as Record<string, unknown>)[k];\n    const bProp = (b as Record<string, unknown>)[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isObject(thing: unknown): thing is object {\n  return thing !== null && typeof thing === 'object';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from './deferred';\n\n/**\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\n * @internal\n */\nexport function promiseWithTimeout<T>(\n  promise: Promise<T>,\n  timeInMS = 2000\n): Promise<T> {\n  const deferredPromise = new Deferred<T>();\n  setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\n  promise.then(deferredPromise.resolve, deferredPromise.reject);\n  return deferredPromise.promise;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\n * params object (e.g. {arg: 'val', arg2: 'val2'})\n * Note: You must prepend it with ? when adding it to a URL.\n */\nexport function querystring(querystringParams: {\n  [key: string]: string | number;\n}): string {\n  const params = [];\n  for (const [key, value] of Object.entries(querystringParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(arrayVal => {\n        params.push(\n          encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal)\n        );\n      });\n    } else {\n      params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n    }\n  }\n  return params.length ? '&' + params.join('&') : '';\n}\n\n/**\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\n * (e.g. {arg: 'val', arg2: 'val2'})\n */\nexport function querystringDecode(querystring: string): Record<string, string> {\n  const obj: Record<string, string> = {};\n  const tokens = querystring.replace(/^\\?/, '').split('&');\n\n  tokens.forEach(token => {\n    if (token) {\n      const [key, value] = token.split('=');\n      obj[decodeURIComponent(key)] = decodeURIComponent(value);\n    }\n  });\n  return obj;\n}\n\n/**\n * Extract the query string part of a URL, including the leading question mark (if present).\n */\nexport function extractQuerystring(url: string): string {\n  const queryStart = url.indexOf('?');\n  if (!queryStart) {\n    return '';\n  }\n  const fragmentStart = url.indexOf('#', queryStart);\n  return url.substring(\n    queryStart,\n    fragmentStart > 0 ? fragmentStart : undefined\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview SHA-1 cryptographic hash.\n * Variable names follow the notation in FIPS PUB 180-3:\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\n *\n * Usage:\n *   var sha1 = new sha1();\n *   sha1.update(bytes);\n *   var hash = sha1.digest();\n *\n * Performance:\n *   Chrome 23:   ~400 Mbit/s\n *   Firefox 16:  ~250 Mbit/s\n *\n */\n\n/**\n * SHA-1 cryptographic hash constructor.\n *\n * The properties declared here are discussed in the above algorithm document.\n * @constructor\n * @final\n * @struct\n */\nexport class Sha1 {\n  /**\n   * Holds the previous values of accumulated variables a-e in the compress_\n   * function.\n   * @private\n   */\n  private chain_: number[] = [];\n\n  /**\n   * A buffer holding the partially computed hash result.\n   * @private\n   */\n  private buf_: number[] = [];\n\n  /**\n   * An array of 80 bytes, each a part of the message to be hashed.  Referred to\n   * as the message schedule in the docs.\n   * @private\n   */\n  private W_: number[] = [];\n\n  /**\n   * Contains data needed to pad messages less than 64 bytes.\n   * @private\n   */\n  private pad_: number[] = [];\n\n  /**\n   * @private {number}\n   */\n  private inbuf_: number = 0;\n\n  /**\n   * @private {number}\n   */\n  private total_: number = 0;\n\n  blockSize: number;\n\n  constructor() {\n    this.blockSize = 512 / 8;\n\n    this.pad_[0] = 128;\n    for (let i = 1; i < this.blockSize; ++i) {\n      this.pad_[i] = 0;\n    }\n\n    this.reset();\n  }\n\n  reset(): void {\n    this.chain_[0] = 0x67452301;\n    this.chain_[1] = 0xefcdab89;\n    this.chain_[2] = 0x98badcfe;\n    this.chain_[3] = 0x10325476;\n    this.chain_[4] = 0xc3d2e1f0;\n\n    this.inbuf_ = 0;\n    this.total_ = 0;\n  }\n\n  /**\n   * Internal compress helper function.\n   * @param buf Block to compress.\n   * @param offset Offset of the block in the buffer.\n   * @private\n   */\n  compress_(buf: number[] | Uint8Array | string, offset?: number): void {\n    if (!offset) {\n      offset = 0;\n    }\n\n    const W = this.W_;\n\n    // get 16 big endian words\n    if (typeof buf === 'string') {\n      for (let i = 0; i < 16; i++) {\n        // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\n        // have a bug that turns the post-increment ++ operator into pre-increment\n        // during JIT compilation.  We have code that depends heavily on SHA-1 for\n        // correctness and which is affected by this bug, so I've removed all uses\n        // of post-increment ++ in which the result value is used.  We can revert\n        // this change once the Safari bug\n        // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\n        // most clients have been updated.\n        W[i] =\n          (buf.charCodeAt(offset) << 24) |\n          (buf.charCodeAt(offset + 1) << 16) |\n          (buf.charCodeAt(offset + 2) << 8) |\n          buf.charCodeAt(offset + 3);\n        offset += 4;\n      }\n    } else {\n      for (let i = 0; i < 16; i++) {\n        W[i] =\n          (buf[offset] << 24) |\n          (buf[offset + 1] << 16) |\n          (buf[offset + 2] << 8) |\n          buf[offset + 3];\n        offset += 4;\n      }\n    }\n\n    // expand to 80 words\n    for (let i = 16; i < 80; i++) {\n      const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n      W[i] = ((t << 1) | (t >>> 31)) & 0xffffffff;\n    }\n\n    let a = this.chain_[0];\n    let b = this.chain_[1];\n    let c = this.chain_[2];\n    let d = this.chain_[3];\n    let e = this.chain_[4];\n    let f, k;\n\n    // TODO(user): Try to unroll this loop to speed up the computation.\n    for (let i = 0; i < 80; i++) {\n      if (i < 40) {\n        if (i < 20) {\n          f = d ^ (b & (c ^ d));\n          k = 0x5a827999;\n        } else {\n          f = b ^ c ^ d;\n          k = 0x6ed9eba1;\n        }\n      } else {\n        if (i < 60) {\n          f = (b & c) | (d & (b | c));\n          k = 0x8f1bbcdc;\n        } else {\n          f = b ^ c ^ d;\n          k = 0xca62c1d6;\n        }\n      }\n\n      const t = (((a << 5) | (a >>> 27)) + f + e + k + W[i]) & 0xffffffff;\n      e = d;\n      d = c;\n      c = ((b << 30) | (b >>> 2)) & 0xffffffff;\n      b = a;\n      a = t;\n    }\n\n    this.chain_[0] = (this.chain_[0] + a) & 0xffffffff;\n    this.chain_[1] = (this.chain_[1] + b) & 0xffffffff;\n    this.chain_[2] = (this.chain_[2] + c) & 0xffffffff;\n    this.chain_[3] = (this.chain_[3] + d) & 0xffffffff;\n    this.chain_[4] = (this.chain_[4] + e) & 0xffffffff;\n  }\n\n  update(bytes?: number[] | Uint8Array | string, length?: number): void {\n    // TODO(johnlenz): tighten the function signature and remove this check\n    if (bytes == null) {\n      return;\n    }\n\n    if (length === undefined) {\n      length = bytes.length;\n    }\n\n    const lengthMinusBlock = length - this.blockSize;\n    let n = 0;\n    // Using local instead of member variables gives ~5% speedup on Firefox 16.\n    const buf = this.buf_;\n    let inbuf = this.inbuf_;\n\n    // The outer while loop should execute at most twice.\n    while (n < length) {\n      // When we have no data in the block to top up, we can directly process the\n      // input buffer (assuming it contains sufficient data). This gives ~25%\n      // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\n      // the data is provided in large chunks (or in multiples of 64 bytes).\n      if (inbuf === 0) {\n        while (n <= lengthMinusBlock) {\n          this.compress_(bytes, n);\n          n += this.blockSize;\n        }\n      }\n\n      if (typeof bytes === 'string') {\n        while (n < length) {\n          buf[inbuf] = bytes.charCodeAt(n);\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      } else {\n        while (n < length) {\n          buf[inbuf] = bytes[n];\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      }\n    }\n\n    this.inbuf_ = inbuf;\n    this.total_ += length;\n  }\n\n  /** @override */\n  digest(): number[] {\n    const digest: number[] = [];\n    let totalBits = this.total_ * 8;\n\n    // Add pad 0x80 0x00*.\n    if (this.inbuf_ < 56) {\n      this.update(this.pad_, 56 - this.inbuf_);\n    } else {\n      this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\n    }\n\n    // Add # bits.\n    for (let i = this.blockSize - 1; i >= 56; i--) {\n      this.buf_[i] = totalBits & 255;\n      totalBits /= 256; // Don't use bit-shifting here!\n    }\n\n    this.compress_(this.buf_);\n\n    let n = 0;\n    for (let i = 0; i < 5; i++) {\n      for (let j = 24; j >= 0; j -= 8) {\n        digest[n] = (this.chain_[i] >> j) & 255;\n        ++n;\n      }\n    }\n    return digest;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport type NextFn<T> = (value: T) => void;\nexport type ErrorFn = (error: Error) => void;\nexport type CompleteFn = () => void;\n\nexport interface Observer<T> {\n  // Called once for each value in a stream of values.\n  next: NextFn<T>;\n\n  // A stream terminates by a single call to EITHER error() or complete().\n  error: ErrorFn;\n\n  // No events will be sent to next() once complete() is called.\n  complete: CompleteFn;\n}\n\nexport type PartialObserver<T> = Partial<Observer<T>>;\n\n// TODO: Support also Unsubscribe.unsubscribe?\nexport type Unsubscribe = () => void;\n\n/**\n * The Subscribe interface has two forms - passing the inline function\n * callbacks, or a object interface with callback properties.\n */\nexport interface Subscribe<T> {\n  (next?: NextFn<T>, error?: ErrorFn, complete?: CompleteFn): Unsubscribe;\n  (observer: PartialObserver<T>): Unsubscribe;\n}\n\nexport interface Observable<T> {\n  // Subscribe method\n  subscribe: Subscribe<T>;\n}\n\nexport type Executor<T> = (observer: Observer<T>) => void;\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nexport function createSubscribe<T>(\n  executor: Executor<T>,\n  onNoObservers?: Executor<T>\n): Subscribe<T> {\n  const proxy = new ObserverProxy<T>(executor, onNoObservers);\n  return proxy.subscribe.bind(proxy);\n}\n\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy<T> implements Observer<T> {\n  private observers: Array<Observer<T>> | undefined = [];\n  private unsubscribes: Unsubscribe[] = [];\n  private onNoObservers: Executor<T> | undefined;\n  private observerCount = 0;\n  // Micro-task scheduling by calling task.then().\n  private task = Promise.resolve();\n  private finalized = false;\n  private finalError?: Error;\n\n  /**\n   * @param executor Function which can make calls to a single Observer\n   *     as a proxy.\n   * @param onNoObservers Callback when count of Observers goes to zero.\n   */\n  constructor(executor: Executor<T>, onNoObservers?: Executor<T>) {\n    this.onNoObservers = onNoObservers;\n    // Call the executor asynchronously so subscribers that are called\n    // synchronously after the creation of the subscribe function\n    // can still receive the very first value generated in the executor.\n    this.task\n      .then(() => {\n        executor(this);\n      })\n      .catch(e => {\n        this.error(e);\n      });\n  }\n\n  next(value: T): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.next(value);\n    });\n  }\n\n  error(error: Error): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.error(error);\n    });\n    this.close(error);\n  }\n\n  complete(): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.complete();\n    });\n    this.close();\n  }\n\n  /**\n   * Subscribe function that can be used to add an Observer to the fan-out list.\n   *\n   * - We require that no event is sent to a subscriber synchronously to their\n   *   call to subscribe().\n   */\n  subscribe(\n    nextOrObserver?: NextFn<T> | PartialObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ): Unsubscribe {\n    let observer: Observer<T>;\n\n    if (\n      nextOrObserver === undefined &&\n      error === undefined &&\n      complete === undefined\n    ) {\n      throw new Error('Missing Observer.');\n    }\n\n    // Assemble an Observer object when passed as callback functions.\n    if (\n      implementsAnyMethods(nextOrObserver as { [key: string]: unknown }, [\n        'next',\n        'error',\n        'complete'\n      ])\n    ) {\n      observer = nextOrObserver as Observer<T>;\n    } else {\n      observer = {\n        next: nextOrObserver as NextFn<T>,\n        error,\n        complete\n      } as Observer<T>;\n    }\n\n    if (observer.next === undefined) {\n      observer.next = noop as NextFn<T>;\n    }\n    if (observer.error === undefined) {\n      observer.error = noop as ErrorFn;\n    }\n    if (observer.complete === undefined) {\n      observer.complete = noop as CompleteFn;\n    }\n\n    const unsub = this.unsubscribeOne.bind(this, this.observers!.length);\n\n    // Attempt to subscribe to a terminated Observable - we\n    // just respond to the Observer with the final error or complete\n    // event.\n    if (this.finalized) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.task.then(() => {\n        try {\n          if (this.finalError) {\n            observer.error(this.finalError);\n          } else {\n            observer.complete();\n          }\n        } catch (e) {\n          // nothing\n        }\n        return;\n      });\n    }\n\n    this.observers!.push(observer as Observer<T>);\n\n    return unsub;\n  }\n\n  // Unsubscribe is synchronous - we guarantee that no events are sent to\n  // any unsubscribed Observer.\n  private unsubscribeOne(i: number): void {\n    if (this.observers === undefined || this.observers[i] === undefined) {\n      return;\n    }\n\n    delete this.observers[i];\n\n    this.observerCount -= 1;\n    if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n      this.onNoObservers(this);\n    }\n  }\n\n  private forEachObserver(fn: (observer: Observer<T>) => void): void {\n    if (this.finalized) {\n      // Already closed by previous event....just eat the additional values.\n      return;\n    }\n\n    // Since sendOne calls asynchronously - there is no chance that\n    // this.observers will become undefined.\n    for (let i = 0; i < this.observers!.length; i++) {\n      this.sendOne(i, fn);\n    }\n  }\n\n  // Call the Observer via one of it's callback function. We are careful to\n  // confirm that the observe has not been unsubscribed since this asynchronous\n  // function had been queued.\n  private sendOne(i: number, fn: (observer: Observer<T>) => void): void {\n    // Execute the callback asynchronously\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      if (this.observers !== undefined && this.observers[i] !== undefined) {\n        try {\n          fn(this.observers[i]);\n        } catch (e) {\n          // Ignore exceptions raised in Observers or missing methods of an\n          // Observer.\n          // Log error to console. b/31404806\n          if (typeof console !== 'undefined' && console.error) {\n            console.error(e);\n          }\n        }\n      }\n    });\n  }\n\n  private close(err?: Error): void {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    if (err !== undefined) {\n      this.finalError = err;\n    }\n    // Proxy is no longer needed - garbage collect references\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      this.observers = undefined;\n      this.onNoObservers = undefined;\n    });\n  }\n}\n\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(fn: Function, onError?: ErrorFn): Function {\n  return (...args: unknown[]) => {\n    Promise.resolve(true)\n      .then(() => {\n        fn(...args);\n      })\n      .catch((error: Error) => {\n        if (onError) {\n          onError(error);\n        }\n      });\n  };\n}\n\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(\n  obj: { [key: string]: unknown },\n  methods: string[]\n): boolean {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n\n  for (const method of methods) {\n    if (method in obj && typeof obj[method] === 'function') {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction noop(): void {\n  // do nothing\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Check to make sure the appropriate number of arguments are provided for a public function.\n * Throws an error if it fails.\n *\n * @param fnName The function name\n * @param minCount The minimum number of arguments to allow for the function call\n * @param maxCount The maximum number of argument to allow for the function call\n * @param argCount The actual number of arguments provided.\n */\nexport const validateArgCount = function (\n  fnName: string,\n  minCount: number,\n  maxCount: number,\n  argCount: number\n): void {\n  let argError;\n  if (argCount < minCount) {\n    argError = 'at least ' + minCount;\n  } else if (argCount > maxCount) {\n    argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\n  }\n  if (argError) {\n    const error =\n      fnName +\n      ' failed: Was called with ' +\n      argCount +\n      (argCount === 1 ? ' argument.' : ' arguments.') +\n      ' Expects ' +\n      argError +\n      '.';\n    throw new Error(error);\n  }\n};\n\n/**\n * Generates a string to prefix an error message about failed argument validation\n *\n * @param fnName The function name\n * @param argName The name of the argument\n * @return The prefix to add to the error thrown for validation.\n */\nexport function errorPrefix(fnName: string, argName: string): string {\n  return `${fnName} failed: ${argName} argument `;\n}\n\n/**\n * @param fnName\n * @param argumentNumber\n * @param namespace\n * @param optional\n */\nexport function validateNamespace(\n  fnName: string,\n  namespace: string,\n  optional: boolean\n): void {\n  if (optional && !namespace) {\n    return;\n  }\n  if (typeof namespace !== 'string') {\n    //TODO: I should do more validation here. We only allow certain chars in namespaces.\n    throw new Error(\n      errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.'\n    );\n  }\n}\n\nexport function validateCallback(\n  fnName: string,\n  argumentName: string,\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  callback: Function,\n  optional: boolean\n): void {\n  if (optional && !callback) {\n    return;\n  }\n  if (typeof callback !== 'function') {\n    throw new Error(\n      errorPrefix(fnName, argumentName) + 'must be a valid function.'\n    );\n  }\n}\n\nexport function validateContextObject(\n  fnName: string,\n  argumentName: string,\n  context: unknown,\n  optional: boolean\n): void {\n  if (optional && !context) {\n    return;\n  }\n  if (typeof context !== 'object' || context === null) {\n    throw new Error(\n      errorPrefix(fnName, argumentName) + 'must be a valid context object.'\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert } from './assert';\n\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\n// so it's been modified.\n\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\n// use 2 characters in JavaScript.  All 4-byte UTF-8 characters begin with a first\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\n// pair).\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\n\n/**\n * @param {string} str\n * @return {Array}\n */\nexport const stringToByteArray = function (str: string): number[] {\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n\n    // Is this the lead surrogate in a surrogate pair?\n    if (c >= 0xd800 && c <= 0xdbff) {\n      const high = c - 0xd800; // the high 10 bits.\n      i++;\n      assert(i < str.length, 'Surrogate pair missing trail surrogate.');\n      const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\n      c = 0x10000 + (high << 10) + low;\n    }\n\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (c < 65536) {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Calculate length without actually converting; useful for doing cheaper validation.\n * @param {string} str\n * @return {number}\n */\nexport const stringLength = function (str: string): number {\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i);\n    if (c < 128) {\n      p++;\n    } else if (c < 2048) {\n      p += 2;\n    } else if (c >= 0xd800 && c <= 0xdbff) {\n      // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\n      p += 4;\n      i++; // skip trail surrogate.\n    } else {\n      p += 3;\n    }\n  }\n  return p;\n};\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nexport const MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nexport const RANDOM_FACTOR = 0.5;\n\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nexport function calculateBackoffMillis(\n  backoffCount: number,\n  intervalMillis: number = DEFAULT_INTERVAL_MILLIS,\n  backoffFactor: number = DEFAULT_BACKOFF_FACTOR\n): number {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n    // A fraction of the backoff value to add/subtract.\n    // Deviation: changes multiplication order to improve readability.\n    RANDOM_FACTOR *\n      currBaseValue *\n      // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n      // if we add or subtract.\n      (Math.random() - 0.5) *\n      2\n  );\n\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Provide English ordinal letters after a number\n */\nexport function ordinal(i: number): string {\n  if (!Number.isFinite(i)) {\n    return `${i}`;\n  }\n  return i + indicator(i);\n}\n\nfunction indicator(i: number): string {\n  i = Math.abs(i);\n  const cent = i % 100;\n  if (cent >= 10 && cent <= 20) {\n    return 'th';\n  }\n  const dec = i % 10;\n  if (dec === 1) {\n    return 'st';\n  }\n  if (dec === 2) {\n    return 'nd';\n  }\n  if (dec === 3) {\n    return 'rd';\n  }\n  return 'th';\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AAEU,MAAAA,SAAS,GAAG;EACvB;;AAEG;EACHC,WAAW,EAAE,KAAK;EAClB;;AAEG;EACHC,UAAU,EAAE,KAAK;EAEjB;;AAEG;EACHC,WAAW,EAAE;;;AClCf;;;;;;;;;;;;;;;AAeG;AAIH;;AAEG;AACU,MAAAC,MAAM,GAAG,SAAAA,CAAUC,SAAkB,EAAEC,OAAe;EACjE,IAAI,CAACD,SAAS,EAAE;IACd,MAAME,cAAc,CAACD,OAAO,CAAC;EAC9B;AACH,CAAE;AAEF;;AAEG;AACI,MAAMC,cAAc,GAAG,SAAAA,CAAUD,OAAe;EACrD,OAAO,IAAIE,KAAK,CACd,qBAAqB,GACnBR,SAAS,CAACG,WAAW,GACrB,4BAA4B,GAC5BG,OAAO,CACV;AACH;;ACtCA;;;;;;;;;;;;;;;AAeG;AAEH,MAAMG,mBAAiB,GAAG,SAAAC,CAAUC,GAAW;;EAE7C,MAAMC,GAAG,GAAa,EAAE;EACxB,IAAIC,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIE,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IACzB,IAAIE,CAAC,GAAG,GAAG,EAAE;MACXJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAGG,CAAC;IACb,OAAM,IAAIA,CAAC,GAAG,IAAI,EAAE;MACnBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,CAAC,GAAI,GAAG;MACzBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC1B,OAAM,IACL,CAACA,CAAC,GAAG,MAAM,MAAM,MAAM,IACvBF,CAAC,GAAG,CAAC,GAAGH,GAAG,CAACI,MAAM,IAClB,CAACJ,GAAG,CAACM,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,MAAM,MAAM,EAC3C;;MAEAE,CAAC,GAAG,OAAO,IAAI,CAACA,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,IAAIL,GAAG,CAACM,UAAU,CAAC,EAAEH,CAAC,CAAC,GAAG,MAAM,CAAC;MACnEF,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,EAAE,GAAI,EAAE,GAAI,GAAG;MACjCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC1B,OAAM;MACLJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC1B;EACF;EACD,OAAOJ,GAAG;AACZ,CAAC;AAED;;;;;AAKG;AACH,MAAMM,iBAAiB,GAAG,SAAAA,CAAUC,KAAe;;EAEjD,MAAMP,GAAG,GAAa,EAAE;EACxB,IAAIQ,GAAG,GAAG,CAAC;IACTJ,CAAC,GAAG,CAAC;EACP,OAAOI,GAAG,GAAGD,KAAK,CAACJ,MAAM,EAAE;IACzB,MAAMM,EAAE,GAAGF,KAAK,CAACC,GAAG,EAAE,CAAC;IACvB,IAAIC,EAAE,GAAG,GAAG,EAAE;MACZT,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAACF,EAAE,CAAC;IACnC,OAAM,IAAIA,EAAE,GAAG,GAAG,IAAIA,EAAE,GAAG,GAAG,EAAE;MAC/B,MAAMG,EAAE,GAAGL,KAAK,CAACC,GAAG,EAAE,CAAC;MACvBR,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAE,CAACF,EAAE,GAAG,EAAE,KAAK,CAAC,GAAKG,EAAE,GAAG,EAAG,CAAC;IAC7D,OAAM,IAAIH,EAAE,GAAG,GAAG,IAAIA,EAAE,GAAG,GAAG,EAAE;;MAE/B,MAAMG,EAAE,GAAGL,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMK,EAAE,GAAGN,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMM,EAAE,GAAGP,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMO,CAAC,GACL,CAAE,CAACN,EAAE,GAAG,CAAC,KAAK,EAAE,GAAK,CAACG,EAAE,GAAG,EAAE,KAAK,EAAG,GAAI,CAACC,EAAE,GAAG,EAAE,KAAK,CAAE,GAAIC,EAAE,GAAG,EAAG,IACpE,OAAO;MACTd,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAC,MAAM,IAAII,CAAC,IAAI,EAAE,CAAC,CAAC;MAClDf,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAC,MAAM,IAAII,CAAC,GAAG,IAAI,CAAC,CAAC;IACpD,OAAM;MACL,MAAMH,EAAE,GAAGL,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMK,EAAE,GAAGN,KAAK,CAACC,GAAG,EAAE,CAAC;MACvBR,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAC3B,CAACF,EAAE,GAAG,EAAE,KAAK,EAAE,GAAK,CAACG,EAAE,GAAG,EAAE,KAAK,CAAE,GAAIC,EAAE,GAAG,EAAG,CACjD;IACF;EACF;EACD,OAAOb,GAAG,CAACgB,IAAI,CAAC,EAAE,CAAC;AACrB,CAAC;AAkBD;AACA;AACA;AACA;AACa,MAAAC,MAAM,GAAW;EAC5B;;AAEG;EACHC,cAAc,EAAE,IAAI;EAEpB;;AAEG;EACHC,cAAc,EAAE,IAAI;EAEpB;;;AAGG;EACHC,qBAAqB,EAAE,IAAI;EAE3B;;;AAGG;EACHC,qBAAqB,EAAE,IAAI;EAE3B;;;AAGG;EACHC,iBAAiB,EACf,4BAA4B,GAAG,4BAA4B,GAAG,YAAY;EAE5E;;AAEG;EACH,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACD,iBAAiB,GAAG,KAAK;EACtC;EAED;;AAEG;EACH,IAAIE,oBAAoBA,CAAA;IACtB,OAAO,IAAI,CAACF,iBAAiB,GAAG,KAAK;EACtC;EAED;;;;;;AAMG;EACHG,kBAAkB,EAAE,OAAOC,IAAI,KAAK,UAAU;EAE9C;;;;;;;;AAQG;EACHC,eAAeA,CAACC,KAA4B,EAAEC,OAAiB;IAC7D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACzB,MAAMhC,KAAK,CAAC,+CAA+C,CAAC;IAC7D;IAED,IAAI,CAACoC,KAAK,EAAE;IAEZ,MAAMC,aAAa,GAAGJ,OAAO,GACzB,IAAI,CAACT,qBAAsB,GAC3B,IAAI,CAACF,cAAe;IAExB,MAAMgB,MAAM,GAAG,EAAE;IAEjB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,KAAK,CAACzB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACxC,MAAMiC,KAAK,GAAGP,KAAK,CAAC1B,CAAC,CAAC;MACtB,MAAMkC,SAAS,GAAGlC,CAAC,GAAG,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MACtC,MAAMkC,KAAK,GAAGD,SAAS,GAAGR,KAAK,CAAC1B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1C,MAAMoC,SAAS,GAAGpC,CAAC,GAAG,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MACtC,MAAMoC,KAAK,GAAGD,SAAS,GAAGV,KAAK,CAAC1B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAE1C,MAAMsC,QAAQ,GAAGL,KAAK,IAAI,CAAC;MAC3B,MAAMM,QAAQ,GAAI,CAACN,KAAK,GAAG,IAAI,KAAK,CAAC,GAAKE,KAAK,IAAI,CAAE;MACrD,IAAIK,QAAQ,GAAI,CAACL,KAAK,GAAG,IAAI,KAAK,CAAC,GAAKE,KAAK,IAAI,CAAE;MACnD,IAAII,QAAQ,GAAGJ,KAAK,GAAG,IAAI;MAE3B,IAAI,CAACD,SAAS,EAAE;QACdK,QAAQ,GAAG,EAAE;QAEb,IAAI,CAACP,SAAS,EAAE;UACdM,QAAQ,GAAG,EAAE;QACd;MACF;MAEDR,MAAM,CAACU,IAAI,CACTX,aAAa,CAACO,QAAQ,CAAC,EACvBP,aAAa,CAACQ,QAAQ,CAAC,EACvBR,aAAa,CAACS,QAAQ,CAAC,EACvBT,aAAa,CAACU,QAAQ,CAAC,CACxB;IACF;IAED,OAAOT,MAAM,CAAClB,IAAI,CAAC,EAAE,CAAC;EACvB;EAED;;;;;;;AAOG;EACH6B,YAAYA,CAACjB,KAAa,EAAEC,OAAiB;;;IAG3C,IAAI,IAAI,CAACJ,kBAAkB,IAAI,CAACI,OAAO,EAAE;MACvC,OAAOiB,IAAI,CAAClB,KAAK,CAAC;IACnB;IACD,OAAO,IAAI,CAACD,eAAe,CAAC9B,mBAAiB,CAAC+B,KAAK,CAAC,EAAEC,OAAO,CAAC;EAC/D;EAED;;;;;;;AAOG;EACHkB,YAAYA,CAACnB,KAAa,EAAEC,OAAgB;;;IAG1C,IAAI,IAAI,CAACJ,kBAAkB,IAAI,CAACI,OAAO,EAAE;MACvC,OAAOH,IAAI,CAACE,KAAK,CAAC;IACnB;IACD,OAAOtB,iBAAiB,CAAC,IAAI,CAAC0C,uBAAuB,CAACpB,KAAK,EAAEC,OAAO,CAAC,CAAC;EACvE;EAED;;;;;;;;;;;;;;AAcG;EACHmB,uBAAuBA,CAACpB,KAAa,EAAEC,OAAgB;IACrD,IAAI,CAACG,KAAK,EAAE;IAEZ,MAAMiB,aAAa,GAAGpB,OAAO,GACzB,IAAI,CAACR,qBAAsB,GAC3B,IAAI,CAACF,cAAe;IAExB,MAAMe,MAAM,GAAa,EAAE;IAE3B,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,KAAK,CAACzB,MAAM,GAAI;MAClC,MAAMgC,KAAK,GAAGc,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,EAAE,CAAC,CAAC;MAE9C,MAAMkC,SAAS,GAAGlC,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MAClC,MAAMkC,KAAK,GAAGD,SAAS,GAAGa,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAG,CAAC;MAC5D,EAAEA,CAAC;MAEH,MAAMoC,SAAS,GAAGpC,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MAClC,MAAMoC,KAAK,GAAGD,SAAS,GAAGW,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAG,EAAE;MAC7D,EAAEA,CAAC;MAEH,MAAMiD,SAAS,GAAGjD,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MAClC,MAAMiD,KAAK,GAAGD,SAAS,GAAGF,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAG,EAAE;MAC7D,EAAEA,CAAC;MAEH,IAAIiC,KAAK,IAAI,IAAI,IAAIE,KAAK,IAAI,IAAI,IAAIE,KAAK,IAAI,IAAI,IAAIa,KAAK,IAAI,IAAI,EAAE;QACpE,MAAM,IAAIC,uBAAuB,EAAE;MACpC;MAED,MAAMb,QAAQ,GAAIL,KAAK,IAAI,CAAC,GAAKE,KAAK,IAAI,CAAE;MAC5CH,MAAM,CAACU,IAAI,CAACJ,QAAQ,CAAC;MAErB,IAAID,KAAK,KAAK,EAAE,EAAE;QAChB,MAAME,QAAQ,GAAKJ,KAAK,IAAI,CAAC,GAAI,IAAI,GAAKE,KAAK,IAAI,CAAE;QACrDL,MAAM,CAACU,IAAI,CAACH,QAAQ,CAAC;QAErB,IAAIW,KAAK,KAAK,EAAE,EAAE;UAChB,MAAMV,QAAQ,GAAKH,KAAK,IAAI,CAAC,GAAI,IAAI,GAAIa,KAAK;UAC9ClB,MAAM,CAACU,IAAI,CAACF,QAAQ,CAAC;QACtB;MACF;IACF;IAED,OAAOR,MAAM;EACd;EAED;;;;AAIG;EACHF,KAAKA,CAAA;IACH,IAAI,CAAC,IAAI,CAACd,cAAc,EAAE;MACxB,IAAI,CAACA,cAAc,GAAG,EAAE;MACxB,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACC,qBAAqB,GAAG,EAAE;MAC/B,IAAI,CAACC,qBAAqB,GAAG,EAAE;;MAG/B,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACqB,YAAY,CAACpB,MAAM,EAAED,CAAC,EAAE,EAAE;QACjD,IAAI,CAACgB,cAAc,CAAChB,CAAC,CAAC,GAAG,IAAI,CAACqB,YAAY,CAAC2B,MAAM,CAAChD,CAAC,CAAC;QACpD,IAAI,CAACiB,cAAc,CAAC,IAAI,CAACD,cAAc,CAAChB,CAAC,CAAC,CAAC,GAAGA,CAAC;QAC/C,IAAI,CAACkB,qBAAqB,CAAClB,CAAC,CAAC,GAAG,IAAI,CAACsB,oBAAoB,CAAC0B,MAAM,CAAChD,CAAC,CAAC;QACnE,IAAI,CAACmB,qBAAqB,CAAC,IAAI,CAACD,qBAAqB,CAAClB,CAAC,CAAC,CAAC,GAAGA,CAAC;;QAG7D,IAAIA,CAAC,IAAI,IAAI,CAACoB,iBAAiB,CAACnB,MAAM,EAAE;UACtC,IAAI,CAACgB,cAAc,CAAC,IAAI,CAACK,oBAAoB,CAAC0B,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAGA,CAAC;UAC5D,IAAI,CAACmB,qBAAqB,CAAC,IAAI,CAACE,YAAY,CAAC2B,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAGA,CAAC;QAC5D;MACF;IACF;EACF;CACD;AAEF;;AAEG;AACG,MAAOmD,uBAAwB,SAAQzD,KAAK;EAAlD0D,YAAA;;IACW,IAAI,CAAAC,IAAA,GAAG,yBAAyB;EAC1C;AAAA;AAED;;AAEG;AACI,MAAMC,YAAY,GAAG,SAAAA,CAAUzD,GAAW;EAC/C,MAAM0D,SAAS,GAAG5D,mBAAiB,CAACE,GAAG,CAAC;EACxC,OAAOkB,MAAM,CAACU,eAAe,CAAC8B,SAAS,EAAE,IAAI,CAAC;AAChD,CAAE;AAEF;;;AAGG;AACI,MAAMC,6BAA6B,GAAG,SAAAA,CAAU3D,GAAW;;EAEhE,OAAOyD,YAAY,CAACzD,GAAG,CAAC,CAAC4D,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAC7C,CAAE;AAEF;;;;;;;;AAQG;AACI,MAAMC,YAAY,GAAG,SAAAA,CAAU7D,GAAW;EAC/C,IAAI;IACF,OAAOkB,MAAM,CAAC8B,YAAY,CAAChD,GAAG,EAAE,IAAI,CAAC;EACtC,EAAC,OAAO8D,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;EAC1C;EACD,OAAO,IAAI;AACb;;ACxXA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACG,SAAUG,QAAQA,CAAIC,KAAQ;EAClC,OAAOC,UAAU,CAACC,SAAS,EAAEF,KAAK,CAAM;AAC1C;AAEA;;;;;;;;;;;;;AAaG;AACa,SAAAC,UAAUA,CAACE,MAAe,EAAEC,MAAe;EACzD,IAAI,EAAEA,MAAM,YAAYC,MAAM,CAAC,EAAE;IAC/B,OAAOD,MAAM;EACd;EAED,QAAQA,MAAM,CAACf,WAAW;IACxB,KAAKiB,IAAI;;;MAGP,MAAMC,SAAS,GAAGH,MAAc;MAChC,OAAO,IAAIE,IAAI,CAACC,SAAS,CAACC,OAAO,CAAE,EAAC;IAEtC,KAAKH,MAAM;MACT,IAAIF,MAAM,KAAKD,SAAS,EAAE;QACxBC,MAAM,GAAG,EAAE;MACZ;MACD;IACF,KAAKtC,KAAK;;MAERsC,MAAM,GAAG,EAAE;MACX;IAEF;;MAEE,OAAOC,MAAM;EAChB;EAED,KAAK,MAAMK,IAAI,IAAIL,MAAM,EAAE;;IAEzB,IAAI,CAACA,MAAM,CAACM,cAAc,CAACD,IAAI,CAAC,IAAI,CAACE,UAAU,CAACF,IAAI,CAAC,EAAE;MACrD;IACD;IACAN,MAAkC,CAACM,IAAI,CAAC,GAAGR,UAAU,CACnDE,MAAkC,CAACM,IAAI,CAAC,EACxCL,MAAkC,CAACK,IAAI,CAAC,CAC1C;EACF;EAED,OAAON,MAAM;AACf;AAEA,SAASQ,UAAUA,CAACC,GAAW;EAC7B,OAAOA,GAAG,KAAK,WAAW;AAC5B;;ACjFA;;;;;;;;;;;;;;;AAeG;AAEH;;;;AAIG;SACaC,SAASA,CAAA;EACvB,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC/B,OAAOA,IAAI;EACZ;EACD,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAOA,MAAM;EACd;EACD,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,OAAOA,MAAM;EACd;EACD,MAAM,IAAIrF,KAAK,CAAC,iCAAiC,CAAC;AACpD;;ACjCA;;;;;;;;;;;;;;;AAeG;AAyCH,MAAMsF,qBAAqB,GAAGA,CAAA,KAC5BJ,SAAS,CAAE,EAACK,qBAAqB;AAEnC;;;;;;;AAOG;AACH,MAAMC,0BAA0B,GAAGA,CAAA,KAAmC;EACpE,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACC,GAAG,KAAK,WAAW,EAAE;IACxE;EACD;EACD,MAAMC,kBAAkB,GAAGF,OAAO,CAACC,GAAG,CAACH,qBAAqB;EAC5D,IAAII,kBAAkB,EAAE;IACtB,OAAOC,IAAI,CAACC,KAAK,CAACF,kBAAkB,CAAC;EACtC;AACH,CAAC;AAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAmC;EAC/D,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACnC;EACD;EACD,IAAIC,KAAK;EACT,IAAI;IACFA,KAAK,GAAGD,QAAQ,CAACE,MAAM,CAACD,KAAK,CAAC,+BAA+B,CAAC;EAC/D,EAAC,OAAO/B,CAAC,EAAE;;;IAGV;EACD;EACD,MAAMiC,OAAO,GAAGF,KAAK,IAAIhC,YAAY,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/C,OAAOE,OAAO,IAAIN,IAAI,CAACC,KAAK,CAACK,OAAO,CAAC;AACvC,CAAC;AAED;;;;;;AAMG;AACI,MAAMC,WAAW,GAAGA,CAAA,KAAmC;EAC5D,IAAI;IACF,OACEC,0BAA0B,CAAE,KAC5Bd,qBAAqB,CAAE,KACvBE,0BAA0B,CAAE,KAC5BM,qBAAqB,EAAE;EAE1B,EAAC,OAAO7B,CAAC,EAAE;IACV;;;;;AAKG;IACHC,OAAO,CAACmC,IAAI,CAAC,+CAA+CpC,CAAC,EAAE,CAAC;IAChE;EACD;AACH,CAAE;AAEF;;;;;AAKG;MACUqC,sBAAsB,GACjCC,WAAmB,IACI;EAAA,IAAAC,EAAA,EAAAC,EAAA;EAAA,QAAAA,EAAA,IAAAD,EAAA,GAAAL,WAAW,EAAE,MAAE,QAAAK,EAAA,uBAAAA,EAAA,CAAAE,aAAa,cAAAD,EAAA,uBAAAA,EAAA,CAAGF,WAAW,CAAC;AAAC;AAErE;;;;;AAKG;AACU,MAAAI,iCAAiC,GAC5CJ,WAAmB,IAC6B;EAChD,MAAMK,IAAI,GAAGN,sBAAsB,CAACC,WAAW,CAAC;EAChD,IAAI,CAACK,IAAI,EAAE;IACT,OAAOrC,SAAS;EACjB;EACD,MAAMsC,cAAc,GAAGD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC,CAAC;EAC7C,IAAID,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAG,CAAC,KAAKD,IAAI,CAACrG,MAAM,EAAE;IAC7D,MAAM,IAAIP,KAAK,CAAC,gBAAgB4G,IAAI,sCAAsC,CAAC;EAC5E;;EAED,MAAMG,IAAI,GAAGC,QAAQ,CAACJ,IAAI,CAACK,SAAS,CAACJ,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7D,IAAID,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;IAEnB,OAAO,CAACA,IAAI,CAACK,SAAS,CAAC,CAAC,EAAEJ,cAAc,GAAG,CAAC,CAAC,EAAEE,IAAI,CAAC;EACrD,OAAM;IACL,OAAO,CAACH,IAAI,CAACK,SAAS,CAAC,CAAC,EAAEJ,cAAc,CAAC,EAAEE,IAAI,CAAC;EACjD;AACH,CAAE;AAEF;;;AAGG;AACI,MAAMG,mBAAmB,GAAGA,CAAA,KAAyC;EAAA,IAAAV,EAAA;EAC1E,QAAAA,EAAA,GAAAL,WAAW,EAAE,cAAAK,EAAA,uBAAAA,EAAA,CAAEW,MAAM;AAAC;AAExB;;;;AAIG;MACUC,sBAAsB,GACjCzD,IAAO,IAEP;EAAA,IAAA6C,EAAA;EAAA,QAAAA,EAAA,GAAAL,WAAW,EAAE,cAAAK,EAAA,uBAAAA,EAAA,CAAG,IAAI7C,IAAI,EAAE,CAA8B;AAAA;;AC5K1D;;;;;;;;;;;;;;;AAeG;MAEU0D,QAAQ;EAInB3D,YAAA;IAFA,KAAA4D,MAAM,GAA8B,MAAK,EAAG;IAC5C,KAAAC,OAAO,GAA8B,MAAK,EAAG;IAE3C,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACF,OAAO,EAAED,MAAM,KAAI;MAC7C,IAAI,CAACC,OAAO,GAAGA,OAAoC;MACnD,IAAI,CAACD,MAAM,GAAGA,MAAmC;IACnD,CAAC,CAAC;EACH;EAED;;;;AAIG;EACHI,YAAYA,CACVC,QAAqD;IAErD,OAAO,CAACxD,KAAK,EAAEE,KAAM,KAAI;MACvB,IAAIF,KAAK,EAAE;QACT,IAAI,CAACmD,MAAM,CAACnD,KAAK,CAAC;MACnB,OAAM;QACL,IAAI,CAACoD,OAAO,CAAClD,KAAK,CAAC;MACpB;MACD,IAAI,OAAOsD,QAAQ,KAAK,UAAU,EAAE;;;QAGlC,IAAI,CAACH,OAAO,CAACI,KAAK,CAAC,MAAK,CAAG,EAAC;;;QAI5B,IAAID,QAAQ,CAACpH,MAAM,KAAK,CAAC,EAAE;UACzBoH,QAAQ,CAACxD,KAAK,CAAC;QAChB,OAAM;UACLwD,QAAQ,CAACxD,KAAK,EAAEE,KAAK,CAAC;QACvB;MACF;IACH,CAAC;EACF;AACF;;ACzDD;;;;;;;;;;;;;;;AAeG;AAEH;;;AAGG;AACG,SAAUwD,kBAAkBA,CAACjB,IAAY;EAC7C,OAAOA,IAAI,CAACkB,QAAQ,CAAC,wBAAwB,CAAC;AAChD;AAEA;;;;AAIG;AACI,eAAeC,UAAUA,CAACC,QAAgB;EAC/C,MAAMC,MAAM,GAAG,MAAMC,KAAK,CAACF,QAAQ,EAAE;IACnCG,WAAW,EAAE;EACd,EAAC;EACF,OAAOF,MAAM,CAACG,EAAE;AAClB;;ACnCA;;;;;;;;;;;;;;;AAeG;AAgFa,SAAAC,mBAAmBA,CACjCC,KAA+B,EAC/BC,SAAkB;EAElB,IAAID,KAAK,CAACE,GAAG,EAAE;IACb,MAAM,IAAIxI,KAAK,CACb,8GAA8G,CAC/G;EACF;;EAED,MAAMyI,MAAM,GAAG;IACbC,GAAG,EAAE,MAAM;IACXC,IAAI,EAAE;GACP;EAED,MAAMC,OAAO,GAAGL,SAAS,IAAI,cAAc;EAC3C,MAAMM,GAAG,GAAGP,KAAK,CAACO,GAAG,IAAI,CAAC;EAC1B,MAAMC,GAAG,GAAGR,KAAK,CAACQ,GAAG,IAAIR,KAAK,CAACS,OAAO;EACtC,IAAI,CAACD,GAAG,EAAE;IACR,MAAM,IAAI9I,KAAK,CAAC,sDAAsD,CAAC;EACxE;EAED,MAAMgJ,OAAO,GAAAtE,MAAA,CAAAuE,MAAA;;IAEXC,GAAG,EAAE,kCAAkCN,OAAO,EAAE;IAChDO,GAAG,EAAEP,OAAO;IACZC,GAAG;IACHO,GAAG,EAAEP,GAAG,GAAG,IAAI;IACfQ,SAAS,EAAER,GAAG;IACdC,GAAG;IACHC,OAAO,EAAED,GAAG;IACZQ,QAAQ,EAAE;MACRC,gBAAgB,EAAE,QAAQ;MAC1BC,UAAU,EAAE,CAAE;;EAIb,GAAAlB,KAAK,CACT;;EAGD,MAAMmB,SAAS,GAAG,EAAE;EACpB,OAAO,CACL3F,6BAA6B,CAAC8B,IAAI,CAAC8D,SAAS,CAACjB,MAAM,CAAC,CAAC,EACrD3E,6BAA6B,CAAC8B,IAAI,CAAC8D,SAAS,CAACV,OAAO,CAAC,CAAC,EACtDS,SAAS,CACV,CAACrI,IAAI,CAAC,GAAG,CAAC;AACb;AAKA,MAAMuI,cAAc,GAAsB,EAAE;AAO5C;AACA,SAASC,kBAAkBA,CAAA;EACzB,MAAMC,OAAO,GAAoB;IAC/BC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE;GACX;EACD,KAAK,MAAM9E,GAAG,IAAIP,MAAM,CAACsF,IAAI,CAACL,cAAc,CAAC,EAAE;IAC7C,IAAIA,cAAc,CAAC1E,GAAG,CAAC,EAAE;MACvB4E,OAAO,CAACE,QAAQ,CAAC/G,IAAI,CAACiC,GAAG,CAAC;IAC3B,OAAM;MACL4E,OAAO,CAACC,IAAI,CAAC9G,IAAI,CAACiC,GAAG,CAAC;IACvB;EACF;EACD,OAAO4E,OAAO;AAChB;AAEA,SAASI,aAAaA,CAACC,EAAU;EAC/B,IAAIC,SAAS,GAAGpE,QAAQ,CAACqE,cAAc,CAACF,EAAE,CAAC;EAC3C,IAAIG,OAAO,GAAG,KAAK;EACnB,IAAI,CAACF,SAAS,EAAE;IACdA,SAAS,GAAGpE,QAAQ,CAACuE,aAAa,CAAC,KAAK,CAAC;IACzCH,SAAS,CAACI,YAAY,CAAC,IAAI,EAAEL,EAAE,CAAC;IAChCG,OAAO,GAAG,IAAI;EACf;EACD,OAAO;IAAEA,OAAO;IAAEG,OAAO,EAAEL;EAAS,CAAE;AACxC;AAEA,IAAIM,mBAAmB,GAAG,KAAK;AAC/B;;;;;AAKG;AACa,SAAAC,oBAAoBA,CAClC/G,IAAY,EACZgH,iBAA0B;EAE1B,IACE,OAAOvF,MAAM,KAAK,WAAW,IAC7B,OAAOW,QAAQ,KAAK,WAAW,IAC/B,CAAC8B,kBAAkB,CAACzC,MAAM,CAACwF,QAAQ,CAAChE,IAAI,CAAC,IACzC+C,cAAc,CAAChG,IAAI,CAAC,KAAKgH,iBAAiB,IAC1ChB,cAAc,CAAChG,IAAI,CAAC;EAAA;EACpB8G,mBAAmB,EACnB;IACA;EACD;EAEDd,cAAc,CAAChG,IAAI,CAAC,GAAGgH,iBAAiB;EAExC,SAASE,UAAUA,CAACX,EAAU;IAC5B,OAAO,uBAAuBA,EAAE,EAAE;EACnC;EACD,MAAMY,QAAQ,GAAG,oBAAoB;EACrC,MAAMjB,OAAO,GAAGD,kBAAkB,EAAE;EACpC,MAAMmB,SAAS,GAAGlB,OAAO,CAACC,IAAI,CAACvJ,MAAM,GAAG,CAAC;EAEzC,SAASyK,QAAQA,CAAA;IACf,MAAMR,OAAO,GAAGzE,QAAQ,CAACqE,cAAc,CAACU,QAAQ,CAAC;IACjD,IAAIN,OAAO,EAAE;MACXA,OAAO,CAACS,MAAM,EAAE;IACjB;EACF;EAED,SAASC,iBAAiBA,CAACC,QAAqB;IAC9CA,QAAQ,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;IAC/BF,QAAQ,CAACC,KAAK,CAACE,UAAU,GAAG,SAAS;IACrCH,QAAQ,CAACC,KAAK,CAACG,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACC,KAAK,CAACI,MAAM,GAAG,KAAK;IAC7BL,QAAQ,CAACC,KAAK,CAACK,IAAI,GAAG,KAAK;IAC3BN,QAAQ,CAACC,KAAK,CAACM,OAAO,GAAG,MAAM;IAC/BP,QAAQ,CAACC,KAAK,CAACO,YAAY,GAAG,KAAK;IACnCR,QAAQ,CAACC,KAAK,CAACQ,UAAU,GAAG,QAAQ;EACrC;EAED,SAASC,eAAeA,CAACC,WAAuB,EAAEC,MAAc;IAC9DD,WAAW,CAACvB,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC;IACvCuB,WAAW,CAACvB,YAAY,CAAC,IAAI,EAAEwB,MAAM,CAAC;IACtCD,WAAW,CAACvB,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC;IACxCuB,WAAW,CAACvB,YAAY,CAAC,SAAS,EAAE,WAAW,CAAC;IAChDuB,WAAW,CAACvB,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;IACxCuB,WAAW,CAACV,KAAK,CAACY,UAAU,GAAG,MAAM;EACtC;EAED,SAASC,aAAaA,CAAA;IACpB,MAAMC,QAAQ,GAAGnG,QAAQ,CAACuE,aAAa,CAAC,MAAM,CAAC;IAC/C4B,QAAQ,CAACd,KAAK,CAACe,MAAM,GAAG,SAAS;IACjCD,QAAQ,CAACd,KAAK,CAACY,UAAU,GAAG,MAAM;IAClCE,QAAQ,CAACd,KAAK,CAACgB,QAAQ,GAAG,MAAM;IAChCF,QAAQ,CAACG,SAAS,GAAG,UAAU;IAC/BH,QAAQ,CAACI,OAAO,GAAG,MAAK;MACtB7B,mBAAmB,GAAG,IAAI;MAC1BO,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOkB,QAAQ;EAChB;EAED,SAASK,eAAeA,CACtBC,aAAgC,EAChCC,WAAmB;IAEnBD,aAAa,CAACjC,YAAY,CAAC,IAAI,EAAEkC,WAAW,CAAC;IAC7CD,aAAa,CAACE,SAAS,GAAG,YAAY;IACtCF,aAAa,CAACG,IAAI,GAChB,sEAAsE;IACxEH,aAAa,CAACjC,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC;IAC/CiC,aAAa,CAACpB,KAAK,CAACwB,WAAW,GAAG,KAAK;IACvCJ,aAAa,CAACpB,KAAK,CAACyB,cAAc,GAAG,WAAW;EACjD;EAED,SAASC,QAAQA,CAAA;IACf,MAAMC,MAAM,GAAG9C,aAAa,CAACa,QAAQ,CAAC;IACtC,MAAMkC,cAAc,GAAGnC,UAAU,CAAC,MAAM,CAAC;IACzC,MAAMoC,YAAY,GAChBlH,QAAQ,CAACqE,cAAc,CAAC4C,cAAc,CAAC,IAAIjH,QAAQ,CAACuE,aAAa,CAAC,MAAM,CAAC;IAC3E,MAAMmC,WAAW,GAAG5B,UAAU,CAAC,WAAW,CAAC;IAC3C,MAAM2B,aAAa,GAChBzG,QAAQ,CAACqE,cAAc,CAACqC,WAAW,CAAuB,IAC3D1G,QAAQ,CAACuE,aAAa,CAAC,GAAG,CAAC;IAC7B,MAAM4C,aAAa,GAAGrC,UAAU,CAAC,cAAc,CAAC;IAChD,MAAMiB,WAAW,GACd/F,QAAQ,CAACqE,cAAc,CACtB8C,aAAa,CACqB,IACpCnH,QAAQ,CAACoH,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;IAC/D,IAAIJ,MAAM,CAAC1C,OAAO,EAAE;;MAElB,MAAMc,QAAQ,GAAG4B,MAAM,CAACvC,OAAO;MAC/BU,iBAAiB,CAACC,QAAQ,CAAC;MAC3BoB,eAAe,CAACC,aAAa,EAAEC,WAAW,CAAC;MAC3C,MAAMP,QAAQ,GAAGD,aAAa,EAAE;MAChCJ,eAAe,CAACC,WAAW,EAAEoB,aAAa,CAAC;MAC3C/B,QAAQ,CAACiC,MAAM,CAACtB,WAAW,EAAEmB,YAAY,EAAET,aAAa,EAAEN,QAAQ,CAAC;MACnEnG,QAAQ,CAACsH,IAAI,CAACC,WAAW,CAACnC,QAAQ,CAAC;IACpC;IAED,IAAIJ,SAAS,EAAE;MACbkC,YAAY,CAACP,SAAS,GAAG,+BAA+B;MACxDZ,WAAW,CAACO,SAAS,GAAG;;;;;;;QAOtB;IACH,OAAM;MACLP,WAAW,CAACO,SAAS,GAAG;;;;;;;QAOtB;MACFY,YAAY,CAACP,SAAS,GAAG,4CAA4C;IACtE;IACDO,YAAY,CAAC1C,YAAY,CAAC,IAAI,EAAEyC,cAAc,CAAC;EAChD;EACD,IAAIjH,QAAQ,CAACwH,UAAU,KAAK,SAAS,EAAE;IACrCnI,MAAM,CAACoI,gBAAgB,CAAC,kBAAkB,EAAEV,QAAQ,CAAC;EACtD,OAAM;IACLA,QAAQ,EAAE;EACX;AACH;;AC/TA;;;;;;;;;;;;;;;AAeG;AAUH;;;AAGG;SACaW,KAAKA,CAAA;EACnB,IACE,OAAOC,SAAS,KAAK,WAAW,IAChC,OAAOA,SAAS,CAAC,WAAW,CAAC,KAAK,QAAQ,EAC1C;IACA,OAAOA,SAAS,CAAC,WAAW,CAAC;EAC9B,OAAM;IACL,OAAO,EAAE;EACV;AACH;AAEA;;;;;;AAMG;SACaC,eAAeA,CAAA;EAC7B,OACE,OAAOvI,MAAM,KAAK,WAAW;;;EAG7B,CAAC,EAAEA,MAAM,CAAC,SAAS,CAAC,IAAIA,MAAM,CAAC,UAAU,CAAC,IAAIA,MAAM,CAAC,UAAU,CAAC,CAAC,IACjE,mDAAmD,CAACwI,IAAI,CAACH,KAAK,EAAE,CAAC;AAErE;AAEA;;;;AAIG;AACH;SACgBI,MAAMA,CAAA;;EACpB,MAAMC,gBAAgB,GAAG,CAAAtH,EAAA,GAAAL,WAAW,CAAE,eAAAK,EAAA,uBAAAA,EAAA,CAAEsH,gBAAgB;EACxD,IAAIA,gBAAgB,KAAK,MAAM,EAAE;IAC/B,OAAO,IAAI;EACZ,OAAM,IAAIA,gBAAgB,KAAK,SAAS,EAAE;IACzC,OAAO,KAAK;EACb;EAED,IAAI;IACF,OACEpJ,MAAM,CAACqJ,SAAS,CAACC,QAAQ,CAACC,IAAI,CAAC5I,MAAM,CAACI,OAAO,CAAC,KAAK,kBAAkB;EAExE,EAAC,OAAOxB,CAAC,EAAE;IACV,OAAO,KAAK;EACb;AACH;AAEA;;;;;AAKG;SACaiK,SAASA,CAAA;EACvB,OAAO,OAAO9I,MAAM,KAAK,WAAW,IAAI+I,WAAW,EAAE;AACvD;AAEA;;AAEG;SACaA,WAAWA,CAAA;EACzB,OACE,OAAOC,iBAAiB,KAAK,WAAW,IACxC,OAAOjJ,IAAI,KAAK,WAAW,IAC3BA,IAAI,YAAYiJ,iBAAiB;AAErC;AAEA;;AAEG;SACaC,kBAAkBA,CAAA;EAChC,OACE,OAAOX,SAAS,KAAK,WAAW,IAChCA,SAAS,CAACY,SAAS,KAAK,oBAAoB;AAEhD;SAUgBC,kBAAkBA,CAAA;EAChC,MAAMC,OAAO,GACX,OAAOC,MAAM,KAAK,QAAQ,GACtBA,MAAM,CAACD,OAAO,GACd,OAAOE,OAAO,KAAK,QAAQ,GAC3BA,OAAO,CAACF,OAAO,GACfjK,SAAS;EACf,OAAO,OAAOiK,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACtE,EAAE,KAAK3F,SAAS;AAChE;AAEA;;;;AAIG;SACaoK,aAAaA,CAAA;EAC3B,OACE,OAAOjB,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAAC,SAAS,CAAC,KAAK,aAAa;AAE3E;AAEA;SACgBkB,UAAUA,CAAA;EACxB,OAAOnB,KAAK,EAAE,CAACoB,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;AAC1C;AAEA;SACgBC,IAAIA,CAAA;EAClB,MAAMC,EAAE,GAAGtB,KAAK,EAAE;EAClB,OAAOsB,EAAE,CAACF,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAIE,EAAE,CAACF,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;AAChE;AAEA;SACgBG,KAAKA,CAAA;EACnB,OAAOvB,KAAK,EAAE,CAACoB,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;AAC3C;AAEA;;;;AAIG;SACaI,SAASA,CAAA;EACvB,OAAOzP,SAAS,CAACC,WAAW,KAAK,IAAI,IAAID,SAAS,CAACE,UAAU,KAAK,IAAI;AACxE;AAEA;SACgBwP,QAAQA,CAAA;EACtB,OACE,CAACrB,MAAM,CAAE,KACT,CAAC,CAACH,SAAS,CAACY,SAAS,IACrBZ,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC,IACtC,CAACzB,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC;AAE3C;AAEA;SACgBC,gBAAgBA,CAAA;EAC9B,OACE,CAACvB,MAAM,CAAE,KACT,CAAC,CAACH,SAAS,CAACY,SAAS,KACpBZ,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC,IACrCzB,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC,CAAC,IACzC,CAACzB,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC;AAE3C;AAEA;;;AAGG;SACaE,oBAAoBA,CAAA;EAClC,IAAI;IACF,OAAO,OAAOC,SAAS,KAAK,QAAQ;EACrC,EAAC,OAAOrL,CAAC,EAAE;IACV,OAAO,KAAK;EACb;AACH;AAEA;;;;;;AAMG;SACasL,yBAAyBA,CAAA;EACvC,OAAO,IAAI9H,OAAO,CAAC,CAACF,OAAO,EAAED,MAAM,KAAI;IACrC,IAAI;MACF,IAAIkI,QAAQ,GAAY,IAAI;MAC5B,MAAMC,aAAa,GACjB,yDAAyD;MAC3D,MAAMC,OAAO,GAAGvK,IAAI,CAACmK,SAAS,CAACK,IAAI,CAACF,aAAa,CAAC;MAClDC,OAAO,CAACE,SAAS,GAAG,MAAK;QACvBF,OAAO,CAACzH,MAAM,CAAC4H,KAAK,EAAE;;QAEtB,IAAI,CAACL,QAAQ,EAAE;UACbrK,IAAI,CAACmK,SAAS,CAACQ,cAAc,CAACL,aAAa,CAAC;QAC7C;QACDlI,OAAO,CAAC,IAAI,CAAC;MACf,CAAC;MACDmI,OAAO,CAACK,eAAe,GAAG,MAAK;QAC7BP,QAAQ,GAAG,KAAK;MAClB,CAAC;MAEDE,OAAO,CAACM,OAAO,GAAG,MAAK;;QACrB1I,MAAM,CAAC,EAAAd,EAAA,GAAAkJ,OAAO,CAACvL,KAAK,cAAAqC,EAAA,uBAAAA,EAAA,CAAE1G,OAAO,KAAI,EAAE,CAAC;MACtC,CAAC;IACF,EAAC,OAAOqE,KAAK,EAAE;MACdmD,MAAM,CAACnD,KAAK,CAAC;IACd;EACH,CAAC,CAAC;AACJ;AAEA;;;;AAIG;SACa8L,iBAAiBA,CAAA;EAC/B,IAAI,OAAOvC,SAAS,KAAK,WAAW,IAAI,CAACA,SAAS,CAACwC,aAAa,EAAE;IAChE,OAAO,KAAK;EACb;EACD,OAAO,IAAI;AACb;;ACnPA;;;;;;;;;;;;;;;AAeG;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCG;AAMH,MAAMC,UAAU,GAAG,eAAe;AAUlC;AACA;AACM,MAAOC,aAAc,SAAQpQ,KAAK;EAItC0D,Y;EAEW2M,IAAY,EACrBvQ,OAAe,E;EAERwQ,UAAoC;IAE3C,KAAK,CAACxQ,OAAO,CAAC;IALL,IAAI,CAAAuQ,IAAA,GAAJA,IAAI;IAGN,IAAU,CAAAC,UAAA,GAAVA,UAAU;;IAPV,IAAI,CAAA3M,IAAA,GAAWwM,UAAU;;;;;IAehCzL,MAAM,CAAC6L,cAAc,CAAC,IAAI,EAAEH,aAAa,CAACrC,SAAS,CAAC;;;IAIpD,IAAI/N,KAAK,CAACwQ,iBAAiB,EAAE;MAC3BxQ,KAAK,CAACwQ,iBAAiB,CAAC,IAAI,EAAEC,YAAY,CAAC1C,SAAS,CAAC2C,MAAM,CAAC;IAC7D;EACF;AACF;MAEYD,YAAY;EAIvB/M,YACmBiN,OAAe,EACfC,WAAmB,EACnBC,MAA2B;IAF3B,IAAO,CAAAF,OAAA,GAAPA,OAAO;IACP,IAAW,CAAAC,WAAA,GAAXA,WAAW;IACX,IAAM,CAAAC,MAAA,GAANA,MAAM;EACrB;EAEJH,MAAMA,CACJL,IAAO,EACP,GAAGS,IAAyD;IAE5D,MAAMR,UAAU,GAAIQ,IAAI,CAAC,CAAC,CAAe,IAAI,EAAE;IAC/C,MAAMC,QAAQ,GAAG,GAAG,IAAI,CAACJ,OAAO,IAAIN,IAAI,EAAE;IAC1C,MAAMW,QAAQ,GAAG,IAAI,CAACH,MAAM,CAACR,IAAI,CAAC;IAElC,MAAMvQ,OAAO,GAAGkR,QAAQ,GAAGC,eAAe,CAACD,QAAQ,EAAEV,UAAU,CAAC,GAAG,OAAO;;IAE1E,MAAMY,WAAW,GAAG,GAAG,IAAI,CAACN,WAAW,KAAK9Q,OAAO,KAAKiR,QAAQ,IAAI;IAEpE,MAAM5M,KAAK,GAAG,IAAIiM,aAAa,CAACW,QAAQ,EAAEG,WAAW,EAAEZ,UAAU,CAAC;IAElE,OAAOnM,KAAK;EACb;AACF;AAED,SAAS8M,eAAeA,CAACD,QAAgB,EAAEF,IAAe;EACxD,OAAOE,QAAQ,CAACjN,OAAO,CAACoN,OAAO,EAAE,CAACC,CAAC,EAAEnM,GAAG,KAAI;IAC1C,MAAMZ,KAAK,GAAGyM,IAAI,CAAC7L,GAAG,CAAC;IACvB,OAAOZ,KAAK,IAAI,IAAI,GAAGvD,MAAM,CAACuD,KAAK,CAAC,GAAG,IAAIY,GAAG,IAAI;EACpD,CAAC,CAAC;AACJ;AAEA,MAAMkM,OAAO,GAAG,eAAe;;ACvI/B;;;;;;;;;;;;;;;AAeG;AAEH;;;;;AAKG;AACG,SAAUE,QAAQA,CAAClR,GAAW;EAClC,OAAOyF,IAAI,CAACC,KAAK,CAAC1F,GAAG,CAAC;AACxB;AAEA;;;;AAIG;AACG,SAAUuJ,SAASA,CAACoH,IAAa;EACrC,OAAOlL,IAAI,CAAC8D,SAAS,CAACoH,IAAI,CAAC;AAC7B;;AClCA;;;;;;;;;;;;;;;AAeG;AAgBH;;;;;;AAMG;AACI,MAAMQ,MAAM,GAAG,SAAAA,CAAUhJ,KAAa;EAC3C,IAAIG,MAAM,GAAG,CAAE;IACb8I,MAAM,GAAW,EAAE;IACnBT,IAAI,GAAG,CAAE;IACTrH,SAAS,GAAG,EAAE;EAEhB,IAAI;IACF,MAAM+H,KAAK,GAAGlJ,KAAK,CAACmJ,KAAK,CAAC,GAAG,CAAC;IAC9BhJ,MAAM,GAAG4I,QAAQ,CAACrN,YAAY,CAACwN,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAW;IACzDD,MAAM,GAAGF,QAAQ,CAACrN,YAAY,CAACwN,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAW;IACzD/H,SAAS,GAAG+H,KAAK,CAAC,CAAC,CAAC;IACpBV,IAAI,GAAGS,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;IACxB,OAAOA,MAAM,CAAC,GAAG,CAAC;EACnB,EAAC,OAAOtN,CAAC,EAAE,CAAE;EAEd,OAAO;IACLwE,MAAM;IACN8I,MAAM;IACNT,IAAI;IACJrH;GACD;AACH,CAAE;AASF;;;;;;;AAOG;AACI,MAAMiI,gBAAgB,GAAG,SAAAA,CAAUpJ,KAAa;EACrD,MAAMiJ,MAAM,GAAWD,MAAM,CAAChJ,KAAK,CAAC,CAACiJ,MAAM;EAC3C,MAAMI,GAAG,GAAWC,IAAI,CAACC,KAAK,CAAC,IAAIlN,IAAI,EAAE,CAACE,OAAO,EAAE,GAAG,IAAI,CAAC;EAC3D,IAAIiN,UAAU,GAAW,CAAC;IACxBC,UAAU,GAAW,CAAC;EAExB,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAIA,MAAM,CAACxM,cAAc,CAAC,KAAK,CAAC,EAAE;MAChC+M,UAAU,GAAGP,MAAM,CAAC,KAAK,CAAW;IACrC,OAAM,IAAIA,MAAM,CAACxM,cAAc,CAAC,KAAK,CAAC,EAAE;MACvC+M,UAAU,GAAGP,MAAM,CAAC,KAAK,CAAW;IACrC;IAED,IAAIA,MAAM,CAACxM,cAAc,CAAC,KAAK,CAAC,EAAE;MAChCgN,UAAU,GAAGR,MAAM,CAAC,KAAK,CAAW;IACrC,OAAM;;MAELQ,UAAU,GAAGD,UAAU,GAAG,KAAK;IAChC;EACF;EAED,OACE,CAAC,CAACH,GAAG,IACL,CAAC,CAACG,UAAU,IACZ,CAAC,CAACC,UAAU,IACZJ,GAAG,IAAIG,UAAU,IACjBH,GAAG,IAAII,UAAU;AAErB,CAAE;AAEF;;;;;;AAMG;AACI,MAAMC,YAAY,GAAG,SAAAA,CAAU1J,KAAa;EACjD,MAAMiJ,MAAM,GAAWD,MAAM,CAAChJ,KAAK,CAAC,CAACiJ,MAAM;EAC3C,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACxM,cAAc,CAAC,KAAK,CAAC,EAAE;IAC9D,OAAOwM,MAAM,CAAC,KAAK,CAAW;EAC/B;EACD,OAAO,IAAI;AACb,CAAE;AAEF;;;;;;AAMG;AACI,MAAMU,aAAa,GAAG,SAAAA,CAAU3J,KAAa;EAClD,MAAMpC,OAAO,GAAGoL,MAAM,CAAChJ,KAAK,CAAC;IAC3BiJ,MAAM,GAAGrL,OAAO,CAACqL,MAAM;EAEzB,OAAO,CAAC,CAACA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACxM,cAAc,CAAC,KAAK,CAAC;AAC/E,CAAE;AAEF;;;;;;AAMG;AACI,MAAMmN,OAAO,GAAG,SAAAA,CAAU5J,KAAa;EAC5C,MAAMiJ,MAAM,GAAWD,MAAM,CAAChJ,KAAK,CAAC,CAACiJ,MAAM;EAC3C,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI;AAC/D;;ACjJA;;;;;;;;;;;;;;;AAeG;AAEa,SAAAY,QAAQA,CAAmBC,GAAM,EAAEnN,GAAW;EAC5D,OAAOP,MAAM,CAACqJ,SAAS,CAAChJ,cAAc,CAACkJ,IAAI,CAACmE,GAAG,EAAEnN,GAAG,CAAC;AACvD;AAEgB,SAAAoN,OAAOA,CACrBD,GAAM,EACNnN,GAAM;EAEN,IAAIP,MAAM,CAACqJ,SAAS,CAAChJ,cAAc,CAACkJ,IAAI,CAACmE,GAAG,EAAEnN,GAAG,CAAC,EAAE;IAClD,OAAOmN,GAAG,CAACnN,GAAG,CAAC;EAChB,OAAM;IACL,OAAOV,SAAS;EACjB;AACH;AAEM,SAAU+N,OAAOA,CAACF,GAAW;EACjC,KAAK,MAAMnN,GAAG,IAAImN,GAAG,EAAE;IACrB,IAAI1N,MAAM,CAACqJ,SAAS,CAAChJ,cAAc,CAACkJ,IAAI,CAACmE,GAAG,EAAEnN,GAAG,CAAC,EAAE;MAClD,OAAO,KAAK;IACb;EACF;EACD,OAAO,IAAI;AACb;SAEgBsN,GAAGA,CACjBH,GAAsB,EACtBI,EAAmD,EACnDC,UAAoB;EAEpB,MAAMC,GAAG,GAA+B,EAAE;EAC1C,KAAK,MAAMzN,GAAG,IAAImN,GAAG,EAAE;IACrB,IAAI1N,MAAM,CAACqJ,SAAS,CAAChJ,cAAc,CAACkJ,IAAI,CAACmE,GAAG,EAAEnN,GAAG,CAAC,EAAE;MAClDyN,GAAG,CAACzN,GAAG,CAAC,GAAGuN,EAAE,CAACvE,IAAI,CAACwE,UAAU,EAAEL,GAAG,CAACnN,GAAG,CAAC,EAAEA,GAAG,EAAEmN,GAAG,CAAC;IACnD;EACF;EACD,OAAOM,GAAwB;AACjC;AAEA;;AAEG;AACa,SAAAC,SAASA,CAACC,CAAS,EAAEC,CAAS;EAC5C,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;EACZ;EAED,MAAMC,KAAK,GAAGpO,MAAM,CAACsF,IAAI,CAAC4I,CAAC,CAAC;EAC5B,MAAMG,KAAK,GAAGrO,MAAM,CAACsF,IAAI,CAAC6I,CAAC,CAAC;EAC5B,KAAK,MAAMG,CAAC,IAAIF,KAAK,EAAE;IACrB,IAAI,CAACC,KAAK,CAAC5D,QAAQ,CAAC6D,CAAC,CAAC,EAAE;MACtB,OAAO,KAAK;IACb;IAED,MAAMC,KAAK,GAAIL,CAA6B,CAACI,CAAC,CAAC;IAC/C,MAAME,KAAK,GAAIL,CAA6B,CAACG,CAAC,CAAC;IAC/C,IAAIG,QAAQ,CAACF,KAAK,CAAC,IAAIE,QAAQ,CAACD,KAAK,CAAC,EAAE;MACtC,IAAI,CAACP,SAAS,CAACM,KAAK,EAAEC,KAAK,CAAC,EAAE;QAC5B,OAAO,KAAK;MACb;IACF,OAAM,IAAID,KAAK,KAAKC,KAAK,EAAE;MAC1B,OAAO,KAAK;IACb;EACF;EAED,KAAK,MAAMF,CAAC,IAAID,KAAK,EAAE;IACrB,IAAI,CAACD,KAAK,CAAC3D,QAAQ,CAAC6D,CAAC,CAAC,EAAE;MACtB,OAAO,KAAK;IACb;EACF;EACD,OAAO,IAAI;AACb;AAEA,SAASG,QAAQA,CAACC,KAAc;EAC9B,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ;AACpD;;AC3FA;;;;;;;;;;;;;;;AAeG;AAIH;;;AAGG;SACaC,kBAAkBA,CAChC7L,OAAmB,EACnB8L,QAAQ,GAAG,IAAI;EAEf,MAAMC,eAAe,GAAG,IAAIlM,QAAQ,EAAK;EACzCmM,UAAU,CAAC,MAAMD,eAAe,CAACjM,MAAM,CAAC,UAAU,CAAC,EAAEgM,QAAQ,CAAC;EAC9D9L,OAAO,CAACiM,IAAI,CAACF,eAAe,CAAChM,OAAO,EAAEgM,eAAe,CAACjM,MAAM,CAAC;EAC7D,OAAOiM,eAAe,CAAC/L,OAAO;AAChC;;AC/BA;;;;;;;;;;;;;;;AAeG;AAEH;;;;AAIG;AACG,SAAUkM,WAAWA,CAACC,iBAE3B;EACC,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,MAAM,CAAC3O,GAAG,EAAEZ,KAAK,CAAC,IAAIK,MAAM,CAACmP,OAAO,CAACF,iBAAiB,CAAC,EAAE;IAC5D,IAAIzR,KAAK,CAACC,OAAO,CAACkC,KAAK,CAAC,EAAE;MACxBA,KAAK,CAACyP,OAAO,CAACC,QAAQ,IAAG;QACvBH,MAAM,CAAC5Q,IAAI,CACTgR,kBAAkB,CAAC/O,GAAG,CAAC,GAAG,GAAG,GAAG+O,kBAAkB,CAACD,QAAQ,CAAC,CAC7D;MACH,CAAC,CAAC;IACH,OAAM;MACLH,MAAM,CAAC5Q,IAAI,CAACgR,kBAAkB,CAAC/O,GAAG,CAAC,GAAG,GAAG,GAAG+O,kBAAkB,CAAC3P,KAAK,CAAC,CAAC;IACvE;EACF;EACD,OAAOuP,MAAM,CAACrT,MAAM,GAAG,GAAG,GAAGqT,MAAM,CAACxS,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;AACpD;AAEA;;;AAGG;AACG,SAAU6S,iBAAiBA,CAACP,WAAmB;EACnD,MAAMtB,GAAG,GAA2B,EAAE;EACtC,MAAM8B,MAAM,GAAGR,WAAW,CAAC3P,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC0N,KAAK,CAAC,GAAG,CAAC;EAExDyC,MAAM,CAACJ,OAAO,CAACxL,KAAK,IAAG;IACrB,IAAIA,KAAK,EAAE;MACT,MAAM,CAACrD,GAAG,EAAEZ,KAAK,CAAC,GAAGiE,KAAK,CAACmJ,KAAK,CAAC,GAAG,CAAC;MACrCW,GAAG,CAAC+B,kBAAkB,CAAClP,GAAG,CAAC,CAAC,GAAGkP,kBAAkB,CAAC9P,KAAK,CAAC;IACzD;EACH,CAAC,CAAC;EACF,OAAO+N,GAAG;AACZ;AAEA;;AAEG;AACG,SAAUgC,kBAAkBA,CAACC,GAAW;EAC5C,MAAMC,UAAU,GAAGD,GAAG,CAACxF,OAAO,CAAC,GAAG,CAAC;EACnC,IAAI,CAACyF,UAAU,EAAE;IACf,OAAO,EAAE;EACV;EACD,MAAMC,aAAa,GAAGF,GAAG,CAACxF,OAAO,CAAC,GAAG,EAAEyF,UAAU,CAAC;EAClD,OAAOD,GAAG,CAACpN,SAAS,CAClBqN,UAAU,EACVC,aAAa,GAAG,CAAC,GAAGA,aAAa,GAAGhQ,SAAS,CAC9C;AACH;;ACtEA;;;;;;;;;;;;;;;AAeG;AAEH;;;;;;;;;;;;;;AAcG;AAEH;;;;;;;AAOG;MACUiQ,IAAI;EAuCf9Q,YAAA;IAtCA;;;;AAIG;IACK,IAAM,CAAA+Q,MAAA,GAAa,EAAE;IAE7B;;;AAGG;IACK,IAAI,CAAAC,IAAA,GAAa,EAAE;IAE3B;;;;AAIG;IACK,IAAE,CAAAC,EAAA,GAAa,EAAE;IAEzB;;;AAGG;IACK,IAAI,CAAAC,IAAA,GAAa,EAAE;IAE3B;;AAEG;IACK,IAAM,CAAAC,MAAA,GAAW,CAAC;IAE1B;;AAEG;IACK,IAAM,CAAAC,MAAA,GAAW,CAAC;IAKxB,IAAI,CAACC,SAAS,GAAG,GAAG,GAAG,CAAC;IAExB,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;IAClB,KAAK,IAAItU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyU,SAAS,EAAE,EAAEzU,CAAC,EAAE;MACvC,IAAI,CAACsU,IAAI,CAACtU,CAAC,CAAC,GAAG,CAAC;IACjB;IAED,IAAI,CAAC0U,KAAK,EAAE;EACb;EAEDA,KAAKA,CAAA;IACH,IAAI,CAACP,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAE3B,IAAI,CAACI,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;EAChB;EAED;;;;;AAKG;EACHG,SAASA,CAACC,GAAmC,EAAEC,MAAe;IAC5D,IAAI,CAACA,MAAM,EAAE;MACXA,MAAM,GAAG,CAAC;IACX;IAED,MAAMC,CAAC,GAAG,IAAI,CAACT,EAAE;;IAGjB,IAAI,OAAOO,GAAG,KAAK,QAAQ,EAAE;MAC3B,KAAK,IAAI5U,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;;;;;;;;;QAS3B8U,CAAC,CAAC9U,CAAC,CAAC,GACD4U,GAAG,CAACzU,UAAU,CAAC0U,MAAM,CAAC,IAAI,EAAE,GAC5BD,GAAG,CAACzU,UAAU,CAAC0U,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACjCD,GAAG,CAACzU,UAAU,CAAC0U,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACjCD,GAAG,CAACzU,UAAU,CAAC0U,MAAM,GAAG,CAAC,CAAC;QAC5BA,MAAM,IAAI,CAAC;MACZ;IACF,OAAM;MACL,KAAK,IAAI7U,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B8U,CAAC,CAAC9U,CAAC,CAAC,GACD4U,GAAG,CAACC,MAAM,CAAC,IAAI,EAAE,GACjBD,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACtBD,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACtBD,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;QACjBA,MAAM,IAAI,CAAC;MACZ;IACF;;IAGD,KAAK,IAAI7U,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAM+U,CAAC,GAAGD,CAAC,CAAC9U,CAAC,GAAG,CAAC,CAAC,GAAG8U,CAAC,CAAC9U,CAAC,GAAG,CAAC,CAAC,GAAG8U,CAAC,CAAC9U,CAAC,GAAG,EAAE,CAAC,GAAG8U,CAAC,CAAC9U,CAAC,GAAG,EAAE,CAAC;MACrD8U,CAAC,CAAC9U,CAAC,CAAC,GAAG,CAAE+U,CAAC,IAAI,CAAC,GAAKA,CAAC,KAAK,EAAG,IAAI,UAAU;IAC5C;IAED,IAAIzC,CAAC,GAAG,IAAI,CAAC6B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI5B,CAAC,GAAG,IAAI,CAAC4B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIjU,CAAC,GAAG,IAAI,CAACiU,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIa,CAAC,GAAG,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIxQ,CAAC,GAAG,IAAI,CAACwQ,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIc,CAAC,EAAEvC,CAAC;;IAGR,KAAK,IAAI1S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,IAAIA,CAAC,GAAG,EAAE,EAAE;QACV,IAAIA,CAAC,GAAG,EAAE,EAAE;UACViV,CAAC,GAAGD,CAAC,GAAIzC,CAAC,IAAIrS,CAAC,GAAG8U,CAAC,CAAE;UACrBtC,CAAC,GAAG,UAAU;QACf,OAAM;UACLuC,CAAC,GAAG1C,CAAC,GAAGrS,CAAC,GAAG8U,CAAC;UACbtC,CAAC,GAAG,UAAU;QACf;MACF,OAAM;QACL,IAAI1S,CAAC,GAAG,EAAE,EAAE;UACViV,CAAC,GAAI1C,CAAC,GAAGrS,CAAC,GAAK8U,CAAC,IAAIzC,CAAC,GAAGrS,CAAC,CAAE;UAC3BwS,CAAC,GAAG,UAAU;QACf,OAAM;UACLuC,CAAC,GAAG1C,CAAC,GAAGrS,CAAC,GAAG8U,CAAC;UACbtC,CAAC,GAAG,UAAU;QACf;MACF;MAED,MAAMqC,CAAC,GAAI,CAAEzC,CAAC,IAAI,CAAC,GAAKA,CAAC,KAAK,EAAG,IAAI2C,CAAC,GAAGtR,CAAC,GAAG+O,CAAC,GAAGoC,CAAC,CAAC9U,CAAC,CAAC,GAAI,UAAU;MACnE2D,CAAC,GAAGqR,CAAC;MACLA,CAAC,GAAG9U,CAAC;MACLA,CAAC,GAAG,CAAEqS,CAAC,IAAI,EAAE,GAAKA,CAAC,KAAK,CAAE,IAAI,UAAU;MACxCA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGyC,CAAC;IACN;IAED,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG7B,CAAC,GAAI,UAAU;IAClD,IAAI,CAAC6B,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG5B,CAAC,GAAI,UAAU;IAClD,IAAI,CAAC4B,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGjU,CAAC,GAAI,UAAU;IAClD,IAAI,CAACiU,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGa,CAAC,GAAI,UAAU;IAClD,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGxQ,CAAC,GAAI,UAAU;EACnD;EAEDuR,MAAMA,CAAC7U,KAAsC,EAAEJ,MAAe;;IAE5D,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB;IACD;IAED,IAAIJ,MAAM,KAAKgE,SAAS,EAAE;MACxBhE,MAAM,GAAGI,KAAK,CAACJ,MAAM;IACtB;IAED,MAAMkV,gBAAgB,GAAGlV,MAAM,GAAG,IAAI,CAACwU,SAAS;IAChD,IAAIW,CAAC,GAAG,CAAC;;IAET,MAAMR,GAAG,GAAG,IAAI,CAACR,IAAI;IACrB,IAAIiB,KAAK,GAAG,IAAI,CAACd,MAAM;;IAGvB,OAAOa,CAAC,GAAGnV,MAAM,EAAE;;;;;MAKjB,IAAIoV,KAAK,KAAK,CAAC,EAAE;QACf,OAAOD,CAAC,IAAID,gBAAgB,EAAE;UAC5B,IAAI,CAACR,SAAS,CAACtU,KAAK,EAAE+U,CAAC,CAAC;UACxBA,CAAC,IAAI,IAAI,CAACX,SAAS;QACpB;MACF;MAED,IAAI,OAAOpU,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO+U,CAAC,GAAGnV,MAAM,EAAE;UACjB2U,GAAG,CAACS,KAAK,CAAC,GAAGhV,KAAK,CAACF,UAAU,CAACiV,CAAC,CAAC;UAChC,EAAEC,KAAK;UACP,EAAED,CAAC;UACH,IAAIC,KAAK,KAAK,IAAI,CAACZ,SAAS,EAAE;YAC5B,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC;YACnBS,KAAK,GAAG,CAAC;;YAET;UACD;QACF;MACF,OAAM;QACL,OAAOD,CAAC,GAAGnV,MAAM,EAAE;UACjB2U,GAAG,CAACS,KAAK,CAAC,GAAGhV,KAAK,CAAC+U,CAAC,CAAC;UACrB,EAAEC,KAAK;UACP,EAAED,CAAC;UACH,IAAIC,KAAK,KAAK,IAAI,CAACZ,SAAS,EAAE;YAC5B,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC;YACnBS,KAAK,GAAG,CAAC;;YAET;UACD;QACF;MACF;IACF;IAED,IAAI,CAACd,MAAM,GAAGc,KAAK;IACnB,IAAI,CAACb,MAAM,IAAIvU,MAAM;EACtB;;EAGDqV,MAAMA,CAAA;IACJ,MAAMA,MAAM,GAAa,EAAE;IAC3B,IAAIC,SAAS,GAAG,IAAI,CAACf,MAAM,GAAG,CAAC;;IAG/B,IAAI,IAAI,CAACD,MAAM,GAAG,EAAE,EAAE;MACpB,IAAI,CAACW,MAAM,CAAC,IAAI,CAACZ,IAAI,EAAE,EAAE,GAAG,IAAI,CAACC,MAAM,CAAC;IACzC,OAAM;MACL,IAAI,CAACW,MAAM,CAAC,IAAI,CAACZ,IAAI,EAAE,IAAI,CAACG,SAAS,IAAI,IAAI,CAACF,MAAM,GAAG,EAAE,CAAC,CAAC;IAC5D;;IAGD,KAAK,IAAIvU,CAAC,GAAG,IAAI,CAACyU,SAAS,GAAG,CAAC,EAAEzU,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC7C,IAAI,CAACoU,IAAI,CAACpU,CAAC,CAAC,GAAGuV,SAAS,GAAG,GAAG;MAC9BA,SAAS,IAAI,GAAG,CAAC;IAClB;IAED,IAAI,CAACZ,SAAS,CAAC,IAAI,CAACP,IAAI,CAAC;IAEzB,IAAIgB,CAAC,GAAG,CAAC;IACT,KAAK,IAAIpV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,KAAK,IAAIwV,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QAC/BF,MAAM,CAACF,CAAC,CAAC,GAAI,IAAI,CAACjB,MAAM,CAACnU,CAAC,CAAC,IAAIwV,CAAC,GAAI,GAAG;QACvC,EAAEJ,CAAC;MACJ;IACF;IACD,OAAOE,MAAM;EACd;AACF;;ACrOD;;;;;;;AAOG;AACa,SAAAG,eAAeA,CAC7BC,QAAqB,EACrBC,aAA2B;EAE3B,MAAMC,KAAK,GAAG,IAAIC,aAAa,CAAIH,QAAQ,EAAEC,aAAa,CAAC;EAC3D,OAAOC,KAAK,CAACE,SAAS,CAACC,IAAI,CAACH,KAAK,CAAC;AACpC;AAEA;;;AAGG;AACH,MAAMC,aAAa;EAUjB;;;;AAIG;EACHzS,WAAYA,CAAAsS,QAAqB,EAAEC,aAA2B;IAdtD,IAAS,CAAAK,SAAA,GAAmC,EAAE;IAC9C,IAAY,CAAAC,YAAA,GAAkB,EAAE;IAEhC,IAAa,CAAAC,aAAA,GAAG,CAAC;;IAEjB,KAAAC,IAAI,GAAGhP,OAAO,CAACF,OAAO,EAAE;IACxB,IAAS,CAAAmP,SAAA,GAAG,KAAK;IASvB,IAAI,CAACT,aAAa,GAAGA,aAAa;;;;IAIlC,IAAI,CAACQ,IAAI,CACNhD,IAAI,CAAC,MAAK;MACTuC,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,EACApO,KAAK,CAAC3D,CAAC,IAAG;MACT,IAAI,CAACE,KAAK,CAACF,CAAC,CAAC;IACf,CAAC,CAAC;EACL;EAED0S,IAAIA,CAACtS,KAAQ;IACX,IAAI,CAACuS,eAAe,CAAEC,QAAqB,IAAI;MAC7CA,QAAQ,CAACF,IAAI,CAACtS,KAAK,CAAC;IACtB,CAAC,CAAC;EACH;EAEDF,KAAKA,CAACA,KAAY;IAChB,IAAI,CAACyS,eAAe,CAAEC,QAAqB,IAAI;MAC7CA,QAAQ,CAAC1S,KAAK,CAACA,KAAK,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAAC0L,KAAK,CAAC1L,KAAK,CAAC;EAClB;EAED2S,QAAQA,CAAA;IACN,IAAI,CAACF,eAAe,CAAEC,QAAqB,IAAI;MAC7CA,QAAQ,CAACC,QAAQ,EAAE;IACrB,CAAC,CAAC;IACF,IAAI,CAACjH,KAAK,EAAE;EACb;EAED;;;;;AAKG;EACHuG,SAASA,CACPW,cAA+C,EAC/C5S,KAAe,EACf2S,QAAqB;IAErB,IAAID,QAAqB;IAEzB,IACEE,cAAc,KAAKxS,SAAS,IAC5BJ,KAAK,KAAKI,SAAS,IACnBuS,QAAQ,KAAKvS,SAAS,EACtB;MACA,MAAM,IAAIvE,KAAK,CAAC,mBAAmB,CAAC;IACrC;;IAGD,IACEgX,oBAAoB,CAACD,cAA4C,EAAE,CACjE,MAAM,EACN,OAAO,EACP,UAAU,CACX,CAAC,EACF;MACAF,QAAQ,GAAGE,cAA6B;IACzC,OAAM;MACLF,QAAQ,GAAG;QACTF,IAAI,EAAEI,cAA2B;QACjC5S,KAAK;QACL2S;OACc;IACjB;IAED,IAAID,QAAQ,CAACF,IAAI,KAAKpS,SAAS,EAAE;MAC/BsS,QAAQ,CAACF,IAAI,GAAGM,IAAiB;IAClC;IACD,IAAIJ,QAAQ,CAAC1S,KAAK,KAAKI,SAAS,EAAE;MAChCsS,QAAQ,CAAC1S,KAAK,GAAG8S,IAAe;IACjC;IACD,IAAIJ,QAAQ,CAACC,QAAQ,KAAKvS,SAAS,EAAE;MACnCsS,QAAQ,CAACC,QAAQ,GAAGG,IAAkB;IACvC;IAED,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAACd,IAAI,CAAC,IAAI,EAAE,IAAI,CAACC,SAAU,CAAC/V,MAAM,CAAC;;;;IAKpE,IAAI,IAAI,CAACmW,SAAS,EAAE;;MAElB,IAAI,CAACD,IAAI,CAAChD,IAAI,CAAC,MAAK;QAClB,IAAI;UACF,IAAI,IAAI,CAAC2D,UAAU,EAAE;YACnBP,QAAQ,CAAC1S,KAAK,CAAC,IAAI,CAACiT,UAAU,CAAC;UAChC,OAAM;YACLP,QAAQ,CAACC,QAAQ,EAAE;UACpB;QACF,EAAC,OAAO7S,CAAC,EAAE;;;QAGZ;MACF,CAAC,CAAC;IACH;IAED,IAAI,CAACqS,SAAU,CAACtT,IAAI,CAAC6T,QAAuB,CAAC;IAE7C,OAAOK,KAAK;EACb;;;EAIOC,cAAcA,CAAC7W,CAAS;IAC9B,IAAI,IAAI,CAACgW,SAAS,KAAK/R,SAAS,IAAI,IAAI,CAAC+R,SAAS,CAAChW,CAAC,CAAC,KAAKiE,SAAS,EAAE;MACnE;IACD;IAED,OAAO,IAAI,CAAC+R,SAAS,CAAChW,CAAC,CAAC;IAExB,IAAI,CAACkW,aAAa,IAAI,CAAC;IACvB,IAAI,IAAI,CAACA,aAAa,KAAK,CAAC,IAAI,IAAI,CAACP,aAAa,KAAK1R,SAAS,EAAE;MAChE,IAAI,CAAC0R,aAAa,CAAC,IAAI,CAAC;IACzB;EACF;EAEOW,eAAeA,CAACpE,EAAmC;IACzD,IAAI,IAAI,CAACkE,SAAS,EAAE;;MAElB;IACD;;;IAID,KAAK,IAAIpW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACgW,SAAU,CAAC/V,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,IAAI,CAAC+W,OAAO,CAAC/W,CAAC,EAAEkS,EAAE,CAAC;IACpB;EACF;;;;EAKO6E,OAAOA,CAAC/W,CAAS,EAAEkS,EAAmC;;;IAG5D,IAAI,CAACiE,IAAI,CAAChD,IAAI,CAAC,MAAK;MAClB,IAAI,IAAI,CAAC6C,SAAS,KAAK/R,SAAS,IAAI,IAAI,CAAC+R,SAAS,CAAChW,CAAC,CAAC,KAAKiE,SAAS,EAAE;QACnE,IAAI;UACFiO,EAAE,CAAC,IAAI,CAAC8D,SAAS,CAAChW,CAAC,CAAC,CAAC;QACtB,EAAC,OAAO2D,CAAC,EAAE;;;;UAIV,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,KAAK,EAAE;YACnDD,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;UACjB;QACF;MACF;IACH,CAAC,CAAC;EACH;EAEO4L,KAAKA,CAACyH,GAAW;IACvB,IAAI,IAAI,CAACZ,SAAS,EAAE;MAClB;IACD;IACD,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAIY,GAAG,KAAK/S,SAAS,EAAE;MACrB,IAAI,CAAC6S,UAAU,GAAGE,GAAG;IACtB;;;IAGD,IAAI,CAACb,IAAI,CAAChD,IAAI,CAAC,MAAK;MAClB,IAAI,CAAC6C,SAAS,GAAG/R,SAAS;MAC1B,IAAI,CAAC0R,aAAa,GAAG1R,SAAS;IAChC,CAAC,CAAC;EACH;AACF;AAED;AACA;AACgB,SAAAgT,KAAKA,CAAC/E,EAAY,EAAEgF,OAAiB;EACnD,OAAO,CAAC,GAAGC,IAAe,KAAI;IAC5BhQ,OAAO,CAACF,OAAO,CAAC,IAAI,EACjBkM,IAAI,CAAC,MAAK;MACTjB,EAAE,CAAC,GAAGiF,IAAI,CAAC;IACb,CAAC,EACA7P,KAAK,CAAEzD,KAAY,IAAI;MACtB,IAAIqT,OAAO,EAAE;QACXA,OAAO,CAACrT,KAAK,CAAC;MACf;IACH,CAAC,CAAC;EACN,CAAC;AACH;AAEA;;AAEG;AACH,SAAS6S,oBAAoBA,CAC3B5E,GAA+B,EAC/BsF,OAAiB;EAEjB,IAAI,OAAOtF,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IAC3C,OAAO,KAAK;EACb;EAED,KAAK,MAAMuF,MAAM,IAAID,OAAO,EAAE;IAC5B,IAAIC,MAAM,IAAIvF,GAAG,IAAI,OAAOA,GAAG,CAACuF,MAAM,CAAC,KAAK,UAAU,EAAE;MACtD,OAAO,IAAI;IACZ;EACF;EAED,OAAO,KAAK;AACd;AAEA,SAASV,IAAIA,CAAA;;;;AC1Sb;;;;;;;;;;;;;;;AAeG;AAEH;;;;;;;;AAQG;AACU,MAAAW,gBAAgB,GAAG,SAAAA,CAC9BC,MAAc,EACdC,QAAgB,EAChBC,QAAgB,EAChBC,QAAgB;EAEhB,IAAIC,QAAQ;EACZ,IAAID,QAAQ,GAAGF,QAAQ,EAAE;IACvBG,QAAQ,GAAG,WAAW,GAAGH,QAAQ;EAClC,OAAM,IAAIE,QAAQ,GAAGD,QAAQ,EAAE;IAC9BE,QAAQ,GAAGF,QAAQ,KAAK,CAAC,GAAG,MAAM,GAAG,eAAe,GAAGA,QAAQ;EAChE;EACD,IAAIE,QAAQ,EAAE;IACZ,MAAM9T,KAAK,GACT0T,MAAM,GACN,2BAA2B,GAC3BG,QAAQ,IACPA,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,aAAa,CAAC,GAC/C,WAAW,GACXC,QAAQ,GACR,GAAG;IACL,MAAM,IAAIjY,KAAK,CAACmE,KAAK,CAAC;EACvB;AACH,CAAE;AAEF;;;;;;AAMG;AACa,SAAA+T,WAAWA,CAACL,MAAc,EAAEM,OAAe;EACzD,OAAO,GAAGN,MAAM,YAAYM,OAAO,YAAY;AACjD;AAEA;;;;;AAKG;SACaC,iBAAiBA,CAC/BP,MAAc,EACdQ,SAAiB,EACjBC,QAAiB;EAEjB,IAAIA,QAAQ,IAAI,CAACD,SAAS,EAAE;IAC1B;EACD;EACD,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;;IAEjC,MAAM,IAAIrY,KAAK,CACbkY,WAAW,CAACL,MAAM,EAAE,WAAW,CAAC,GAAG,qCAAqC,CACzE;EACF;AACH;AAEgB,SAAAU,gBAAgBA,CAC9BV,MAAc,EACdW,YAAoB;AACpB;AACA7Q,QAAkB,EAClB2Q,QAAiB;EAEjB,IAAIA,QAAQ,IAAI,CAAC3Q,QAAQ,EAAE;IACzB;EACD;EACD,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAClC,MAAM,IAAI3H,KAAK,CACbkY,WAAW,CAACL,MAAM,EAAEW,YAAY,CAAC,GAAG,2BAA2B,CAChE;EACF;AACH;AAEM,SAAUC,qBAAqBA,CACnCZ,MAAc,EACdW,YAAoB,EACpBE,OAAgB,EAChBJ,QAAiB;EAEjB,IAAIA,QAAQ,IAAI,CAACI,OAAO,EAAE;IACxB;EACD;EACD,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;IACnD,MAAM,IAAI1Y,KAAK,CACbkY,WAAW,CAACL,MAAM,EAAEW,YAAY,CAAC,GAAG,iCAAiC,CACtE;EACF;AACH;;ACnHA;;;;;;;;;;;;;;;AAeG;AAIH;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAGG;AACI,MAAMtY,iBAAiB,GAAG,SAAAA,CAAUC,GAAW;EACpD,MAAMC,GAAG,GAAa,EAAE;EACxB,IAAIC,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIE,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;;IAGzB,IAAIE,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;MAC9B,MAAMmY,IAAI,GAAGnY,CAAC,GAAG,MAAM,CAAC;MACxBF,CAAC,EAAE;MACHV,MAAM,CAACU,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAE,yCAAyC,CAAC;MACjE,MAAMqY,GAAG,GAAGzY,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC,GAAG,MAAM,CAAC;MACvCE,CAAC,GAAG,OAAO,IAAImY,IAAI,IAAI,EAAE,CAAC,GAAGC,GAAG;IACjC;IAED,IAAIpY,CAAC,GAAG,GAAG,EAAE;MACXJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAGG,CAAC;IACb,OAAM,IAAIA,CAAC,GAAG,IAAI,EAAE;MACnBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,CAAC,GAAI,GAAG;MACzBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC1B,OAAM,IAAIA,CAAC,GAAG,KAAK,EAAE;MACpBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC1B,OAAM;MACLJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,EAAE,GAAI,EAAE,GAAI,GAAG;MACjCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC1B;EACF;EACD,OAAOJ,GAAG;AACZ,CAAE;AAEF;;;;AAIG;AACI,MAAMyY,YAAY,GAAG,SAAAA,CAAU1Y,GAAW;EAC/C,IAAIE,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,MAAME,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IAC3B,IAAIE,CAAC,GAAG,GAAG,EAAE;MACXH,CAAC,EAAE;IACJ,OAAM,IAAIG,CAAC,GAAG,IAAI,EAAE;MACnBH,CAAC,IAAI,CAAC;IACP,OAAM,IAAIG,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;;MAErCH,CAAC,IAAI,CAAC;MACNC,CAAC,EAAE,CAAC;IACL,OAAM;MACLD,CAAC,IAAI,CAAC;IACP;EACF;EACD,OAAOA,CAAC;AACV;;AC1FA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACH,MAAMyY,uBAAuB,GAAG,IAAI;AAEpC;;;AAGG;AACH,MAAMC,sBAAsB,GAAG,CAAC;AAEhC;;;;AAIG;AACI,MAAMC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAK;AAEnD;;;;;;;AAOG;AACI,MAAMC,aAAa,GAAG,GAAI;AAEjC;;;;AAIG;AACG,SAAUC,sBAAsBA,CACpCC,YAAoB,EACpBC,cAAyB,GAAAN,uBAAuB,EAChDO,aAAA,GAAwBN,sBAAsB;;;;EAK9C,MAAMO,aAAa,GAAGF,cAAc,GAAGxH,IAAI,CAAC2H,GAAG,CAACF,aAAa,EAAEF,YAAY,CAAC;;;EAI5E,MAAMK,UAAU,GAAG5H,IAAI,CAAC6H,KAAK;;;EAG3BR,aAAa,GACXK,aAAa;;;EAGZ1H,IAAI,CAAC8H,MAAM,CAAE,IAAG,GAAG,CAAC,GACrB,CAAC,CACJ;;EAGD,OAAO9H,IAAI,CAAC+H,GAAG,CAACX,gBAAgB,EAAEM,aAAa,GAAGE,UAAU,CAAC;AAC/D;;AC3EA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACG,SAAUI,OAAOA,CAACtZ,CAAS;EAC/B,IAAI,CAACuZ,MAAM,CAACC,QAAQ,CAACxZ,CAAC,CAAC,EAAE;IACvB,OAAO,GAAGA,CAAC,EAAE;EACd;EACD,OAAOA,CAAC,GAAGyZ,SAAS,CAACzZ,CAAC,CAAC;AACzB;AAEA,SAASyZ,SAASA,CAACzZ,CAAS;EAC1BA,CAAC,GAAGsR,IAAI,CAACoI,GAAG,CAAC1Z,CAAC,CAAC;EACf,MAAM2Z,IAAI,GAAG3Z,CAAC,GAAG,GAAG;EACpB,IAAI2Z,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,EAAE;IAC5B,OAAO,IAAI;EACZ;EACD,MAAMC,GAAG,GAAG5Z,CAAC,GAAG,EAAE;EAClB,IAAI4Z,GAAG,KAAK,CAAC,EAAE;IACb,OAAO,IAAI;EACZ;EACD,IAAIA,GAAG,KAAK,CAAC,EAAE;IACb,OAAO,IAAI;EACZ;EACD,IAAIA,GAAG,KAAK,CAAC,EAAE;IACb,OAAO,IAAI;EACZ;EACD,OAAO,IAAI;AACb;;AC5CA;;;;;;;;;;;;;;;AAeG;AAMG,SAAUC,kBAAkBA,CAChCxJ,OAAwC;EAExC,IAAIA,OAAO,IAAKA,OAA8B,CAACyJ,SAAS,EAAE;IACxD,OAAQzJ,OAA8B,CAACyJ,SAAS;EACjD,OAAM;IACL,OAAOzJ,OAAqB;EAC7B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}