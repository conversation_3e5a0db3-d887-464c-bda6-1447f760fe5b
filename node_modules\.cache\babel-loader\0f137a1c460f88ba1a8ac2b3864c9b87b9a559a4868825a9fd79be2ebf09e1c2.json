{"ast": null, "code": "// A derivative work based on:\n// <https://github.com/browserify/path-browserify>.\n// Which is licensed:\n//\n// MIT License\n//\n// Copyright (c) 2013 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of\n// this software and associated documentation files (the \"Software\"), to deal in\n// the Software without restriction, including without limitation the rights to\n// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n// the Software, and to permit persons to whom the Software is furnished to do so,\n// subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\n// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\n// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n// A derivative work based on:\n//\n// Parts of that are extracted from Node’s internal `path` module:\n// <https://github.com/nodejs/node/blob/master/lib/path.js>.\n// Which is licensed:\n//\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nexport const minpath = {\n  basename,\n  dirname,\n  extname,\n  join,\n  sep: '/'\n};\n\n/* eslint-disable max-depth, complexity */\n\n/**\n * Get the basename from a path.\n *\n * @param {string} path\n *   File path.\n * @param {string | null | undefined} [extname]\n *   Extension to strip.\n * @returns {string}\n *   Stem or basename.\n */\nfunction basename(path, extname) {\n  if (extname !== undefined && typeof extname !== 'string') {\n    throw new TypeError('\"ext\" argument must be a string');\n  }\n  assertPath(path);\n  let start = 0;\n  let end = -1;\n  let index = path.length;\n  /** @type {boolean | undefined} */\n  let seenNonSlash;\n  if (extname === undefined || extname.length === 0 || extname.length > path.length) {\n    while (index--) {\n      if (path.codePointAt(index) === 47 /* `/` */) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now.\n        if (seenNonSlash) {\n          start = index + 1;\n          break;\n        }\n      } else if (end < 0) {\n        // We saw the first non-path separator, mark this as the end of our\n        // path component.\n        seenNonSlash = true;\n        end = index + 1;\n      }\n    }\n    return end < 0 ? '' : path.slice(start, end);\n  }\n  if (extname === path) {\n    return '';\n  }\n  let firstNonSlashEnd = -1;\n  let extnameIndex = extname.length - 1;\n  while (index--) {\n    if (path.codePointAt(index) === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (seenNonSlash) {\n        start = index + 1;\n        break;\n      }\n    } else {\n      if (firstNonSlashEnd < 0) {\n        // We saw the first non-path separator, remember this index in case\n        // we need it if the extension ends up not matching.\n        seenNonSlash = true;\n        firstNonSlashEnd = index + 1;\n      }\n      if (extnameIndex > -1) {\n        // Try to match the explicit extension.\n        if (path.codePointAt(index) === extname.codePointAt(extnameIndex--)) {\n          if (extnameIndex < 0) {\n            // We matched the extension, so mark this as the end of our path\n            // component\n            end = index;\n          }\n        } else {\n          // Extension does not match, so our result is the entire path\n          // component\n          extnameIndex = -1;\n          end = firstNonSlashEnd;\n        }\n      }\n    }\n  }\n  if (start === end) {\n    end = firstNonSlashEnd;\n  } else if (end < 0) {\n    end = path.length;\n  }\n  return path.slice(start, end);\n}\n\n/**\n * Get the dirname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\nfunction dirname(path) {\n  assertPath(path);\n  if (path.length === 0) {\n    return '.';\n  }\n  let end = -1;\n  let index = path.length;\n  /** @type {boolean | undefined} */\n  let unmatchedSlash;\n\n  // Prefix `--` is important to not run on `0`.\n  while (--index) {\n    if (path.codePointAt(index) === 47 /* `/` */) {\n      if (unmatchedSlash) {\n        end = index;\n        break;\n      }\n    } else if (!unmatchedSlash) {\n      // We saw the first non-path separator\n      unmatchedSlash = true;\n    }\n  }\n  return end < 0 ? path.codePointAt(0) === 47 /* `/` */ ? '/' : '.' : end === 1 && path.codePointAt(0) === 47 /* `/` */ ? '//' : path.slice(0, end);\n}\n\n/**\n * Get an extname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   Extname.\n */\nfunction extname(path) {\n  assertPath(path);\n  let index = path.length;\n  let end = -1;\n  let startPart = 0;\n  let startDot = -1;\n  // Track the state of characters (if any) we see before our first dot and\n  // after any path separator we find.\n  let preDotState = 0;\n  /** @type {boolean | undefined} */\n  let unmatchedSlash;\n  while (index--) {\n    const code = path.codePointAt(index);\n    if (code === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (unmatchedSlash) {\n        startPart = index + 1;\n        break;\n      }\n      continue;\n    }\n    if (end < 0) {\n      // We saw the first non-path separator, mark this as the end of our\n      // extension.\n      unmatchedSlash = true;\n      end = index + 1;\n    }\n    if (code === 46 /* `.` */) {\n      // If this is our first dot, mark it as the start of our extension.\n      if (startDot < 0) {\n        startDot = index;\n      } else if (preDotState !== 1) {\n        preDotState = 1;\n      }\n    } else if (startDot > -1) {\n      // We saw a non-dot and non-path separator before our dot, so we should\n      // have a good chance at having a non-empty extension.\n      preDotState = -1;\n    }\n  }\n  if (startDot < 0 || end < 0 ||\n  // We saw a non-dot character immediately before the dot.\n  preDotState === 0 ||\n  // The (right-most) trimmed path component is exactly `..`.\n  preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n    return '';\n  }\n  return path.slice(startDot, end);\n}\n\n/**\n * Join segments from a path.\n *\n * @param {Array<string>} segments\n *   Path segments.\n * @returns {string}\n *   File path.\n */\nfunction join(...segments) {\n  let index = -1;\n  /** @type {string | undefined} */\n  let joined;\n  while (++index < segments.length) {\n    assertPath(segments[index]);\n    if (segments[index]) {\n      joined = joined === undefined ? segments[index] : joined + '/' + segments[index];\n    }\n  }\n  return joined === undefined ? '.' : normalize(joined);\n}\n\n/**\n * Normalize a basic file path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\n// Note: `normalize` is not exposed as `path.normalize`, so some code is\n// manually removed from it.\nfunction normalize(path) {\n  assertPath(path);\n  const absolute = path.codePointAt(0) === 47; /* `/` */\n\n  // Normalize the path according to POSIX rules.\n  let value = normalizeString(path, !absolute);\n  if (value.length === 0 && !absolute) {\n    value = '.';\n  }\n  if (value.length > 0 && path.codePointAt(path.length - 1) === 47 /* / */) {\n    value += '/';\n  }\n  return absolute ? '/' + value : value;\n}\n\n/**\n * Resolve `.` and `..` elements in a path with directory names.\n *\n * @param {string} path\n *   File path.\n * @param {boolean} allowAboveRoot\n *   Whether `..` can move above root.\n * @returns {string}\n *   File path.\n */\nfunction normalizeString(path, allowAboveRoot) {\n  let result = '';\n  let lastSegmentLength = 0;\n  let lastSlash = -1;\n  let dots = 0;\n  let index = -1;\n  /** @type {number | undefined} */\n  let code;\n  /** @type {number} */\n  let lastSlashIndex;\n  while (++index <= path.length) {\n    if (index < path.length) {\n      code = path.codePointAt(index);\n    } else if (code === 47 /* `/` */) {\n      break;\n    } else {\n      code = 47; /* `/` */\n    }\n    if (code === 47 /* `/` */) {\n      if (lastSlash === index - 1 || dots === 1) {\n        // Empty.\n      } else if (lastSlash !== index - 1 && dots === 2) {\n        if (result.length < 2 || lastSegmentLength !== 2 || result.codePointAt(result.length - 1) !== 46 /* `.` */ || result.codePointAt(result.length - 2) !== 46 /* `.` */) {\n          if (result.length > 2) {\n            lastSlashIndex = result.lastIndexOf('/');\n            if (lastSlashIndex !== result.length - 1) {\n              if (lastSlashIndex < 0) {\n                result = '';\n                lastSegmentLength = 0;\n              } else {\n                result = result.slice(0, lastSlashIndex);\n                lastSegmentLength = result.length - 1 - result.lastIndexOf('/');\n              }\n              lastSlash = index;\n              dots = 0;\n              continue;\n            }\n          } else if (result.length > 0) {\n            result = '';\n            lastSegmentLength = 0;\n            lastSlash = index;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          result = result.length > 0 ? result + '/..' : '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (result.length > 0) {\n          result += '/' + path.slice(lastSlash + 1, index);\n        } else {\n          result = path.slice(lastSlash + 1, index);\n        }\n        lastSegmentLength = index - lastSlash - 1;\n      }\n      lastSlash = index;\n      dots = 0;\n    } else if (code === 46 /* `.` */ && dots > -1) {\n      dots++;\n    } else {\n      dots = -1;\n    }\n  }\n  return result;\n}\n\n/**\n * Make sure `path` is a string.\n *\n * @param {string} path\n *   File path.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n/* eslint-enable max-depth, complexity */", "map": {"version": 3, "names": ["min<PERSON>", "basename", "dirname", "extname", "join", "sep", "path", "undefined", "TypeError", "assertPath", "start", "end", "index", "length", "seenNonSlash", "codePointAt", "slice", "firstNonSlashEnd", "extnameIndex", "unmatchedSlash", "startPart", "startDot", "preDotState", "code", "segments", "joined", "normalize", "absolute", "value", "normalizeString", "allowAboveRoot", "result", "lastSegmentLength", "lastSlash", "dots", "lastSlashIndex", "lastIndexOf", "JSON", "stringify"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/vfile/lib/minpath.browser.js"], "sourcesContent": ["// A derivative work based on:\n// <https://github.com/browserify/path-browserify>.\n// Which is licensed:\n//\n// MIT License\n//\n// Copyright (c) 2013 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of\n// this software and associated documentation files (the \"Software\"), to deal in\n// the Software without restriction, including without limitation the rights to\n// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n// the Software, and to permit persons to whom the Software is furnished to do so,\n// subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\n// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\n// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n// A derivative work based on:\n//\n// Parts of that are extracted from Node’s internal `path` module:\n// <https://github.com/nodejs/node/blob/master/lib/path.js>.\n// Which is licensed:\n//\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nexport const minpath = {basename, dirname, extname, join, sep: '/'}\n\n/* eslint-disable max-depth, complexity */\n\n/**\n * Get the basename from a path.\n *\n * @param {string} path\n *   File path.\n * @param {string | null | undefined} [extname]\n *   Extension to strip.\n * @returns {string}\n *   Stem or basename.\n */\nfunction basename(path, extname) {\n  if (extname !== undefined && typeof extname !== 'string') {\n    throw new TypeError('\"ext\" argument must be a string')\n  }\n\n  assertPath(path)\n  let start = 0\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let seenNonSlash\n\n  if (\n    extname === undefined ||\n    extname.length === 0 ||\n    extname.length > path.length\n  ) {\n    while (index--) {\n      if (path.codePointAt(index) === 47 /* `/` */) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now.\n        if (seenNonSlash) {\n          start = index + 1\n          break\n        }\n      } else if (end < 0) {\n        // We saw the first non-path separator, mark this as the end of our\n        // path component.\n        seenNonSlash = true\n        end = index + 1\n      }\n    }\n\n    return end < 0 ? '' : path.slice(start, end)\n  }\n\n  if (extname === path) {\n    return ''\n  }\n\n  let firstNonSlashEnd = -1\n  let extnameIndex = extname.length - 1\n\n  while (index--) {\n    if (path.codePointAt(index) === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (seenNonSlash) {\n        start = index + 1\n        break\n      }\n    } else {\n      if (firstNonSlashEnd < 0) {\n        // We saw the first non-path separator, remember this index in case\n        // we need it if the extension ends up not matching.\n        seenNonSlash = true\n        firstNonSlashEnd = index + 1\n      }\n\n      if (extnameIndex > -1) {\n        // Try to match the explicit extension.\n        if (path.codePointAt(index) === extname.codePointAt(extnameIndex--)) {\n          if (extnameIndex < 0) {\n            // We matched the extension, so mark this as the end of our path\n            // component\n            end = index\n          }\n        } else {\n          // Extension does not match, so our result is the entire path\n          // component\n          extnameIndex = -1\n          end = firstNonSlashEnd\n        }\n      }\n    }\n  }\n\n  if (start === end) {\n    end = firstNonSlashEnd\n  } else if (end < 0) {\n    end = path.length\n  }\n\n  return path.slice(start, end)\n}\n\n/**\n * Get the dirname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\nfunction dirname(path) {\n  assertPath(path)\n\n  if (path.length === 0) {\n    return '.'\n  }\n\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  // Prefix `--` is important to not run on `0`.\n  while (--index) {\n    if (path.codePointAt(index) === 47 /* `/` */) {\n      if (unmatchedSlash) {\n        end = index\n        break\n      }\n    } else if (!unmatchedSlash) {\n      // We saw the first non-path separator\n      unmatchedSlash = true\n    }\n  }\n\n  return end < 0\n    ? path.codePointAt(0) === 47 /* `/` */\n      ? '/'\n      : '.'\n    : end === 1 && path.codePointAt(0) === 47 /* `/` */\n      ? '//'\n      : path.slice(0, end)\n}\n\n/**\n * Get an extname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   Extname.\n */\nfunction extname(path) {\n  assertPath(path)\n\n  let index = path.length\n\n  let end = -1\n  let startPart = 0\n  let startDot = -1\n  // Track the state of characters (if any) we see before our first dot and\n  // after any path separator we find.\n  let preDotState = 0\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  while (index--) {\n    const code = path.codePointAt(index)\n\n    if (code === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (unmatchedSlash) {\n        startPart = index + 1\n        break\n      }\n\n      continue\n    }\n\n    if (end < 0) {\n      // We saw the first non-path separator, mark this as the end of our\n      // extension.\n      unmatchedSlash = true\n      end = index + 1\n    }\n\n    if (code === 46 /* `.` */) {\n      // If this is our first dot, mark it as the start of our extension.\n      if (startDot < 0) {\n        startDot = index\n      } else if (preDotState !== 1) {\n        preDotState = 1\n      }\n    } else if (startDot > -1) {\n      // We saw a non-dot and non-path separator before our dot, so we should\n      // have a good chance at having a non-empty extension.\n      preDotState = -1\n    }\n  }\n\n  if (\n    startDot < 0 ||\n    end < 0 ||\n    // We saw a non-dot character immediately before the dot.\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly `..`.\n    (preDotState === 1 && startDot === end - 1 && startDot === startPart + 1)\n  ) {\n    return ''\n  }\n\n  return path.slice(startDot, end)\n}\n\n/**\n * Join segments from a path.\n *\n * @param {Array<string>} segments\n *   Path segments.\n * @returns {string}\n *   File path.\n */\nfunction join(...segments) {\n  let index = -1\n  /** @type {string | undefined} */\n  let joined\n\n  while (++index < segments.length) {\n    assertPath(segments[index])\n\n    if (segments[index]) {\n      joined =\n        joined === undefined ? segments[index] : joined + '/' + segments[index]\n    }\n  }\n\n  return joined === undefined ? '.' : normalize(joined)\n}\n\n/**\n * Normalize a basic file path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\n// Note: `normalize` is not exposed as `path.normalize`, so some code is\n// manually removed from it.\nfunction normalize(path) {\n  assertPath(path)\n\n  const absolute = path.codePointAt(0) === 47 /* `/` */\n\n  // Normalize the path according to POSIX rules.\n  let value = normalizeString(path, !absolute)\n\n  if (value.length === 0 && !absolute) {\n    value = '.'\n  }\n\n  if (value.length > 0 && path.codePointAt(path.length - 1) === 47 /* / */) {\n    value += '/'\n  }\n\n  return absolute ? '/' + value : value\n}\n\n/**\n * Resolve `.` and `..` elements in a path with directory names.\n *\n * @param {string} path\n *   File path.\n * @param {boolean} allowAboveRoot\n *   Whether `..` can move above root.\n * @returns {string}\n *   File path.\n */\nfunction normalizeString(path, allowAboveRoot) {\n  let result = ''\n  let lastSegmentLength = 0\n  let lastSlash = -1\n  let dots = 0\n  let index = -1\n  /** @type {number | undefined} */\n  let code\n  /** @type {number} */\n  let lastSlashIndex\n\n  while (++index <= path.length) {\n    if (index < path.length) {\n      code = path.codePointAt(index)\n    } else if (code === 47 /* `/` */) {\n      break\n    } else {\n      code = 47 /* `/` */\n    }\n\n    if (code === 47 /* `/` */) {\n      if (lastSlash === index - 1 || dots === 1) {\n        // Empty.\n      } else if (lastSlash !== index - 1 && dots === 2) {\n        if (\n          result.length < 2 ||\n          lastSegmentLength !== 2 ||\n          result.codePointAt(result.length - 1) !== 46 /* `.` */ ||\n          result.codePointAt(result.length - 2) !== 46 /* `.` */\n        ) {\n          if (result.length > 2) {\n            lastSlashIndex = result.lastIndexOf('/')\n\n            if (lastSlashIndex !== result.length - 1) {\n              if (lastSlashIndex < 0) {\n                result = ''\n                lastSegmentLength = 0\n              } else {\n                result = result.slice(0, lastSlashIndex)\n                lastSegmentLength = result.length - 1 - result.lastIndexOf('/')\n              }\n\n              lastSlash = index\n              dots = 0\n              continue\n            }\n          } else if (result.length > 0) {\n            result = ''\n            lastSegmentLength = 0\n            lastSlash = index\n            dots = 0\n            continue\n          }\n        }\n\n        if (allowAboveRoot) {\n          result = result.length > 0 ? result + '/..' : '..'\n          lastSegmentLength = 2\n        }\n      } else {\n        if (result.length > 0) {\n          result += '/' + path.slice(lastSlash + 1, index)\n        } else {\n          result = path.slice(lastSlash + 1, index)\n        }\n\n        lastSegmentLength = index - lastSlash - 1\n      }\n\n      lastSlash = index\n      dots = 0\n    } else if (code === 46 /* `.` */ && dots > -1) {\n      dots++\n    } else {\n      dots = -1\n    }\n  }\n\n  return result\n}\n\n/**\n * Make sure `path` is a string.\n *\n * @param {string} path\n *   File path.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError(\n      'Path must be a string. Received ' + JSON.stringify(path)\n    )\n  }\n}\n\n/* eslint-enable max-depth, complexity */\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,OAAO,GAAG;EAACC,QAAQ;EAAEC,OAAO;EAAEC,OAAO;EAAEC,IAAI;EAAEC,GAAG,EAAE;AAAG,CAAC;;AAEnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,QAAQA,CAACK,IAAI,EAAEH,OAAO,EAAE;EAC/B,IAAIA,OAAO,KAAKI,SAAS,IAAI,OAAOJ,OAAO,KAAK,QAAQ,EAAE;IACxD,MAAM,IAAIK,SAAS,CAAC,iCAAiC,CAAC;EACxD;EAEAC,UAAU,CAACH,IAAI,CAAC;EAChB,IAAII,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,KAAK,GAAGN,IAAI,CAACO,MAAM;EACvB;EACA,IAAIC,YAAY;EAEhB,IACEX,OAAO,KAAKI,SAAS,IACrBJ,OAAO,CAACU,MAAM,KAAK,CAAC,IACpBV,OAAO,CAACU,MAAM,GAAGP,IAAI,CAACO,MAAM,EAC5B;IACA,OAAOD,KAAK,EAAE,EAAE;MACd,IAAIN,IAAI,CAACS,WAAW,CAACH,KAAK,CAAC,KAAK,EAAE,CAAC,WAAW;QAC5C;QACA;QACA,IAAIE,YAAY,EAAE;UAChBJ,KAAK,GAAGE,KAAK,GAAG,CAAC;UACjB;QACF;MACF,CAAC,MAAM,IAAID,GAAG,GAAG,CAAC,EAAE;QAClB;QACA;QACAG,YAAY,GAAG,IAAI;QACnBH,GAAG,GAAGC,KAAK,GAAG,CAAC;MACjB;IACF;IAEA,OAAOD,GAAG,GAAG,CAAC,GAAG,EAAE,GAAGL,IAAI,CAACU,KAAK,CAACN,KAAK,EAAEC,GAAG,CAAC;EAC9C;EAEA,IAAIR,OAAO,KAAKG,IAAI,EAAE;IACpB,OAAO,EAAE;EACX;EAEA,IAAIW,gBAAgB,GAAG,CAAC,CAAC;EACzB,IAAIC,YAAY,GAAGf,OAAO,CAACU,MAAM,GAAG,CAAC;EAErC,OAAOD,KAAK,EAAE,EAAE;IACd,IAAIN,IAAI,CAACS,WAAW,CAACH,KAAK,CAAC,KAAK,EAAE,CAAC,WAAW;MAC5C;MACA;MACA,IAAIE,YAAY,EAAE;QAChBJ,KAAK,GAAGE,KAAK,GAAG,CAAC;QACjB;MACF;IACF,CAAC,MAAM;MACL,IAAIK,gBAAgB,GAAG,CAAC,EAAE;QACxB;QACA;QACAH,YAAY,GAAG,IAAI;QACnBG,gBAAgB,GAAGL,KAAK,GAAG,CAAC;MAC9B;MAEA,IAAIM,YAAY,GAAG,CAAC,CAAC,EAAE;QACrB;QACA,IAAIZ,IAAI,CAACS,WAAW,CAACH,KAAK,CAAC,KAAKT,OAAO,CAACY,WAAW,CAACG,YAAY,EAAE,CAAC,EAAE;UACnE,IAAIA,YAAY,GAAG,CAAC,EAAE;YACpB;YACA;YACAP,GAAG,GAAGC,KAAK;UACb;QACF,CAAC,MAAM;UACL;UACA;UACAM,YAAY,GAAG,CAAC,CAAC;UACjBP,GAAG,GAAGM,gBAAgB;QACxB;MACF;IACF;EACF;EAEA,IAAIP,KAAK,KAAKC,GAAG,EAAE;IACjBA,GAAG,GAAGM,gBAAgB;EACxB,CAAC,MAAM,IAAIN,GAAG,GAAG,CAAC,EAAE;IAClBA,GAAG,GAAGL,IAAI,CAACO,MAAM;EACnB;EAEA,OAAOP,IAAI,CAACU,KAAK,CAACN,KAAK,EAAEC,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAST,OAAOA,CAACI,IAAI,EAAE;EACrBG,UAAU,CAACH,IAAI,CAAC;EAEhB,IAAIA,IAAI,CAACO,MAAM,KAAK,CAAC,EAAE;IACrB,OAAO,GAAG;EACZ;EAEA,IAAIF,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,KAAK,GAAGN,IAAI,CAACO,MAAM;EACvB;EACA,IAAIM,cAAc;;EAElB;EACA,OAAO,EAAEP,KAAK,EAAE;IACd,IAAIN,IAAI,CAACS,WAAW,CAACH,KAAK,CAAC,KAAK,EAAE,CAAC,WAAW;MAC5C,IAAIO,cAAc,EAAE;QAClBR,GAAG,GAAGC,KAAK;QACX;MACF;IACF,CAAC,MAAM,IAAI,CAACO,cAAc,EAAE;MAC1B;MACAA,cAAc,GAAG,IAAI;IACvB;EACF;EAEA,OAAOR,GAAG,GAAG,CAAC,GACVL,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,YACzB,GAAG,GACH,GAAG,GACLJ,GAAG,KAAK,CAAC,IAAIL,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,YACtC,IAAI,GACJT,IAAI,CAACU,KAAK,CAAC,CAAC,EAAEL,GAAG,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASR,OAAOA,CAACG,IAAI,EAAE;EACrBG,UAAU,CAACH,IAAI,CAAC;EAEhB,IAAIM,KAAK,GAAGN,IAAI,CAACO,MAAM;EAEvB,IAAIF,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIS,SAAS,GAAG,CAAC;EACjB,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB;EACA;EACA,IAAIC,WAAW,GAAG,CAAC;EACnB;EACA,IAAIH,cAAc;EAElB,OAAOP,KAAK,EAAE,EAAE;IACd,MAAMW,IAAI,GAAGjB,IAAI,CAACS,WAAW,CAACH,KAAK,CAAC;IAEpC,IAAIW,IAAI,KAAK,EAAE,CAAC,WAAW;MACzB;MACA;MACA,IAAIJ,cAAc,EAAE;QAClBC,SAAS,GAAGR,KAAK,GAAG,CAAC;QACrB;MACF;MAEA;IACF;IAEA,IAAID,GAAG,GAAG,CAAC,EAAE;MACX;MACA;MACAQ,cAAc,GAAG,IAAI;MACrBR,GAAG,GAAGC,KAAK,GAAG,CAAC;IACjB;IAEA,IAAIW,IAAI,KAAK,EAAE,CAAC,WAAW;MACzB;MACA,IAAIF,QAAQ,GAAG,CAAC,EAAE;QAChBA,QAAQ,GAAGT,KAAK;MAClB,CAAC,MAAM,IAAIU,WAAW,KAAK,CAAC,EAAE;QAC5BA,WAAW,GAAG,CAAC;MACjB;IACF,CAAC,MAAM,IAAID,QAAQ,GAAG,CAAC,CAAC,EAAE;MACxB;MACA;MACAC,WAAW,GAAG,CAAC,CAAC;IAClB;EACF;EAEA,IACED,QAAQ,GAAG,CAAC,IACZV,GAAG,GAAG,CAAC;EACP;EACAW,WAAW,KAAK,CAAC;EACjB;EACCA,WAAW,KAAK,CAAC,IAAID,QAAQ,KAAKV,GAAG,GAAG,CAAC,IAAIU,QAAQ,KAAKD,SAAS,GAAG,CAAE,EACzE;IACA,OAAO,EAAE;EACX;EAEA,OAAOd,IAAI,CAACU,KAAK,CAACK,QAAQ,EAAEV,GAAG,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,IAAIA,CAAC,GAAGoB,QAAQ,EAAE;EACzB,IAAIZ,KAAK,GAAG,CAAC,CAAC;EACd;EACA,IAAIa,MAAM;EAEV,OAAO,EAAEb,KAAK,GAAGY,QAAQ,CAACX,MAAM,EAAE;IAChCJ,UAAU,CAACe,QAAQ,CAACZ,KAAK,CAAC,CAAC;IAE3B,IAAIY,QAAQ,CAACZ,KAAK,CAAC,EAAE;MACnBa,MAAM,GACJA,MAAM,KAAKlB,SAAS,GAAGiB,QAAQ,CAACZ,KAAK,CAAC,GAAGa,MAAM,GAAG,GAAG,GAAGD,QAAQ,CAACZ,KAAK,CAAC;IAC3E;EACF;EAEA,OAAOa,MAAM,KAAKlB,SAAS,GAAG,GAAG,GAAGmB,SAAS,CAACD,MAAM,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACpB,IAAI,EAAE;EACvBG,UAAU,CAACH,IAAI,CAAC;EAEhB,MAAMqB,QAAQ,GAAGrB,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,EAAC;;EAE5C;EACA,IAAIa,KAAK,GAAGC,eAAe,CAACvB,IAAI,EAAE,CAACqB,QAAQ,CAAC;EAE5C,IAAIC,KAAK,CAACf,MAAM,KAAK,CAAC,IAAI,CAACc,QAAQ,EAAE;IACnCC,KAAK,GAAG,GAAG;EACb;EAEA,IAAIA,KAAK,CAACf,MAAM,GAAG,CAAC,IAAIP,IAAI,CAACS,WAAW,CAACT,IAAI,CAACO,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS;IACxEe,KAAK,IAAI,GAAG;EACd;EAEA,OAAOD,QAAQ,GAAG,GAAG,GAAGC,KAAK,GAAGA,KAAK;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACvB,IAAI,EAAEwB,cAAc,EAAE;EAC7C,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,iBAAiB,GAAG,CAAC;EACzB,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAItB,KAAK,GAAG,CAAC,CAAC;EACd;EACA,IAAIW,IAAI;EACR;EACA,IAAIY,cAAc;EAElB,OAAO,EAAEvB,KAAK,IAAIN,IAAI,CAACO,MAAM,EAAE;IAC7B,IAAID,KAAK,GAAGN,IAAI,CAACO,MAAM,EAAE;MACvBU,IAAI,GAAGjB,IAAI,CAACS,WAAW,CAACH,KAAK,CAAC;IAChC,CAAC,MAAM,IAAIW,IAAI,KAAK,EAAE,CAAC,WAAW;MAChC;IACF,CAAC,MAAM;MACLA,IAAI,GAAG,EAAE,EAAC;IACZ;IAEA,IAAIA,IAAI,KAAK,EAAE,CAAC,WAAW;MACzB,IAAIU,SAAS,KAAKrB,KAAK,GAAG,CAAC,IAAIsB,IAAI,KAAK,CAAC,EAAE;QACzC;MAAA,CACD,MAAM,IAAID,SAAS,KAAKrB,KAAK,GAAG,CAAC,IAAIsB,IAAI,KAAK,CAAC,EAAE;QAChD,IACEH,MAAM,CAAClB,MAAM,GAAG,CAAC,IACjBmB,iBAAiB,KAAK,CAAC,IACvBD,MAAM,CAAChB,WAAW,CAACgB,MAAM,CAAClB,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,aAC7CkB,MAAM,CAAChB,WAAW,CAACgB,MAAM,CAAClB,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,WAC7C;UACA,IAAIkB,MAAM,CAAClB,MAAM,GAAG,CAAC,EAAE;YACrBsB,cAAc,GAAGJ,MAAM,CAACK,WAAW,CAAC,GAAG,CAAC;YAExC,IAAID,cAAc,KAAKJ,MAAM,CAAClB,MAAM,GAAG,CAAC,EAAE;cACxC,IAAIsB,cAAc,GAAG,CAAC,EAAE;gBACtBJ,MAAM,GAAG,EAAE;gBACXC,iBAAiB,GAAG,CAAC;cACvB,CAAC,MAAM;gBACLD,MAAM,GAAGA,MAAM,CAACf,KAAK,CAAC,CAAC,EAAEmB,cAAc,CAAC;gBACxCH,iBAAiB,GAAGD,MAAM,CAAClB,MAAM,GAAG,CAAC,GAAGkB,MAAM,CAACK,WAAW,CAAC,GAAG,CAAC;cACjE;cAEAH,SAAS,GAAGrB,KAAK;cACjBsB,IAAI,GAAG,CAAC;cACR;YACF;UACF,CAAC,MAAM,IAAIH,MAAM,CAAClB,MAAM,GAAG,CAAC,EAAE;YAC5BkB,MAAM,GAAG,EAAE;YACXC,iBAAiB,GAAG,CAAC;YACrBC,SAAS,GAAGrB,KAAK;YACjBsB,IAAI,GAAG,CAAC;YACR;UACF;QACF;QAEA,IAAIJ,cAAc,EAAE;UAClBC,MAAM,GAAGA,MAAM,CAAClB,MAAM,GAAG,CAAC,GAAGkB,MAAM,GAAG,KAAK,GAAG,IAAI;UAClDC,iBAAiB,GAAG,CAAC;QACvB;MACF,CAAC,MAAM;QACL,IAAID,MAAM,CAAClB,MAAM,GAAG,CAAC,EAAE;UACrBkB,MAAM,IAAI,GAAG,GAAGzB,IAAI,CAACU,KAAK,CAACiB,SAAS,GAAG,CAAC,EAAErB,KAAK,CAAC;QAClD,CAAC,MAAM;UACLmB,MAAM,GAAGzB,IAAI,CAACU,KAAK,CAACiB,SAAS,GAAG,CAAC,EAAErB,KAAK,CAAC;QAC3C;QAEAoB,iBAAiB,GAAGpB,KAAK,GAAGqB,SAAS,GAAG,CAAC;MAC3C;MAEAA,SAAS,GAAGrB,KAAK;MACjBsB,IAAI,GAAG,CAAC;IACV,CAAC,MAAM,IAAIX,IAAI,KAAK,EAAE,CAAC,aAAaW,IAAI,GAAG,CAAC,CAAC,EAAE;MAC7CA,IAAI,EAAE;IACR,CAAC,MAAM;MACLA,IAAI,GAAG,CAAC,CAAC;IACX;EACF;EAEA,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStB,UAAUA,CAACH,IAAI,EAAE;EACxB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIE,SAAS,CACjB,kCAAkC,GAAG6B,IAAI,CAACC,SAAS,CAAChC,IAAI,CAC1D,CAAC;EACH;AACF;;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}