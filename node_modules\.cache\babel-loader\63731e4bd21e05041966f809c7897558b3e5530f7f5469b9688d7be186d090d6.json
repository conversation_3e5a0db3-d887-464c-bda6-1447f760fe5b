{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { subtokenize } from 'micromark-util-subtokenize';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/**\n * No name because it must not be turned off.\n * @type {Construct}\n */\nexport const content = {\n  resolve: resolveContent,\n  tokenize: tokenizeContent\n};\n\n/** @type {Construct} */\nconst continuationConstruct = {\n  partial: true,\n  tokenize: tokenizeContinuation\n};\n\n/**\n * Content is transparent: it’s parsed right now. That way, definitions are also\n * parsed right now: before text in paragraphs (specifically, media) are parsed.\n *\n * @type {Resolver}\n */\nfunction resolveContent(events) {\n  subtokenize(events);\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContent(effects, ok) {\n  /** @type {Token | undefined} */\n  let previous;\n  return chunkStart;\n\n  /**\n   * Before a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkStart(code) {\n    assert(code !== codes.eof && !markdownLineEnding(code), 'expected no eof or eol');\n    effects.enter(types.content);\n    previous = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent\n    });\n    return chunkInside(code);\n  }\n\n  /**\n   * In a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkInside(code) {\n    if (code === codes.eof) {\n      return contentEnd(code);\n    }\n\n    // To do: in `markdown-rs`, each line is parsed on its own, and everything\n    // is stitched together resolving.\n    if (markdownLineEnding(code)) {\n      return effects.check(continuationConstruct, contentContinue, contentEnd)(code);\n    }\n\n    // Data.\n    effects.consume(code);\n    return chunkInside;\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentEnd(code) {\n    effects.exit(types.chunkContent);\n    effects.exit(types.content);\n    return ok(code);\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentContinue(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.consume(code);\n    effects.exit(types.chunkContent);\n    assert(previous, 'expected previous token');\n    previous.next = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent,\n      previous\n    });\n    previous = previous.next;\n    return chunkInside;\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContinuation(effects, ok, nok) {\n  const self = this;\n  return startLookahead;\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function startLookahead(code) {\n    assert(markdownLineEnding(code), 'expected a line ending');\n    effects.exit(types.chunkContent);\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return factorySpace(effects, prefixed, types.linePrefix);\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function prefixed(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code);\n    }\n\n    // Always populated by defaults.\n    assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n    const tail = self.events[self.events.length - 1];\n    if (!self.parser.constructs.disable.null.includes('codeIndented') && tail && tail[1].type === types.linePrefix && tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize) {\n      return ok(code);\n    }\n    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factorySpace", "markdownLineEnding", "subtokenize", "codes", "constants", "types", "content", "resolve", "resolveContent", "tokenize", "tokenizeContent", "continuationConstruct", "partial", "tokenizeContinuation", "events", "effects", "previous", "chunkStart", "code", "eof", "enter", "chunkContent", "contentType", "contentTypeContent", "chunkInside", "contentEnd", "check", "contentContinue", "consume", "exit", "next", "nok", "self", "startLookahead", "lineEnding", "prefixed", "linePrefix", "parser", "constructs", "disable", "null", "tail", "length", "includes", "type", "sliceSerialize", "tabSize", "interrupt", "flow"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark-core-commonmark/dev/lib/content.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {subtokenize} from 'micromark-util-subtokenize'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * No name because it must not be turned off.\n * @type {Construct}\n */\nexport const content = {resolve: resolveContent, tokenize: tokenizeContent}\n\n/** @type {Construct} */\nconst continuationConstruct = {partial: true, tokenize: tokenizeContinuation}\n\n/**\n * Content is transparent: it’s parsed right now. That way, definitions are also\n * parsed right now: before text in paragraphs (specifically, media) are parsed.\n *\n * @type {Resolver}\n */\nfunction resolveContent(events) {\n  subtokenize(events)\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContent(effects, ok) {\n  /** @type {Token | undefined} */\n  let previous\n\n  return chunkStart\n\n  /**\n   * Before a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkStart(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected no eof or eol'\n    )\n\n    effects.enter(types.content)\n    previous = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent\n    })\n    return chunkInside(code)\n  }\n\n  /**\n   * In a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkInside(code) {\n    if (code === codes.eof) {\n      return contentEnd(code)\n    }\n\n    // To do: in `markdown-rs`, each line is parsed on its own, and everything\n    // is stitched together resolving.\n    if (markdownLineEnding(code)) {\n      return effects.check(\n        continuationConstruct,\n        contentContinue,\n        contentEnd\n      )(code)\n    }\n\n    // Data.\n    effects.consume(code)\n    return chunkInside\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentEnd(code) {\n    effects.exit(types.chunkContent)\n    effects.exit(types.content)\n    return ok(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentContinue(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.consume(code)\n    effects.exit(types.chunkContent)\n    assert(previous, 'expected previous token')\n    previous.next = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent,\n      previous\n    })\n    previous = previous.next\n    return chunkInside\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContinuation(effects, ok, nok) {\n  const self = this\n\n  return startLookahead\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function startLookahead(code) {\n    assert(markdownLineEnding(code), 'expected a line ending')\n    effects.exit(types.chunkContent)\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, prefixed, types.linePrefix)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function prefixed(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n\n    const tail = self.events[self.events.length - 1]\n\n    if (\n      !self.parser.constructs.disable.null.includes('codeIndented') &&\n      tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n    ) {\n      return ok(code)\n    }\n\n    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,WAAW,QAAO,4BAA4B;AACtD,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAG;EAACC,OAAO,EAAEC,cAAc;EAAEC,QAAQ,EAAEC;AAAe,CAAC;;AAE3E;AACA,MAAMC,qBAAqB,GAAG;EAACC,OAAO,EAAE,IAAI;EAAEH,QAAQ,EAAEI;AAAoB,CAAC;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,cAAcA,CAACM,MAAM,EAAE;EAC9BZ,WAAW,CAACY,MAAM,CAAC;EACnB,OAAOA,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASJ,eAAeA,CAACK,OAAO,EAAEjB,EAAE,EAAE;EACpC;EACA,IAAIkB,QAAQ;EAEZ,OAAOC,UAAU;;EAEjB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,UAAUA,CAACC,IAAI,EAAE;IACxBnB,MAAM,CACJmB,IAAI,KAAKf,KAAK,CAACgB,GAAG,IAAI,CAAClB,kBAAkB,CAACiB,IAAI,CAAC,EAC/C,wBACF,CAAC;IAEDH,OAAO,CAACK,KAAK,CAACf,KAAK,CAACC,OAAO,CAAC;IAC5BU,QAAQ,GAAGD,OAAO,CAACK,KAAK,CAACf,KAAK,CAACgB,YAAY,EAAE;MAC3CC,WAAW,EAAElB,SAAS,CAACmB;IACzB,CAAC,CAAC;IACF,OAAOC,WAAW,CAACN,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASM,WAAWA,CAACN,IAAI,EAAE;IACzB,IAAIA,IAAI,KAAKf,KAAK,CAACgB,GAAG,EAAE;MACtB,OAAOM,UAAU,CAACP,IAAI,CAAC;IACzB;;IAEA;IACA;IACA,IAAIjB,kBAAkB,CAACiB,IAAI,CAAC,EAAE;MAC5B,OAAOH,OAAO,CAACW,KAAK,CAClBf,qBAAqB,EACrBgB,eAAe,EACfF,UACF,CAAC,CAACP,IAAI,CAAC;IACT;;IAEA;IACAH,OAAO,CAACa,OAAO,CAACV,IAAI,CAAC;IACrB,OAAOM,WAAW;EACpB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASC,UAAUA,CAACP,IAAI,EAAE;IACxBH,OAAO,CAACc,IAAI,CAACxB,KAAK,CAACgB,YAAY,CAAC;IAChCN,OAAO,CAACc,IAAI,CAACxB,KAAK,CAACC,OAAO,CAAC;IAC3B,OAAOR,EAAE,CAACoB,IAAI,CAAC;EACjB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASS,eAAeA,CAACT,IAAI,EAAE;IAC7BnB,MAAM,CAACE,kBAAkB,CAACiB,IAAI,CAAC,EAAE,cAAc,CAAC;IAChDH,OAAO,CAACa,OAAO,CAACV,IAAI,CAAC;IACrBH,OAAO,CAACc,IAAI,CAACxB,KAAK,CAACgB,YAAY,CAAC;IAChCtB,MAAM,CAACiB,QAAQ,EAAE,yBAAyB,CAAC;IAC3CA,QAAQ,CAACc,IAAI,GAAGf,OAAO,CAACK,KAAK,CAACf,KAAK,CAACgB,YAAY,EAAE;MAChDC,WAAW,EAAElB,SAAS,CAACmB,kBAAkB;MACzCP;IACF,CAAC,CAAC;IACFA,QAAQ,GAAGA,QAAQ,CAACc,IAAI;IACxB,OAAON,WAAW;EACpB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASX,oBAAoBA,CAACE,OAAO,EAAEjB,EAAE,EAAEiC,GAAG,EAAE;EAC9C,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOC,cAAc;;EAErB;AACF;AACA;AACA;AACA;EACE,SAASA,cAAcA,CAACf,IAAI,EAAE;IAC5BnB,MAAM,CAACE,kBAAkB,CAACiB,IAAI,CAAC,EAAE,wBAAwB,CAAC;IAC1DH,OAAO,CAACc,IAAI,CAACxB,KAAK,CAACgB,YAAY,CAAC;IAChCN,OAAO,CAACK,KAAK,CAACf,KAAK,CAAC6B,UAAU,CAAC;IAC/BnB,OAAO,CAACa,OAAO,CAACV,IAAI,CAAC;IACrBH,OAAO,CAACc,IAAI,CAACxB,KAAK,CAAC6B,UAAU,CAAC;IAC9B,OAAOlC,YAAY,CAACe,OAAO,EAAEoB,QAAQ,EAAE9B,KAAK,CAAC+B,UAAU,CAAC;EAC1D;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASD,QAAQA,CAACjB,IAAI,EAAE;IACtB,IAAIA,IAAI,KAAKf,KAAK,CAACgB,GAAG,IAAIlB,kBAAkB,CAACiB,IAAI,CAAC,EAAE;MAClD,OAAOa,GAAG,CAACb,IAAI,CAAC;IAClB;;IAEA;IACAnB,MAAM,CACJiC,IAAI,CAACK,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCACF,CAAC;IAED,MAAMC,IAAI,GAAGT,IAAI,CAAClB,MAAM,CAACkB,IAAI,CAAClB,MAAM,CAAC4B,MAAM,GAAG,CAAC,CAAC;IAEhD,IACE,CAACV,IAAI,CAACK,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,CAACG,QAAQ,CAAC,cAAc,CAAC,IAC7DF,IAAI,IACJA,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKvC,KAAK,CAAC+B,UAAU,IACjCK,IAAI,CAAC,CAAC,CAAC,CAACI,cAAc,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACC,MAAM,IAAItC,SAAS,CAAC0C,OAAO,EACjE;MACA,OAAOhD,EAAE,CAACoB,IAAI,CAAC;IACjB;IAEA,OAAOH,OAAO,CAACgC,SAAS,CAACf,IAAI,CAACK,MAAM,CAACC,UAAU,CAACU,IAAI,EAAEjB,GAAG,EAAEjC,EAAE,CAAC,CAACoB,IAAI,CAAC;EACtE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}