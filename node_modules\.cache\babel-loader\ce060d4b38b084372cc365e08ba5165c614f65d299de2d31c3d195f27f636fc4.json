{"ast": null, "code": "export default function isPlainObject(value) {\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(value);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);\n}", "map": {"version": 3, "names": ["isPlainObject", "value", "prototype", "Object", "getPrototypeOf", "Symbol", "toStringTag", "iterator"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/is-plain-obj/index.js"], "sourcesContent": ["export default function isPlainObject(value) {\n\tif (typeof value !== 'object' || value === null) {\n\t\treturn false;\n\t}\n\n\tconst prototype = Object.getPrototypeOf(value);\n\treturn (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);\n}\n"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,KAAK,EAAE;EAC5C,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;IAChD,OAAO,KAAK;EACb;EAEA,MAAMC,SAAS,GAAGC,MAAM,CAACC,cAAc,CAACH,KAAK,CAAC;EAC9C,OAAO,CAACC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKC,MAAM,CAACD,SAAS,IAAIC,MAAM,CAACC,cAAc,CAACF,SAAS,CAAC,KAAK,IAAI,KAAK,EAAEG,MAAM,CAACC,WAAW,IAAIL,KAAK,CAAC,IAAI,EAAEI,MAAM,CAACE,QAAQ,IAAIN,KAAK,CAAC;AAC5K", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}