{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\n\n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function image(state, node) {\n  /** @type {Properties} */\n  const properties = {\n    src: normalizeUri(node.url)\n  };\n  if (node.alt !== null && node.alt !== undefined) {\n    properties.alt = node.alt;\n  }\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title;\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'img',\n    properties,\n    children: []\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["normalizeUri", "image", "state", "node", "properties", "src", "url", "alt", "undefined", "title", "result", "type", "tagName", "children", "patch", "applyData"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/mdast-util-to-hast/lib/handlers/image.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function image(state, node) {\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(node.url)}\n\n  if (node.alt !== null && node.alt !== undefined) {\n    properties.alt = node.alt\n  }\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,6BAA6B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACjC;EACA,MAAMC,UAAU,GAAG;IAACC,GAAG,EAAEL,YAAY,CAACG,IAAI,CAACG,GAAG;EAAC,CAAC;EAEhD,IAAIH,IAAI,CAACI,GAAG,KAAK,IAAI,IAAIJ,IAAI,CAACI,GAAG,KAAKC,SAAS,EAAE;IAC/CJ,UAAU,CAACG,GAAG,GAAGJ,IAAI,CAACI,GAAG;EAC3B;EAEA,IAAIJ,IAAI,CAACM,KAAK,KAAK,IAAI,IAAIN,IAAI,CAACM,KAAK,KAAKD,SAAS,EAAE;IACnDJ,UAAU,CAACK,KAAK,GAAGN,IAAI,CAACM,KAAK;EAC/B;;EAEA;EACA,MAAMC,MAAM,GAAG;IAACC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,KAAK;IAAER,UAAU;IAAES,QAAQ,EAAE;EAAE,CAAC;EAC1EX,KAAK,CAACY,KAAK,CAACX,IAAI,EAAEO,MAAM,CAAC;EACzB,OAAOR,KAAK,CAACa,SAAS,CAACZ,IAAI,EAAEO,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}