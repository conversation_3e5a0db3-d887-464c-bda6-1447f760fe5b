{"ast": null, "code": "/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */\n\n/**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */\n\nimport structuredClone from '@ungap/structured-clone';\nimport { visit } from 'unist-util-visit';\nimport { position } from 'unist-util-position';\nimport { handlers as defaultHandlers } from './handlers/index.js';\nconst own = {}.hasOwnProperty;\n\n/** @type {Options} */\nconst emptyOptions = {};\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */\nexport function createState(tree, options) {\n  const settings = options || emptyOptions;\n  /** @type {Map<string, MdastDefinition>} */\n  const definitionById = new Map();\n  /** @type {Map<string, MdastFootnoteDefinition>} */\n  const footnoteById = new Map();\n  /** @type {Map<string, number>} */\n  const footnoteCounts = new Map();\n  /** @type {Handlers} */\n  // @ts-expect-error: the root handler returns a root.\n  // Hard to type.\n  const handlers = {\n    ...defaultHandlers,\n    ...settings.handlers\n  };\n\n  /** @type {State} */\n  const state = {\n    all,\n    applyData,\n    definitionById,\n    footnoteById,\n    footnoteCounts,\n    footnoteOrder: [],\n    handlers,\n    one,\n    options: settings,\n    patch,\n    wrap\n  };\n  visit(tree, function (node) {\n    if (node.type === 'definition' || node.type === 'footnoteDefinition') {\n      const map = node.type === 'definition' ? definitionById : footnoteById;\n      const id = String(node.identifier).toUpperCase();\n\n      // Mimick CM behavior of link definitions.\n      // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n      if (!map.has(id)) {\n        // @ts-expect-error: node type matches map.\n        map.set(id, node);\n      }\n    }\n  });\n  return state;\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */\n  function one(node, parent) {\n    const type = node.type;\n    const handle = state.handlers[type];\n    if (own.call(state.handlers, type) && handle) {\n      return handle(state, node, parent);\n    }\n    if (state.options.passThrough && state.options.passThrough.includes(type)) {\n      if ('children' in node) {\n        const {\n          children,\n          ...shallow\n        } = node;\n        const result = structuredClone(shallow);\n        // @ts-expect-error: TS doesn’t understand…\n        result.children = state.all(node);\n        // @ts-expect-error: TS doesn’t understand…\n        return result;\n      }\n\n      // @ts-expect-error: it’s custom.\n      return structuredClone(node);\n    }\n    const unknown = state.options.unknownHandler || defaultUnknownHandler;\n    return unknown(state, node, parent);\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function all(parent) {\n    /** @type {Array<HastElementContent>} */\n    const values = [];\n    if ('children' in parent) {\n      const nodes = parent.children;\n      let index = -1;\n      while (++index < nodes.length) {\n        const result = state.one(nodes[index], parent);\n\n        // To do: see if we van clean this? Can we merge texts?\n        if (result) {\n          if (index && nodes[index - 1].type === 'break') {\n            if (!Array.isArray(result) && result.type === 'text') {\n              result.value = trimMarkdownSpaceStart(result.value);\n            }\n            if (!Array.isArray(result) && result.type === 'element') {\n              const head = result.children[0];\n              if (head && head.type === 'text') {\n                head.value = trimMarkdownSpaceStart(head.value);\n              }\n            }\n          }\n          if (Array.isArray(result)) {\n            values.push(...result);\n          } else {\n            values.push(result);\n          }\n        }\n      }\n    }\n    return values;\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = position(from);\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {HastElement | Type} */\n  let result = to;\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName;\n    const hChildren = from.data.hChildren;\n    const hProperties = from.data.hProperties;\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName;\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent of `hName` is to create an element, but likely also to keep\n      // the content around (otherwise: pass `hChildren`).\n      else {\n        /** @type {Array<HastElementContent>} */\n        // @ts-expect-error: assume no doctypes in `root`.\n        const children = 'children' in result ? result.children : [result];\n        result = {\n          type: 'element',\n          tagName: hName,\n          properties: {},\n          children\n        };\n      }\n    }\n    if (result.type === 'element' && hProperties) {\n      Object.assign(result.properties, structuredClone(hProperties));\n    }\n    if ('children' in result && result.children && hChildren !== null && hChildren !== undefined) {\n      result.children = hChildren;\n    }\n  }\n  return result;\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {};\n  /** @type {HastElement | HastText} */\n  const result = 'value' in node && !(own.call(data, 'hProperties') || own.call(data, 'hChildren')) ? {\n    type: 'text',\n    value: node.value\n  } : {\n    type: 'element',\n    tagName: 'div',\n    properties: {},\n    children: state.all(node)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */\nexport function wrap(nodes, loose) {\n  /** @type {Array<HastText | Type>} */\n  const result = [];\n  let index = -1;\n  if (loose) {\n    result.push({\n      type: 'text',\n      value: '\\n'\n    });\n  }\n  while (++index < nodes.length) {\n    if (index) result.push({\n      type: 'text',\n      value: '\\n'\n    });\n    result.push(nodes[index]);\n  }\n  if (loose && nodes.length > 0) {\n    result.push({\n      type: 'text',\n      value: '\\n'\n    });\n  }\n  return result;\n}\n\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */\nfunction trimMarkdownSpaceStart(value) {\n  let index = 0;\n  let code = value.charCodeAt(index);\n  while (code === 9 || code === 32) {\n    index++;\n    code = value.charCodeAt(index);\n  }\n  return value.slice(index);\n}", "map": {"version": 3, "names": ["structuredClone", "visit", "position", "handlers", "defaultHandlers", "own", "hasOwnProperty", "emptyOptions", "createState", "tree", "options", "settings", "definitionById", "Map", "footnoteById", "footnoteCounts", "state", "all", "applyData", "footnoteOrder", "one", "patch", "wrap", "node", "type", "map", "id", "String", "identifier", "toUpperCase", "has", "set", "parent", "handle", "call", "passThrough", "includes", "children", "shallow", "result", "unknown", "<PERSON><PERSON><PERSON><PERSON>", "defaultUnknownHandler", "values", "nodes", "index", "length", "Array", "isArray", "value", "trimMarkdownSpaceStart", "head", "push", "from", "to", "data", "hName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hProperties", "tagName", "properties", "Object", "assign", "undefined", "loose", "code", "charCodeAt", "slice"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/mdast-util-to-hast/lib/state.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */\n\n/**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {visit} from 'unist-util-visit'\nimport {position} from 'unist-util-position'\nimport {handlers as defaultHandlers} from './handlers/index.js'\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */\nexport function createState(tree, options) {\n  const settings = options || emptyOptions\n  /** @type {Map<string, MdastDefinition>} */\n  const definitionById = new Map()\n  /** @type {Map<string, MdastFootnoteDefinition>} */\n  const footnoteById = new Map()\n  /** @type {Map<string, number>} */\n  const footnoteCounts = new Map()\n  /** @type {Handlers} */\n  // @ts-expect-error: the root handler returns a root.\n  // Hard to type.\n  const handlers = {...defaultHandlers, ...settings.handlers}\n\n  /** @type {State} */\n  const state = {\n    all,\n    applyData,\n    definitionById,\n    footnoteById,\n    footnoteCounts,\n    footnoteOrder: [],\n    handlers,\n    one,\n    options: settings,\n    patch,\n    wrap\n  }\n\n  visit(tree, function (node) {\n    if (node.type === 'definition' || node.type === 'footnoteDefinition') {\n      const map = node.type === 'definition' ? definitionById : footnoteById\n      const id = String(node.identifier).toUpperCase()\n\n      // Mimick CM behavior of link definitions.\n      // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n      if (!map.has(id)) {\n        // @ts-expect-error: node type matches map.\n        map.set(id, node)\n      }\n    }\n  })\n\n  return state\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */\n  function one(node, parent) {\n    const type = node.type\n    const handle = state.handlers[type]\n\n    if (own.call(state.handlers, type) && handle) {\n      return handle(state, node, parent)\n    }\n\n    if (state.options.passThrough && state.options.passThrough.includes(type)) {\n      if ('children' in node) {\n        const {children, ...shallow} = node\n        const result = structuredClone(shallow)\n        // @ts-expect-error: TS doesn’t understand…\n        result.children = state.all(node)\n        // @ts-expect-error: TS doesn’t understand…\n        return result\n      }\n\n      // @ts-expect-error: it’s custom.\n      return structuredClone(node)\n    }\n\n    const unknown = state.options.unknownHandler || defaultUnknownHandler\n\n    return unknown(state, node, parent)\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function all(parent) {\n    /** @type {Array<HastElementContent>} */\n    const values = []\n\n    if ('children' in parent) {\n      const nodes = parent.children\n      let index = -1\n      while (++index < nodes.length) {\n        const result = state.one(nodes[index], parent)\n\n        // To do: see if we van clean this? Can we merge texts?\n        if (result) {\n          if (index && nodes[index - 1].type === 'break') {\n            if (!Array.isArray(result) && result.type === 'text') {\n              result.value = trimMarkdownSpaceStart(result.value)\n            }\n\n            if (!Array.isArray(result) && result.type === 'element') {\n              const head = result.children[0]\n\n              if (head && head.type === 'text') {\n                head.value = trimMarkdownSpaceStart(head.value)\n              }\n            }\n          }\n\n          if (Array.isArray(result)) {\n            values.push(...result)\n          } else {\n            values.push(result)\n          }\n        }\n      }\n    }\n\n    return values\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = position(from)\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {HastElement | Type} */\n  let result = to\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName\n    const hChildren = from.data.hChildren\n    const hProperties = from.data.hProperties\n\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent of `hName` is to create an element, but likely also to keep\n      // the content around (otherwise: pass `hChildren`).\n      else {\n        /** @type {Array<HastElementContent>} */\n        // @ts-expect-error: assume no doctypes in `root`.\n        const children = 'children' in result ? result.children : [result]\n        result = {type: 'element', tagName: hName, properties: {}, children}\n      }\n    }\n\n    if (result.type === 'element' && hProperties) {\n      Object.assign(result.properties, structuredClone(hProperties))\n    }\n\n    if (\n      'children' in result &&\n      result.children &&\n      hChildren !== null &&\n      hChildren !== undefined\n    ) {\n      result.children = hChildren\n    }\n  }\n\n  return result\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {}\n  /** @type {HastElement | HastText} */\n  const result =\n    'value' in node &&\n    !(own.call(data, 'hProperties') || own.call(data, 'hChildren'))\n      ? {type: 'text', value: node.value}\n      : {\n          type: 'element',\n          tagName: 'div',\n          properties: {},\n          children: state.all(node)\n        }\n\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */\nexport function wrap(nodes, loose) {\n  /** @type {Array<HastText | Type>} */\n  const result = []\n  let index = -1\n\n  if (loose) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  while (++index < nodes.length) {\n    if (index) result.push({type: 'text', value: '\\n'})\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  return result\n}\n\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */\nfunction trimMarkdownSpaceStart(value) {\n  let index = 0\n  let code = value.charCodeAt(index)\n\n  while (code === 9 || code === 32) {\n    index++\n    code = value.charCodeAt(index)\n  }\n\n  return value.slice(index)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,eAAe,MAAM,yBAAyB;AACrD,SAAQC,KAAK,QAAO,kBAAkB;AACtC,SAAQC,QAAQ,QAAO,qBAAqB;AAC5C,SAAQC,QAAQ,IAAIC,eAAe,QAAO,qBAAqB;AAE/D,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA,MAAMC,YAAY,GAAG,CAAC,CAAC;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,MAAMC,QAAQ,GAAGD,OAAO,IAAIH,YAAY;EACxC;EACA,MAAMK,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAChC;EACA,MAAMC,YAAY,GAAG,IAAID,GAAG,CAAC,CAAC;EAC9B;EACA,MAAME,cAAc,GAAG,IAAIF,GAAG,CAAC,CAAC;EAChC;EACA;EACA;EACA,MAAMV,QAAQ,GAAG;IAAC,GAAGC,eAAe;IAAE,GAAGO,QAAQ,CAACR;EAAQ,CAAC;;EAE3D;EACA,MAAMa,KAAK,GAAG;IACZC,GAAG;IACHC,SAAS;IACTN,cAAc;IACdE,YAAY;IACZC,cAAc;IACdI,aAAa,EAAE,EAAE;IACjBhB,QAAQ;IACRiB,GAAG;IACHV,OAAO,EAAEC,QAAQ;IACjBU,KAAK;IACLC;EACF,CAAC;EAEDrB,KAAK,CAACQ,IAAI,EAAE,UAAUc,IAAI,EAAE;IAC1B,IAAIA,IAAI,CAACC,IAAI,KAAK,YAAY,IAAID,IAAI,CAACC,IAAI,KAAK,oBAAoB,EAAE;MACpE,MAAMC,GAAG,GAAGF,IAAI,CAACC,IAAI,KAAK,YAAY,GAAGZ,cAAc,GAAGE,YAAY;MACtE,MAAMY,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,UAAU,CAAC,CAACC,WAAW,CAAC,CAAC;;MAEhD;MACA;MACA,IAAI,CAACJ,GAAG,CAACK,GAAG,CAACJ,EAAE,CAAC,EAAE;QAChB;QACAD,GAAG,CAACM,GAAG,CAACL,EAAE,EAAEH,IAAI,CAAC;MACnB;IACF;EACF,CAAC,CAAC;EAEF,OAAOP,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASI,GAAGA,CAACG,IAAI,EAAES,MAAM,EAAE;IACzB,MAAMR,IAAI,GAAGD,IAAI,CAACC,IAAI;IACtB,MAAMS,MAAM,GAAGjB,KAAK,CAACb,QAAQ,CAACqB,IAAI,CAAC;IAEnC,IAAInB,GAAG,CAAC6B,IAAI,CAAClB,KAAK,CAACb,QAAQ,EAAEqB,IAAI,CAAC,IAAIS,MAAM,EAAE;MAC5C,OAAOA,MAAM,CAACjB,KAAK,EAAEO,IAAI,EAAES,MAAM,CAAC;IACpC;IAEA,IAAIhB,KAAK,CAACN,OAAO,CAACyB,WAAW,IAAInB,KAAK,CAACN,OAAO,CAACyB,WAAW,CAACC,QAAQ,CAACZ,IAAI,CAAC,EAAE;MACzE,IAAI,UAAU,IAAID,IAAI,EAAE;QACtB,MAAM;UAACc,QAAQ;UAAE,GAAGC;QAAO,CAAC,GAAGf,IAAI;QACnC,MAAMgB,MAAM,GAAGvC,eAAe,CAACsC,OAAO,CAAC;QACvC;QACAC,MAAM,CAACF,QAAQ,GAAGrB,KAAK,CAACC,GAAG,CAACM,IAAI,CAAC;QACjC;QACA,OAAOgB,MAAM;MACf;;MAEA;MACA,OAAOvC,eAAe,CAACuB,IAAI,CAAC;IAC9B;IAEA,MAAMiB,OAAO,GAAGxB,KAAK,CAACN,OAAO,CAAC+B,cAAc,IAAIC,qBAAqB;IAErE,OAAOF,OAAO,CAACxB,KAAK,EAAEO,IAAI,EAAES,MAAM,CAAC;EACrC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASf,GAAGA,CAACe,MAAM,EAAE;IACnB;IACA,MAAMW,MAAM,GAAG,EAAE;IAEjB,IAAI,UAAU,IAAIX,MAAM,EAAE;MACxB,MAAMY,KAAK,GAAGZ,MAAM,CAACK,QAAQ;MAC7B,IAAIQ,KAAK,GAAG,CAAC,CAAC;MACd,OAAO,EAAEA,KAAK,GAAGD,KAAK,CAACE,MAAM,EAAE;QAC7B,MAAMP,MAAM,GAAGvB,KAAK,CAACI,GAAG,CAACwB,KAAK,CAACC,KAAK,CAAC,EAAEb,MAAM,CAAC;;QAE9C;QACA,IAAIO,MAAM,EAAE;UACV,IAAIM,KAAK,IAAID,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC,CAACrB,IAAI,KAAK,OAAO,EAAE;YAC9C,IAAI,CAACuB,KAAK,CAACC,OAAO,CAACT,MAAM,CAAC,IAAIA,MAAM,CAACf,IAAI,KAAK,MAAM,EAAE;cACpDe,MAAM,CAACU,KAAK,GAAGC,sBAAsB,CAACX,MAAM,CAACU,KAAK,CAAC;YACrD;YAEA,IAAI,CAACF,KAAK,CAACC,OAAO,CAACT,MAAM,CAAC,IAAIA,MAAM,CAACf,IAAI,KAAK,SAAS,EAAE;cACvD,MAAM2B,IAAI,GAAGZ,MAAM,CAACF,QAAQ,CAAC,CAAC,CAAC;cAE/B,IAAIc,IAAI,IAAIA,IAAI,CAAC3B,IAAI,KAAK,MAAM,EAAE;gBAChC2B,IAAI,CAACF,KAAK,GAAGC,sBAAsB,CAACC,IAAI,CAACF,KAAK,CAAC;cACjD;YACF;UACF;UAEA,IAAIF,KAAK,CAACC,OAAO,CAACT,MAAM,CAAC,EAAE;YACzBI,MAAM,CAACS,IAAI,CAAC,GAAGb,MAAM,CAAC;UACxB,CAAC,MAAM;YACLI,MAAM,CAACS,IAAI,CAACb,MAAM,CAAC;UACrB;QACF;MACF;IACF;IAEA,OAAOI,MAAM;EACf;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStB,KAAKA,CAACgC,IAAI,EAAEC,EAAE,EAAE;EACvB,IAAID,IAAI,CAACnD,QAAQ,EAAEoD,EAAE,CAACpD,QAAQ,GAAGA,QAAQ,CAACmD,IAAI,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnC,SAASA,CAACmC,IAAI,EAAEC,EAAE,EAAE;EAC3B;EACA,IAAIf,MAAM,GAAGe,EAAE;;EAEf;EACA,IAAID,IAAI,IAAIA,IAAI,CAACE,IAAI,EAAE;IACrB,MAAMC,KAAK,GAAGH,IAAI,CAACE,IAAI,CAACC,KAAK;IAC7B,MAAMC,SAAS,GAAGJ,IAAI,CAACE,IAAI,CAACE,SAAS;IACrC,MAAMC,WAAW,GAAGL,IAAI,CAACE,IAAI,CAACG,WAAW;IAEzC,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;MAC7B;MACA;MACA,IAAIjB,MAAM,CAACf,IAAI,KAAK,SAAS,EAAE;QAC7Be,MAAM,CAACoB,OAAO,GAAGH,KAAK;MACxB;MACA;MACA;MACA;MACA;MAAA,KACK;QACH;QACA;QACA,MAAMnB,QAAQ,GAAG,UAAU,IAAIE,MAAM,GAAGA,MAAM,CAACF,QAAQ,GAAG,CAACE,MAAM,CAAC;QAClEA,MAAM,GAAG;UAACf,IAAI,EAAE,SAAS;UAAEmC,OAAO,EAAEH,KAAK;UAAEI,UAAU,EAAE,CAAC,CAAC;UAAEvB;QAAQ,CAAC;MACtE;IACF;IAEA,IAAIE,MAAM,CAACf,IAAI,KAAK,SAAS,IAAIkC,WAAW,EAAE;MAC5CG,MAAM,CAACC,MAAM,CAACvB,MAAM,CAACqB,UAAU,EAAE5D,eAAe,CAAC0D,WAAW,CAAC,CAAC;IAChE;IAEA,IACE,UAAU,IAAInB,MAAM,IACpBA,MAAM,CAACF,QAAQ,IACfoB,SAAS,KAAK,IAAI,IAClBA,SAAS,KAAKM,SAAS,EACvB;MACAxB,MAAM,CAACF,QAAQ,GAAGoB,SAAS;IAC7B;EACF;EAEA,OAAOlB,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,qBAAqBA,CAAC1B,KAAK,EAAEO,IAAI,EAAE;EAC1C,MAAMgC,IAAI,GAAGhC,IAAI,CAACgC,IAAI,IAAI,CAAC,CAAC;EAC5B;EACA,MAAMhB,MAAM,GACV,OAAO,IAAIhB,IAAI,IACf,EAAElB,GAAG,CAAC6B,IAAI,CAACqB,IAAI,EAAE,aAAa,CAAC,IAAIlD,GAAG,CAAC6B,IAAI,CAACqB,IAAI,EAAE,WAAW,CAAC,CAAC,GAC3D;IAAC/B,IAAI,EAAE,MAAM;IAAEyB,KAAK,EAAE1B,IAAI,CAAC0B;EAAK,CAAC,GACjC;IACEzB,IAAI,EAAE,SAAS;IACfmC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,CAAC,CAAC;IACdvB,QAAQ,EAAErB,KAAK,CAACC,GAAG,CAACM,IAAI;EAC1B,CAAC;EAEPP,KAAK,CAACK,KAAK,CAACE,IAAI,EAAEgB,MAAM,CAAC;EACzB,OAAOvB,KAAK,CAACE,SAAS,CAACK,IAAI,EAAEgB,MAAM,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASjB,IAAIA,CAACsB,KAAK,EAAEoB,KAAK,EAAE;EACjC;EACA,MAAMzB,MAAM,GAAG,EAAE;EACjB,IAAIM,KAAK,GAAG,CAAC,CAAC;EAEd,IAAImB,KAAK,EAAE;IACTzB,MAAM,CAACa,IAAI,CAAC;MAAC5B,IAAI,EAAE,MAAM;MAAEyB,KAAK,EAAE;IAAI,CAAC,CAAC;EAC1C;EAEA,OAAO,EAAEJ,KAAK,GAAGD,KAAK,CAACE,MAAM,EAAE;IAC7B,IAAID,KAAK,EAAEN,MAAM,CAACa,IAAI,CAAC;MAAC5B,IAAI,EAAE,MAAM;MAAEyB,KAAK,EAAE;IAAI,CAAC,CAAC;IACnDV,MAAM,CAACa,IAAI,CAACR,KAAK,CAACC,KAAK,CAAC,CAAC;EAC3B;EAEA,IAAImB,KAAK,IAAIpB,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IAC7BP,MAAM,CAACa,IAAI,CAAC;MAAC5B,IAAI,EAAE,MAAM;MAAEyB,KAAK,EAAE;IAAI,CAAC,CAAC;EAC1C;EAEA,OAAOV,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,sBAAsBA,CAACD,KAAK,EAAE;EACrC,IAAIJ,KAAK,GAAG,CAAC;EACb,IAAIoB,IAAI,GAAGhB,KAAK,CAACiB,UAAU,CAACrB,KAAK,CAAC;EAElC,OAAOoB,IAAI,KAAK,CAAC,IAAIA,IAAI,KAAK,EAAE,EAAE;IAChCpB,KAAK,EAAE;IACPoB,IAAI,GAAGhB,KAAK,CAACiB,UAAU,CAACrB,KAAK,CAAC;EAChC;EAEA,OAAOI,KAAK,CAACkB,KAAK,CAACtB,KAAK,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}