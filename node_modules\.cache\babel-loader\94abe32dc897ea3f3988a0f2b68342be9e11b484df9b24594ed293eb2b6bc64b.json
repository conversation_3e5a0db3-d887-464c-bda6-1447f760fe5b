{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = Math.random() * 16 | 0,\n      v = c == 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n}\nexport function ensureTrailingSlash(url) {\n  return url.endsWith('/') ? url : url + '/';\n}\nexport const isBrowser = () => typeof window !== 'undefined';\nexport function applySettingDefaults(options, defaults) {\n  var _a, _b;\n  const {\n    db: dbOptions,\n    auth: authOptions,\n    realtime: realtimeOptions,\n    global: globalOptions\n  } = options;\n  const {\n    db: DEFAULT_DB_OPTIONS,\n    auth: DEFAULT_AUTH_OPTIONS,\n    realtime: DEFAULT_REALTIME_OPTIONS,\n    global: DEFAULT_GLOBAL_OPTIONS\n  } = defaults;\n  const result = {\n    db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n    auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n    realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n    global: Object.assign(Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions), {\n      headers: Object.assign(Object.assign({}, (_a = DEFAULT_GLOBAL_OPTIONS === null || DEFAULT_GLOBAL_OPTIONS === void 0 ? void 0 : DEFAULT_GLOBAL_OPTIONS.headers) !== null && _a !== void 0 ? _a : {}), (_b = globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.headers) !== null && _b !== void 0 ? _b : {})\n    }),\n    accessToken: () => __awaiter(this, void 0, void 0, function* () {\n      return '';\n    })\n  };\n  if (options.accessToken) {\n    result.accessToken = options.accessToken;\n  } else {\n    // hack around Required<>\n    delete result.accessToken;\n  }\n  return result;\n}", "map": {"version": 3, "names": ["uuid", "replace", "c", "r", "Math", "random", "v", "toString", "ensureTrailingSlash", "url", "endsWith", "<PERSON><PERSON><PERSON><PERSON>", "window", "applySettingDefaults", "options", "defaults", "db", "dbOptions", "auth", "authOptions", "realtime", "realtimeOptions", "global", "globalOptions", "DEFAULT_DB_OPTIONS", "DEFAULT_AUTH_OPTIONS", "DEFAULT_REALTIME_OPTIONS", "DEFAULT_GLOBAL_OPTIONS", "result", "Object", "assign", "headers", "_a", "_b", "accessToken", "__awaiter"], "sources": ["C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@supabase\\supabase-js\\src\\lib\\helpers.ts"], "sourcesContent": ["// helpers.ts\nimport { SupabaseClientOptions } from './types'\n\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = (Math.random() * 16) | 0,\n      v = c == 'x' ? r : (r & 0x3) | 0x8\n    return v.toString(16)\n  })\n}\n\nexport function ensureTrailingSlash(url: string): string {\n  return url.endsWith('/') ? url : url + '/'\n}\n\nexport const isBrowser = () => typeof window !== 'undefined'\n\nexport function applySettingDefaults<\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database\n>(\n  options: SupabaseClientOptions<SchemaName>,\n  defaults: SupabaseClientOptions<any>\n): Required<SupabaseClientOptions<SchemaName>> {\n  const {\n    db: dbOptions,\n    auth: authOptions,\n    realtime: realtimeOptions,\n    global: globalOptions,\n  } = options\n  const {\n    db: DEFAULT_DB_OPTIONS,\n    auth: DEFAULT_AUTH_OPTIONS,\n    realtime: DEFAULT_REALTIME_OPTIONS,\n    global: DEFAULT_GLOBAL_OPTIONS,\n  } = defaults\n\n  const result: Required<SupabaseClientOptions<SchemaName>> = {\n    db: {\n      ...DEFAULT_DB_OPTIONS,\n      ...dbOptions,\n    },\n    auth: {\n      ...DEFAULT_AUTH_OPTIONS,\n      ...authOptions,\n    },\n    realtime: {\n      ...DEFAULT_REALTIME_OPTIONS,\n      ...realtimeOptions,\n    },\n    global: {\n      ...DEFAULT_GLOBAL_OPTIONS,\n      ...globalOptions,\n      headers: {\n        ...(DEFAULT_GLOBAL_OPTIONS?.headers ?? {}),\n        ...(globalOptions?.headers ?? {}),\n      },\n    },\n    accessToken: async () => '',\n  }\n\n  if (options.accessToken) {\n    result.accessToken = options.accessToken\n  } else {\n    // hack around Required<>\n    delete (result as any).accessToken\n  }\n\n  return result\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,OAAM,SAAUA,IAAIA,CAAA;EAClB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAUC,CAAC;IACxE,IAAIC,CAAC,GAAIC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAI,CAAC;MAC9BC,CAAC,GAAGJ,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;IACpC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUC,mBAAmBA,CAACC,GAAW;EAC7C,OAAOA,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,GAAGD,GAAG,GAAGA,GAAG,GAAG,GAAG;AAC5C;AAEA,OAAO,MAAME,SAAS,GAAGA,CAAA,KAAM,OAAOC,MAAM,KAAK,WAAW;AAE5D,OAAM,SAAUC,oBAAoBA,CAMlCC,OAA0C,EAC1CC,QAAoC;;EAEpC,MAAM;IACJC,EAAE,EAAEC,SAAS;IACbC,IAAI,EAAEC,WAAW;IACjBC,QAAQ,EAAEC,eAAe;IACzBC,MAAM,EAAEC;EAAa,CACtB,GAAGT,OAAO;EACX,MAAM;IACJE,EAAE,EAAEQ,kBAAkB;IACtBN,IAAI,EAAEO,oBAAoB;IAC1BL,QAAQ,EAAEM,wBAAwB;IAClCJ,MAAM,EAAEK;EAAsB,CAC/B,GAAGZ,QAAQ;EAEZ,MAAMa,MAAM,GAAgD;IAC1DZ,EAAE,EAAAa,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACGN,kBAAkB,GAClBP,SAAS,CACb;IACDC,IAAI,EAAAW,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACCL,oBAAoB,GACpBN,WAAW,CACf;IACDC,QAAQ,EAAAS,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACHJ,wBAAwB,GACxBL,eAAe,CACnB;IACDC,MAAM,EAAAO,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACDH,sBAAsB,GACtBJ,aAAa;MAChBQ,OAAO,EAAAF,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACD,CAAAE,EAAA,GAAAL,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEI,OAAO,cAAAC,EAAA,cAAAA,EAAA,GAAI,EAAG,GACtC,CAAAC,EAAA,GAAAV,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEQ,OAAO,cAAAE,EAAA,cAAAA,EAAA,GAAI,EAAG;IAAA,EAEpC;IACDC,WAAW,EAAEA,CAAA,KAAWC,SAAA;MAAC,SAAE;IAAA;GAC5B;EAED,IAAIrB,OAAO,CAACoB,WAAW,EAAE;IACvBN,MAAM,CAACM,WAAW,GAAGpB,OAAO,CAACoB,WAAW;GACzC,MAAM;IACL;IACA,OAAQN,MAAc,CAACM,WAAW;;EAGpC,OAAON,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}