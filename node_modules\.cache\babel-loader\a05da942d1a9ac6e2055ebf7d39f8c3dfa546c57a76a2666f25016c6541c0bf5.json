{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nvar style_to_object_1 = __importDefault(require(\"style-to-object\"));\nvar utilities_1 = require(\"./utilities\");\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style, options) {\n  var output = {};\n  if (!style || typeof style !== 'string') {\n    return output;\n  }\n  (0, style_to_object_1.default)(style, function (property, value) {\n    // skip CSS comment\n    if (property && value) {\n      output[(0, utilities_1.camelCase)(property, options)] = value;\n    }\n  });\n  return output;\n}\nStyleToJS.default = StyleToJS;\nmodule.exports = StyleToJS;", "map": {"version": 3, "names": ["style_to_object_1", "__importDefault", "require", "utilities_1", "StyleToJS", "style", "options", "output", "default", "property", "value", "camelCase", "module", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\style-to-js\\src\\index.ts"], "sourcesContent": ["import StyleToObject from 'style-to-object';\n\nimport { camelCase, CamelCaseOptions } from './utilities';\n\ntype StyleObject = Record<string, string>;\n\ninterface StyleToJSOptions extends CamelCaseOptions {}\n\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style: string, options?: StyleToJSOptions): StyleObject {\n  const output: StyleObject = {};\n\n  if (!style || typeof style !== 'string') {\n    return output;\n  }\n\n  StyleToObject(style, (property, value) => {\n    // skip CSS comment\n    if (property && value) {\n      output[camelCase(property, options)] = value;\n    }\n  });\n\n  return output;\n}\n\nStyleToJS.default = StyleToJS;\n\nexport = StyleToJS;\n"], "mappings": ";;;;;;;AAAA,IAAAA,iBAAA,GAAAC,eAAA,CAAAC,OAAA;AAEA,IAAAC,WAAA,GAAAD,OAAA;AAMA;;;AAGA,SAASE,SAASA,CAACC,KAAa,EAAEC,OAA0B;EAC1D,IAAMC,MAAM,GAAgB,EAAE;EAE9B,IAAI,CAACF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAOE,MAAM;EACf;EAEA,IAAAP,iBAAA,CAAAQ,OAAa,EAACH,KAAK,EAAE,UAACI,QAAQ,EAAEC,KAAK;IACnC;IACA,IAAID,QAAQ,IAAIC,KAAK,EAAE;MACrBH,MAAM,CAAC,IAAAJ,WAAA,CAAAQ,SAAS,EAACF,QAAQ,EAAEH,OAAO,CAAC,CAAC,GAAGI,KAAK;IAC9C;EACF,CAAC,CAAC;EAEF,OAAOH,MAAM;AACf;AAEAH,SAAS,CAACI,OAAO,GAAGJ,SAAS;AAE7BQ,MAAA,CAAAC,OAAA,GAAST,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}