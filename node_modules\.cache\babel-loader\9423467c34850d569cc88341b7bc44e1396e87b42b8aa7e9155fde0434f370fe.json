{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const lineEnding = {\n  name: 'lineEnding',\n  tokenize: tokenizeLineEnding\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLineEnding(effects, ok) {\n  return start;\n\n  /** @type {State} */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return factorySpace(effects, ok, types.linePrefix);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factorySpace", "markdownLineEnding", "types", "lineEnding", "name", "tokenize", "tokenizeLineEnding", "effects", "start", "code", "enter", "consume", "exit", "linePrefix"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark-core-commonmark/dev/lib/line-ending.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const lineEnding = {name: 'lineEnding', tokenize: tokenizeLineEnding}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLineEnding(effects, ok) {\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, ok, types.linePrefix)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,KAAK,QAAO,uBAAuB;;AAE3C;AACA,OAAO,MAAMC,UAAU,GAAG;EAACC,IAAI,EAAE,YAAY;EAAEC,QAAQ,EAAEC;AAAkB,CAAC;;AAE5E;AACA;AACA;AACA;AACA;AACA,SAASA,kBAAkBA,CAACC,OAAO,EAAET,EAAE,EAAE;EACvC,OAAOU,KAAK;;EAEZ;EACA,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBV,MAAM,CAACE,kBAAkB,CAACQ,IAAI,CAAC,EAAE,cAAc,CAAC;IAChDF,OAAO,CAACG,KAAK,CAACR,KAAK,CAACC,UAAU,CAAC;IAC/BI,OAAO,CAACI,OAAO,CAACF,IAAI,CAAC;IACrBF,OAAO,CAACK,IAAI,CAACV,KAAK,CAACC,UAAU,CAAC;IAC9B,OAAOH,YAAY,CAACO,OAAO,EAAET,EAAE,EAAEI,KAAK,CAACW,UAAU,CAAC;EACpD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}