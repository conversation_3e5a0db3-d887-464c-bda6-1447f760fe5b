{"ast": null, "code": "import { store } from '../../store/store';\n/**\n * Initiation\n * @param {string} userID - set the EmailJS user ID\n * @param {string} origin - set the EmailJS origin\n */\nexport const init = (userID, origin = 'https://api.emailjs.com') => {\n  store._userID = userID;\n  store._origin = origin;\n};", "map": {"version": 3, "names": ["store", "init", "userID", "origin", "_userID", "_origin"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/emailjs-com/es/methods/init/init.js"], "sourcesContent": ["import { store } from '../../store/store';\n/**\n * Initiation\n * @param {string} userID - set the EmailJS user ID\n * @param {string} origin - set the EmailJS origin\n */\nexport const init = (userID, origin = 'https://api.emailjs.com') => {\n    store._userID = userID;\n    store._origin = origin;\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,mBAAmB;AACzC;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAGA,CAACC,MAAM,EAAEC,MAAM,GAAG,yBAAyB,KAAK;EAChEH,KAAK,CAACI,OAAO,GAAGF,MAAM;EACtBF,KAAK,CAACK,OAAO,GAAGF,MAAM;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}