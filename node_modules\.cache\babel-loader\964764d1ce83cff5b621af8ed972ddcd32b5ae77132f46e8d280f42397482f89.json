{"ast": null, "code": "/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nexport const constants = /** @type {const} */{\n  attentionSideAfter: 2,\n  // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1,\n  // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6,\n  // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63,\n  // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32,\n  // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[',\n  // And preceded by `<![`.\n  characterGroupPunctuation: 2,\n  // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1,\n  // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7,\n  // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6,\n  // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31,\n  // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3,\n  // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2,\n  // At least 2 trailing spaces are needed.\n  htmlBasic: 6,\n  // Symbol for `<div`\n  htmlCdata: 5,\n  // Symbol for `<![CDATA[]]>`\n  htmlComment: 2,\n  // Symbol for `<!---->`\n  htmlComplete: 7,\n  // Symbol for `<x>`\n  htmlDeclaration: 4,\n  // Symbol for `<!doctype>`\n  htmlInstruction: 3,\n  // Symbol for `<?php?>`\n  htmlRawSizeMax: 8,\n  // Length of `textarea`.\n  htmlRaw: 1,\n  // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32,\n  // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999,\n  // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10,\n  // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4,\n  // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3,\n  // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n};", "map": {"version": 3, "names": ["constants", "attentionSideAfter", "attentionSideBefore", "atxHeadingOpeningFenceSizeMax", "autolinkDomainSizeMax", "autolinkSchemeSizeMax", "cdataOpeningString", "characterGroupPunctuation", "characterGroupWhitespace", "characterReferenceDecimalSizeMax", "characterReferenceHexadecimalSizeMax", "characterReferenceNamedSizeMax", "codeFencedSequenceSizeMin", "contentTypeContent", "contentTypeDocument", "contentTypeFlow", "contentTypeString", "contentTypeText", "hardBreakPrefixSizeMin", "htmlBasic", "htmlCdata", "htmlComment", "htmlComplete", "htmlDeclaration", "htmlInstruction", "htmlRawSizeMax", "htmlRaw", "linkResourceDestinationBalanceMax", "linkReferenceSizeMax", "listItemValueSizeMax", "numericBaseDecimal", "numericBaseHexadecimal", "tabSize", "thematicBreakMarkerCountMin", "v8MaxSafeChunkSize"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark-util-symbol/lib/constants.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nexport const constants = /** @type {const} */ ({\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlBasic: 6, // Symbol for `<div`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  htmlRaw: 1, // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,SAAS,GAAG,oBAAsB;EAC7CC,kBAAkB,EAAE,CAAC;EAAE;EACvBC,mBAAmB,EAAE,CAAC;EAAE;EACxBC,6BAA6B,EAAE,CAAC;EAAE;EAClCC,qBAAqB,EAAE,EAAE;EAAE;EAC3BC,qBAAqB,EAAE,EAAE;EAAE;EAC3BC,kBAAkB,EAAE,QAAQ;EAAE;EAC9BC,yBAAyB,EAAE,CAAC;EAAE;EAC9BC,wBAAwB,EAAE,CAAC;EAAE;EAC7BC,gCAAgC,EAAE,CAAC;EAAE;EACrCC,oCAAoC,EAAE,CAAC;EAAE;EACzCC,8BAA8B,EAAE,EAAE;EAAE;EACpCC,yBAAyB,EAAE,CAAC;EAAE;EAC9BC,kBAAkB,EAAE,SAAS;EAC7BC,mBAAmB,EAAE,UAAU;EAC/BC,eAAe,EAAE,MAAM;EACvBC,iBAAiB,EAAE,QAAQ;EAC3BC,eAAe,EAAE,MAAM;EACvBC,sBAAsB,EAAE,CAAC;EAAE;EAC3BC,SAAS,EAAE,CAAC;EAAE;EACdC,SAAS,EAAE,CAAC;EAAE;EACdC,WAAW,EAAE,CAAC;EAAE;EAChBC,YAAY,EAAE,CAAC;EAAE;EACjBC,eAAe,EAAE,CAAC;EAAE;EACpBC,eAAe,EAAE,CAAC;EAAE;EACpBC,cAAc,EAAE,CAAC;EAAE;EACnBC,OAAO,EAAE,CAAC;EAAE;EACZC,iCAAiC,EAAE,EAAE;EAAE;EACvCC,oBAAoB,EAAE,GAAG;EAAE;EAC3BC,oBAAoB,EAAE,EAAE;EAAE;EAC1BC,kBAAkB,EAAE,EAAE;EACtBC,sBAAsB,EAAE,IAAI;EAC5BC,OAAO,EAAE,CAAC;EAAE;EACZC,2BAA2B,EAAE,CAAC;EAAE;EAChCC,kBAAkB,EAAE,MAAM,CAAC;AAC7B,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}