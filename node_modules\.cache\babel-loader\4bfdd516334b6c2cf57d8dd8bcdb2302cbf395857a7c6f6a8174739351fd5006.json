{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n'';\n\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function tableCell(state, node) {\n  // Note: this function is normally not called: see `table-row` for how rows\n  // and their cells are compiled.\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'td',\n    // Assume body cell.\n    properties: {},\n    children: state.all(node)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["tableCell", "state", "node", "result", "type", "tagName", "properties", "children", "all", "patch", "applyData"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/mdast-util-to-hast/lib/handlers/table-cell.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function tableCell(state, node) {\n  // Note: this function is normally not called: see `table-row` for how rows\n  // and their cells are compiled.\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'td', // Assume body cell.\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACrC;EACA;EACA;EACA,MAAMC,MAAM,GAAG;IACbC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,IAAI;IAAE;IACfC,UAAU,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAEN,KAAK,CAACO,GAAG,CAACN,IAAI;EAC1B,CAAC;EACDD,KAAK,CAACQ,KAAK,CAACP,IAAI,EAAEC,MAAM,CAAC;EACzB,OAAOF,KAAK,CAACS,SAAS,CAACR,IAAI,EAAEC,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}