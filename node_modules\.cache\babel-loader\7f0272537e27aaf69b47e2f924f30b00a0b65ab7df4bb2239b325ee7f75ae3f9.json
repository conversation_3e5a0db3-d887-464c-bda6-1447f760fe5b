{"ast": null, "code": "import { create } from './util/create.js';\nimport { booleanish, number, spaceSeparated } from './util/types.js';\nexport const aria = create({\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  },\n  transform(_, property) {\n    return property === 'role' ? property : 'aria-' + property.slice(4).toLowerCase();\n  }\n});", "map": {"version": 3, "names": ["create", "booleanish", "number", "spaceSeparated", "aria", "properties", "ariaActiveDescendant", "ariaAtomic", "ariaAutoComplete", "ariaBusy", "ariaChe<PERSON>", "ariaColCount", "ariaColIndex", "ariaColSpan", "ariaControls", "aria<PERSON>urrent", "ariaDescribedBy", "ariaDetails", "ariaDisabled", "ariaDropEffect", "ariaErrorMessage", "ariaExpanded", "ariaFlowTo", "ariaGrabbed", "aria<PERSON>as<PERSON><PERSON><PERSON>", "ariaHidden", "ariaInvalid", "ariaKeyShortcuts", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "ariaLevel", "ariaLive", "ariaModal", "ariaMultiLine", "ariaMultiSelectable", "ariaOrientation", "ariaOwns", "ariaPlaceholder", "ariaPosInSet", "ariaPressed", "ariaReadOnly", "ariaRelevant", "ariaRequired", "ariaRoleDescription", "ariaRowCount", "ariaRowIndex", "ariaRowSpan", "ariaSelected", "ariaSetSize", "ariaSort", "ariaValueMax", "ariaValueMin", "ariaValueNow", "ariaValueText", "role", "transform", "_", "property", "slice", "toLowerCase"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/property-information/lib/aria.js"], "sourcesContent": ["import {create} from './util/create.js'\nimport {booleanish, number, spaceSeparated} from './util/types.js'\n\nexport const aria = create({\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  },\n  transform(_, property) {\n    return property === 'role'\n      ? property\n      : 'aria-' + property.slice(4).toLowerCase()\n  }\n})\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,kBAAkB;AACvC,SAAQC,UAAU,EAAEC,MAAM,EAAEC,cAAc,QAAO,iBAAiB;AAElE,OAAO,MAAMC,IAAI,GAAGJ,MAAM,CAAC;EACzBK,UAAU,EAAE;IACVC,oBAAoB,EAAE,IAAI;IAC1BC,UAAU,EAAEN,UAAU;IACtBO,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAER,UAAU;IACpBS,WAAW,EAAET,UAAU;IACvBU,YAAY,EAAET,MAAM;IACpBU,YAAY,EAAEV,MAAM;IACpBW,WAAW,EAAEX,MAAM;IACnBY,YAAY,EAAEX,cAAc;IAC5BY,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAEb,cAAc;IAC/Bc,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAEjB,UAAU;IACxBkB,cAAc,EAAEhB,cAAc;IAC9BiB,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAEpB,UAAU;IACxBqB,UAAU,EAAEnB,cAAc;IAC1BoB,WAAW,EAAEtB,UAAU;IACvBuB,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAExB,UAAU;IACtByB,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE1B,cAAc;IAC9B2B,SAAS,EAAE5B,MAAM;IACjB6B,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE/B,UAAU;IACrBgC,aAAa,EAAEhC,UAAU;IACzBiC,mBAAmB,EAAEjC,UAAU;IAC/BkC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAEjC,cAAc;IACxBkC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAEpC,MAAM;IACpBqC,WAAW,EAAEtC,UAAU;IACvBuC,YAAY,EAAEvC,UAAU;IACxBwC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAEzC,UAAU;IACxB0C,mBAAmB,EAAExC,cAAc;IACnCyC,YAAY,EAAE1C,MAAM;IACpB2C,YAAY,EAAE3C,MAAM;IACpB4C,WAAW,EAAE5C,MAAM;IACnB6C,YAAY,EAAE9C,UAAU;IACxB+C,WAAW,EAAE9C,MAAM;IACnB+C,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAEhD,MAAM;IACpBiD,YAAY,EAAEjD,MAAM;IACpBkD,YAAY,EAAElD,MAAM;IACpBmD,aAAa,EAAE,IAAI;IACnBC,IAAI,EAAE;EACR,CAAC;EACDC,SAASA,CAACC,CAAC,EAAEC,QAAQ,EAAE;IACrB,OAAOA,QAAQ,KAAK,MAAM,GACtBA,QAAQ,GACR,OAAO,GAAGA,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC/C;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}