{"ast": null, "code": "/**\n * @import {Node, Point, Position} from 'unist'\n * @import {Options as MessageOptions} from 'vfile-message'\n * @import {Compatible, Data, Map, Options, Value} from 'vfile'\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\nimport { VFileMessage } from 'vfile-message';\nimport { minpath } from '#minpath';\nimport { minproc } from '#minproc';\nimport { urlToPath, isUrl } from '#minurl';\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n */\nconst order = /** @type {const} */['history', 'path', 'basename', 'stem', 'extname', 'dirname'];\nexport class VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Uint8Array` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options;\n    if (!value) {\n      options = {};\n    } else if (isUrl(value)) {\n      options = {\n        path: value\n      };\n    } else if (typeof value === 'string' || isUint8Array(value)) {\n      options = {\n        value\n      };\n    } else {\n      options = value;\n    }\n\n    /* eslint-disable no-unused-expressions */\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    // Prevent calling `cwd` (which could be expensive) if it’s not needed;\n    // the empty string will be overridden in the next block.\n    this.cwd = 'cwd' in options ? '' : minproc.cwd();\n\n    /**\n     * Place to store custom info (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {};\n\n    /**\n     * List of file paths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = [];\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = [];\n\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value;\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map;\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result;\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored;\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1;\n    while (++index < order.length) {\n      const field = order[index];\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (field in options && options[field] !== undefined && options[field] !== null) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[field] = field === 'history' ? [...options[field]] : options[field];\n      }\n    }\n\n    /** @type {string} */\n    let field;\n\n    // Set non-path related properties.\n    for (field in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(field)) {\n        // @ts-expect-error: fine to set other things.\n        this[field] = options[field];\n      }\n    }\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   *\n   * @returns {string | undefined}\n   *   Basename.\n   */\n  get basename() {\n    return typeof this.path === 'string' ? minpath.basename(this.path) : undefined;\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} basename\n   *   Basename.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename');\n    assertPart(basename, 'basename');\n    this.path = minpath.join(this.dirname || '', basename);\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   *\n   * @returns {string | undefined}\n   *   Dirname.\n   */\n  get dirname() {\n    return typeof this.path === 'string' ? minpath.dirname(this.path) : undefined;\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} dirname\n   *   Dirname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname');\n    this.path = minpath.join(dirname || '', this.basename);\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   *\n   * @returns {string | undefined}\n   *   Extname.\n   */\n  get extname() {\n    return typeof this.path === 'string' ? minpath.extname(this.path) : undefined;\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} extname\n   *   Extname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname');\n    assertPath(this.dirname, 'extname');\n    if (extname) {\n      if (extname.codePointAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`');\n      }\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots');\n      }\n    }\n    this.path = minpath.join(this.dirname, this.stem + (extname || ''));\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   *   Path.\n   */\n  get path() {\n    return this.history[this.history.length - 1];\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {URL | string} path\n   *   Path.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set path(path) {\n    if (isUrl(path)) {\n      path = urlToPath(path);\n    }\n    assertNonEmpty(path, 'path');\n    if (this.path !== path) {\n      this.history.push(path);\n    }\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * @returns {string | undefined}\n   *   Stem.\n   */\n  get stem() {\n    return typeof this.path === 'string' ? minpath.basename(this.path, this.extname) : undefined;\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} stem\n   *   Stem.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem');\n    assertPart(stem, 'stem');\n    this.path = minpath.join(this.dirname || '', stem + (this.extname || ''));\n  }\n\n  // Normal prototypal methods.\n  /**\n   * Create a fatal message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `true` (error; file not usable)\n   * and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Never.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin);\n    message.fatal = true;\n    throw message;\n  }\n\n  /**\n   * Create an info message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `undefined` (info; change\n   * likely not needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin);\n    message.fatal = undefined;\n    return message;\n  }\n\n  /**\n   * Create a message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `false` (warning; change may be\n   * needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(causeOrReason, optionsOrParentOrPlace, origin) {\n    const message = new VFileMessage(\n    // @ts-expect-error: the overloads are fine.\n    causeOrReason, optionsOrParentOrPlace, origin);\n    if (this.path) {\n      message.name = this.path + ':' + message.name;\n      message.file = this.path;\n    }\n    message.fatal = false;\n    this.messages.push(message);\n    return message;\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * > **Note**: which encodings are supported depends on the engine.\n   * > For info on Node.js, see:\n   * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n   *\n   * @param {string | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Uint8Array`\n   *   (default: `'utf-8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    if (this.value === undefined) {\n      return '';\n    }\n    if (typeof this.value === 'string') {\n      return this.value;\n    }\n    const decoder = new TextDecoder(encoding || undefined);\n    return decoder.decode(this.value);\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {undefined}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(minpath.sep)) {\n    throw new Error('`' + name + '` cannot be a path: did not expect `' + minpath.sep + '`');\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty');\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too');\n  }\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(value && typeof value === 'object' && 'byteLength' in value && 'byteOffset' in value);\n}", "map": {"version": 3, "names": ["VFileMessage", "min<PERSON>", "minproc", "urlToPath", "isUrl", "order", "VFile", "constructor", "value", "options", "path", "isUint8Array", "cwd", "data", "history", "messages", "map", "result", "stored", "index", "length", "field", "undefined", "includes", "basename", "assertNonEmpty", "assertPart", "join", "dirname", "assertPath", "extname", "codePointAt", "Error", "stem", "push", "fail", "causeOrReason", "optionsOrParentOrPlace", "origin", "message", "fatal", "info", "name", "file", "toString", "encoding", "decoder", "TextDecoder", "decode", "part", "sep", "Boolean"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/vfile/lib/index.js"], "sourcesContent": ["/**\n * @import {Node, Point, Position} from 'unist'\n * @import {Options as MessageOptions} from 'vfile-message'\n * @import {Compatible, Data, Map, Options, Value} from 'vfile'\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\nimport {VFileMessage} from 'vfile-message'\nimport {minpath} from '#minpath'\nimport {minproc} from '#minproc'\nimport {urlToPath, isUrl} from '#minurl'\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n */\nconst order = /** @type {const} */ ([\n  'history',\n  'path',\n  'basename',\n  'stem',\n  'extname',\n  'dirname'\n])\n\nexport class VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Uint8Array` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (isUrl(value)) {\n      options = {path: value}\n    } else if (typeof value === 'string' || isUint8Array(value)) {\n      options = {value}\n    } else {\n      options = value\n    }\n\n    /* eslint-disable no-unused-expressions */\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    // Prevent calling `cwd` (which could be expensive) if it’s not needed;\n    // the empty string will be overridden in the next block.\n    this.cwd = 'cwd' in options ? '' : minproc.cwd()\n\n    /**\n     * Place to store custom info (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of file paths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const field = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        field in options &&\n        options[field] !== undefined &&\n        options[field] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[field] = field === 'history' ? [...options[field]] : options[field]\n      }\n    }\n\n    /** @type {string} */\n    let field\n\n    // Set non-path related properties.\n    for (field in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(field)) {\n        // @ts-expect-error: fine to set other things.\n        this[field] = options[field]\n      }\n    }\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   *\n   * @returns {string | undefined}\n   *   Basename.\n   */\n  get basename() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path)\n      : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} basename\n   *   Basename.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = minpath.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   *\n   * @returns {string | undefined}\n   *   Dirname.\n   */\n  get dirname() {\n    return typeof this.path === 'string'\n      ? minpath.dirname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} dirname\n   *   Dirname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = minpath.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   *\n   * @returns {string | undefined}\n   *   Extname.\n   */\n  get extname() {\n    return typeof this.path === 'string'\n      ? minpath.extname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} extname\n   *   Extname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.codePointAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = minpath.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   *   Path.\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {URL | string} path\n   *   Path.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set path(path) {\n    if (isUrl(path)) {\n      path = urlToPath(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * @returns {string | undefined}\n   *   Stem.\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} stem\n   *   Stem.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = minpath.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  // Normal prototypal methods.\n  /**\n   * Create a fatal message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `true` (error; file not usable)\n   * and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Never.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n\n  /**\n   * Create an info message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `undefined` (info; change\n   * likely not needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = undefined\n\n    return message\n  }\n\n  /**\n   * Create a message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `false` (warning; change may be\n   * needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(causeOrReason, optionsOrParentOrPlace, origin) {\n    const message = new VFileMessage(\n      // @ts-expect-error: the overloads are fine.\n      causeOrReason,\n      optionsOrParentOrPlace,\n      origin\n    )\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * > **Note**: which encodings are supported depends on the engine.\n   * > For info on Node.js, see:\n   * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n   *\n   * @param {string | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Uint8Array`\n   *   (default: `'utf-8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    if (this.value === undefined) {\n      return ''\n    }\n\n    if (typeof this.value === 'string') {\n      return this.value\n    }\n\n    const decoder = new TextDecoder(encoding || undefined)\n    return decoder.decode(this.value)\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {undefined}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(minpath.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + minpath.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,eAAe;AAC1C,SAAQC,OAAO,QAAO,UAAU;AAChC,SAAQC,OAAO,QAAO,UAAU;AAChC,SAAQC,SAAS,EAAEC,KAAK,QAAO,SAAS;;AAExC;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAG,oBAAsB,CAClC,SAAS,EACT,MAAM,EACN,UAAU,EACV,MAAM,EACN,SAAS,EACT,SAAS,CACT;AAEF,OAAO,MAAMC,KAAK,CAAC;EACjB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,KAAK,EAAE;IACjB;IACA,IAAIC,OAAO;IAEX,IAAI,CAACD,KAAK,EAAE;MACVC,OAAO,GAAG,CAAC,CAAC;IACd,CAAC,MAAM,IAAIL,KAAK,CAACI,KAAK,CAAC,EAAE;MACvBC,OAAO,GAAG;QAACC,IAAI,EAAEF;MAAK,CAAC;IACzB,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIG,YAAY,CAACH,KAAK,CAAC,EAAE;MAC3DC,OAAO,GAAG;QAACD;MAAK,CAAC;IACnB,CAAC,MAAM;MACLC,OAAO,GAAGD,KAAK;IACjB;;IAEA;;IAEA;AACJ;AACA;AACA;AACA;IACI;IACA;IACA,IAAI,CAACI,GAAG,GAAG,KAAK,IAAIH,OAAO,GAAG,EAAE,GAAGP,OAAO,CAACU,GAAG,CAAC,CAAC;;IAEhD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;;IAEd;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,OAAO,GAAG,EAAE;;IAEjB;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,QAAQ,GAAG,EAAE;;IAElB;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACP,KAAK;;IAEV;IACA;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACQ,GAAG;;IAER;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM;;IAEX;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM;IACX;;IAEA;IACA,IAAIC,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,EAAEA,KAAK,GAAGd,KAAK,CAACe,MAAM,EAAE;MAC7B,MAAMC,KAAK,GAAGhB,KAAK,CAACc,KAAK,CAAC;;MAE1B;MACA;MACA,IACEE,KAAK,IAAIZ,OAAO,IAChBA,OAAO,CAACY,KAAK,CAAC,KAAKC,SAAS,IAC5Bb,OAAO,CAACY,KAAK,CAAC,KAAK,IAAI,EACvB;QACA;QACA,IAAI,CAACA,KAAK,CAAC,GAAGA,KAAK,KAAK,SAAS,GAAG,CAAC,GAAGZ,OAAO,CAACY,KAAK,CAAC,CAAC,GAAGZ,OAAO,CAACY,KAAK,CAAC;MAC1E;IACF;;IAEA;IACA,IAAIA,KAAK;;IAET;IACA,KAAKA,KAAK,IAAIZ,OAAO,EAAE;MACrB;MACA,IAAI,CAACJ,KAAK,CAACkB,QAAQ,CAACF,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACA,KAAK,CAAC,GAAGZ,OAAO,CAACY,KAAK,CAAC;MAC9B;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIG,QAAQA,CAAA,EAAG;IACb,OAAO,OAAO,IAAI,CAACd,IAAI,KAAK,QAAQ,GAChCT,OAAO,CAACuB,QAAQ,CAAC,IAAI,CAACd,IAAI,CAAC,GAC3BY,SAAS;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIE,QAAQA,CAACA,QAAQ,EAAE;IACrBC,cAAc,CAACD,QAAQ,EAAE,UAAU,CAAC;IACpCE,UAAU,CAACF,QAAQ,EAAE,UAAU,CAAC;IAChC,IAAI,CAACd,IAAI,GAAGT,OAAO,CAAC0B,IAAI,CAAC,IAAI,CAACC,OAAO,IAAI,EAAE,EAAEJ,QAAQ,CAAC;EACxD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAII,OAAOA,CAAA,EAAG;IACZ,OAAO,OAAO,IAAI,CAAClB,IAAI,KAAK,QAAQ,GAChCT,OAAO,CAAC2B,OAAO,CAAC,IAAI,CAAClB,IAAI,CAAC,GAC1BY,SAAS;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIM,OAAOA,CAACA,OAAO,EAAE;IACnBC,UAAU,CAAC,IAAI,CAACL,QAAQ,EAAE,SAAS,CAAC;IACpC,IAAI,CAACd,IAAI,GAAGT,OAAO,CAAC0B,IAAI,CAACC,OAAO,IAAI,EAAE,EAAE,IAAI,CAACJ,QAAQ,CAAC;EACxD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIM,OAAOA,CAAA,EAAG;IACZ,OAAO,OAAO,IAAI,CAACpB,IAAI,KAAK,QAAQ,GAChCT,OAAO,CAAC6B,OAAO,CAAC,IAAI,CAACpB,IAAI,CAAC,GAC1BY,SAAS;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIQ,OAAOA,CAACA,OAAO,EAAE;IACnBJ,UAAU,CAACI,OAAO,EAAE,SAAS,CAAC;IAC9BD,UAAU,CAAC,IAAI,CAACD,OAAO,EAAE,SAAS,CAAC;IAEnC,IAAIE,OAAO,EAAE;MACX,IAAIA,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,WAAW;QAC3C,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,IAAIF,OAAO,CAACP,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;QAC5B,MAAM,IAAIS,KAAK,CAAC,wCAAwC,CAAC;MAC3D;IACF;IAEA,IAAI,CAACtB,IAAI,GAAGT,OAAO,CAAC0B,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAACK,IAAI,IAAIH,OAAO,IAAI,EAAE,CAAC,CAAC;EACrE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIpB,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACI,OAAO,CAAC,IAAI,CAACA,OAAO,CAACM,MAAM,GAAG,CAAC,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIV,IAAIA,CAACA,IAAI,EAAE;IACb,IAAIN,KAAK,CAACM,IAAI,CAAC,EAAE;MACfA,IAAI,GAAGP,SAAS,CAACO,IAAI,CAAC;IACxB;IAEAe,cAAc,CAACf,IAAI,EAAE,MAAM,CAAC;IAE5B,IAAI,IAAI,CAACA,IAAI,KAAKA,IAAI,EAAE;MACtB,IAAI,CAACI,OAAO,CAACoB,IAAI,CAACxB,IAAI,CAAC;IACzB;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIuB,IAAIA,CAAA,EAAG;IACT,OAAO,OAAO,IAAI,CAACvB,IAAI,KAAK,QAAQ,GAChCT,OAAO,CAACuB,QAAQ,CAAC,IAAI,CAACd,IAAI,EAAE,IAAI,CAACoB,OAAO,CAAC,GACzCR,SAAS;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIW,IAAIA,CAACA,IAAI,EAAE;IACbR,cAAc,CAACQ,IAAI,EAAE,MAAM,CAAC;IAC5BP,UAAU,CAACO,IAAI,EAAE,MAAM,CAAC;IACxB,IAAI,CAACvB,IAAI,GAAGT,OAAO,CAAC0B,IAAI,CAAC,IAAI,CAACC,OAAO,IAAI,EAAE,EAAEK,IAAI,IAAI,IAAI,CAACH,OAAO,IAAI,EAAE,CAAC,CAAC;EAC3E;;EAEA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEK,IAAIA,CAACC,aAAa,EAAEC,sBAAsB,EAAEC,MAAM,EAAE;IAClD;IACA,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACH,aAAa,EAAEC,sBAAsB,EAAEC,MAAM,CAAC;IAE3EC,OAAO,CAACC,KAAK,GAAG,IAAI;IAEpB,MAAMD,OAAO;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,IAAIA,CAACL,aAAa,EAAEC,sBAAsB,EAAEC,MAAM,EAAE;IAClD;IACA,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACH,aAAa,EAAEC,sBAAsB,EAAEC,MAAM,CAAC;IAE3EC,OAAO,CAACC,KAAK,GAAGlB,SAAS;IAEzB,OAAOiB,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEA,OAAOA,CAACH,aAAa,EAAEC,sBAAsB,EAAEC,MAAM,EAAE;IACrD,MAAMC,OAAO,GAAG,IAAIvC,YAAY;IAC9B;IACAoC,aAAa,EACbC,sBAAsB,EACtBC,MACF,CAAC;IAED,IAAI,IAAI,CAAC5B,IAAI,EAAE;MACb6B,OAAO,CAACG,IAAI,GAAG,IAAI,CAAChC,IAAI,GAAG,GAAG,GAAG6B,OAAO,CAACG,IAAI;MAC7CH,OAAO,CAACI,IAAI,GAAG,IAAI,CAACjC,IAAI;IAC1B;IAEA6B,OAAO,CAACC,KAAK,GAAG,KAAK;IAErB,IAAI,CAACzB,QAAQ,CAACmB,IAAI,CAACK,OAAO,CAAC;IAE3B,OAAOA,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEK,QAAQA,CAACC,QAAQ,EAAE;IACjB,IAAI,IAAI,CAACrC,KAAK,KAAKc,SAAS,EAAE;MAC5B,OAAO,EAAE;IACX;IAEA,IAAI,OAAO,IAAI,CAACd,KAAK,KAAK,QAAQ,EAAE;MAClC,OAAO,IAAI,CAACA,KAAK;IACnB;IAEA,MAAMsC,OAAO,GAAG,IAAIC,WAAW,CAACF,QAAQ,IAAIvB,SAAS,CAAC;IACtD,OAAOwB,OAAO,CAACE,MAAM,CAAC,IAAI,CAACxC,KAAK,CAAC;EACnC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,UAAUA,CAACuB,IAAI,EAAEP,IAAI,EAAE;EAC9B,IAAIO,IAAI,IAAIA,IAAI,CAAC1B,QAAQ,CAACtB,OAAO,CAACiD,GAAG,CAAC,EAAE;IACtC,MAAM,IAAIlB,KAAK,CACb,GAAG,GAAGU,IAAI,GAAG,sCAAsC,GAAGzC,OAAO,CAACiD,GAAG,GAAG,GACtE,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASzB,cAAcA,CAACwB,IAAI,EAAEP,IAAI,EAAE;EAClC,IAAI,CAACO,IAAI,EAAE;IACT,MAAM,IAAIjB,KAAK,CAAC,GAAG,GAAGU,IAAI,GAAG,mBAAmB,CAAC;EACnD;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASb,UAAUA,CAACnB,IAAI,EAAEgC,IAAI,EAAE;EAC9B,IAAI,CAAChC,IAAI,EAAE;IACT,MAAM,IAAIsB,KAAK,CAAC,WAAW,GAAGU,IAAI,GAAG,iCAAiC,CAAC;EACzE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/B,YAAYA,CAACH,KAAK,EAAE;EAC3B,OAAO2C,OAAO,CACZ3C,KAAK,IACH,OAAOA,KAAK,KAAK,QAAQ,IACzB,YAAY,IAAIA,KAAK,IACrB,YAAY,IAAIA,KACpB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}