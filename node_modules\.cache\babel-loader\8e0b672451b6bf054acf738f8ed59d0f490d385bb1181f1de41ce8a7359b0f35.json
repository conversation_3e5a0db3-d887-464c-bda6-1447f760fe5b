{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n'';\n\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function paragraph(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'p',\n    properties: {},\n    children: state.all(node)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["paragraph", "state", "node", "result", "type", "tagName", "properties", "children", "all", "patch", "applyData"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/mdast-util-to-hast/lib/handlers/paragraph.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function paragraph(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'p',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACrC;EACA,MAAMC,MAAM,GAAG;IACbC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,GAAG;IACZC,UAAU,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAEN,KAAK,CAACO,GAAG,CAACN,IAAI;EAC1B,CAAC;EACDD,KAAK,CAACQ,KAAK,CAACP,IAAI,EAAEC,MAAM,CAAC;EACzB,OAAOF,KAAK,CAACS,SAAS,CAACR,IAAI,EAAEC,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}