{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').Check} Check\n * @typedef {import('./lib/index.js').Test} Test\n * @typedef {import('./lib/index.js').TestFunction} TestFunction\n */\n\nexport { is, convert } from './lib/index.js';", "map": {"version": 3, "names": ["is", "convert"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/unist-util-is/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').Check} Check\n * @typedef {import('./lib/index.js').Test} Test\n * @typedef {import('./lib/index.js').TestFunction} TestFunction\n */\n\nexport {is, convert} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,EAAEC,OAAO,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}