{"ast": null, "code": "import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = (url, data, headers = {}) => {\n  return new Promise((resolve, reject) => {\n    const xhr = new XMLHttpRequest();\n    xhr.addEventListener('load', ({\n      target\n    }) => {\n      const responseStatus = new EmailJSResponseStatus(target);\n      if (responseStatus.status === 200 || responseStatus.text === 'OK') {\n        resolve(responseStatus);\n      } else {\n        reject(responseStatus);\n      }\n    });\n    xhr.addEventListener('error', ({\n      target\n    }) => {\n      reject(new EmailJSResponseStatus(target));\n    });\n    xhr.open('POST', store._origin + url, true);\n    Object.keys(headers).forEach(key => {\n      xhr.setRequestHeader(key, headers[key]);\n    });\n    xhr.send(data);\n  });\n};", "map": {"version": 3, "names": ["EmailJSResponseStatus", "store", "sendPost", "url", "data", "headers", "Promise", "resolve", "reject", "xhr", "XMLHttpRequest", "addEventListener", "target", "responseStatus", "status", "text", "open", "_origin", "Object", "keys", "for<PERSON>ach", "key", "setRequestHeader", "send"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/emailjs-com/es/api/sendPost.js"], "sourcesContent": ["import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = (url, data, headers = {}) => {\n    return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest();\n        xhr.addEventListener('load', ({ target }) => {\n            const responseStatus = new EmailJSResponseStatus(target);\n            if (responseStatus.status === 200 || responseStatus.text === 'OK') {\n                resolve(responseStatus);\n            }\n            else {\n                reject(responseStatus);\n            }\n        });\n        xhr.addEventListener('error', ({ target }) => {\n            reject(new EmailJSResponseStatus(target));\n        });\n        xhr.open('POST', store._origin + url, true);\n        Object.keys(headers).forEach((key) => {\n            xhr.setRequestHeader(key, headers[key]);\n        });\n        xhr.send(data);\n    });\n};\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,MAAMC,QAAQ,GAAGA,CAACC,GAAG,EAAEC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACjD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpC,MAAMC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAChCD,GAAG,CAACE,gBAAgB,CAAC,MAAM,EAAE,CAAC;MAAEC;IAAO,CAAC,KAAK;MACzC,MAAMC,cAAc,GAAG,IAAIb,qBAAqB,CAACY,MAAM,CAAC;MACxD,IAAIC,cAAc,CAACC,MAAM,KAAK,GAAG,IAAID,cAAc,CAACE,IAAI,KAAK,IAAI,EAAE;QAC/DR,OAAO,CAACM,cAAc,CAAC;MAC3B,CAAC,MACI;QACDL,MAAM,CAACK,cAAc,CAAC;MAC1B;IACJ,CAAC,CAAC;IACFJ,GAAG,CAACE,gBAAgB,CAAC,OAAO,EAAE,CAAC;MAAEC;IAAO,CAAC,KAAK;MAC1CJ,MAAM,CAAC,IAAIR,qBAAqB,CAACY,MAAM,CAAC,CAAC;IAC7C,CAAC,CAAC;IACFH,GAAG,CAACO,IAAI,CAAC,MAAM,EAAEf,KAAK,CAACgB,OAAO,GAAGd,GAAG,EAAE,IAAI,CAAC;IAC3Ce,MAAM,CAACC,IAAI,CAACd,OAAO,CAAC,CAACe,OAAO,CAAEC,GAAG,IAAK;MAClCZ,GAAG,CAACa,gBAAgB,CAACD,GAAG,EAAEhB,OAAO,CAACgB,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC;IACFZ,GAAG,CAACc,IAAI,CAACnB,IAAI,CAAC;EAClB,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}