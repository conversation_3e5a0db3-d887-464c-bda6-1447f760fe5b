{"ast": null, "code": "/**\n * @import {Space} from 'property-information'\n */\n\nimport { Info } from './info.js';\nimport * as types from './types.js';\nconst checks = /** @type {ReadonlyArray<keyof typeof types>} */\nObject.keys(types);\nexport class DefinedInfo extends Info {\n  /**\n   * @constructor\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @param {number | null | undefined} [mask]\n   *   Mask.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1;\n    super(property, attribute);\n    mark(this, 'space', space);\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index];\n        mark(this, checks[index], (mask & types[check]) === types[check]);\n      }\n    }\n  }\n}\nDefinedInfo.prototype.defined = true;\n\n/**\n * @template {keyof DefinedInfo} Key\n *   Key type.\n * @param {DefinedInfo} values\n *   Info.\n * @param {Key} key\n *   Key.\n * @param {DefinedInfo[Key]} value\n *   Value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value;\n  }\n}", "map": {"version": 3, "names": ["Info", "types", "checks", "Object", "keys", "DefinedInfo", "constructor", "property", "attribute", "mask", "space", "index", "mark", "length", "check", "prototype", "defined", "values", "key", "value"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/property-information/lib/util/defined-info.js"], "sourcesContent": ["/**\n * @import {Space} from 'property-information'\n */\n\nimport {Info} from './info.js'\nimport * as types from './types.js'\n\nconst checks = /** @type {ReadonlyArray<keyof typeof types>} */ (\n  Object.keys(types)\n)\n\nexport class DefinedInfo extends Info {\n  /**\n   * @constructor\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @param {number | null | undefined} [mask]\n   *   Mask.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & types[check]) === types[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @template {keyof DefinedInfo} Key\n *   Key type.\n * @param {DefinedInfo} values\n *   Info.\n * @param {Key} key\n *   Key.\n * @param {DefinedInfo[Key]} value\n *   Value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,IAAI,QAAO,WAAW;AAC9B,OAAO,KAAKC,KAAK,MAAM,YAAY;AAEnC,MAAMC,MAAM,GAAG;AACbC,MAAM,CAACC,IAAI,CAACH,KAAK,CAClB;AAED,OAAO,MAAMI,WAAW,SAASL,IAAI,CAAC;EACpC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEM,WAAWA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAE;IAC5C,IAAIC,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,CAACJ,QAAQ,EAAEC,SAAS,CAAC;IAE1BI,IAAI,CAAC,IAAI,EAAE,OAAO,EAAEF,KAAK,CAAC;IAE1B,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAO,EAAEE,KAAK,GAAGT,MAAM,CAACW,MAAM,EAAE;QAC9B,MAAMC,KAAK,GAAGZ,MAAM,CAACS,KAAK,CAAC;QAC3BC,IAAI,CAAC,IAAI,EAAEV,MAAM,CAACS,KAAK,CAAC,EAAE,CAACF,IAAI,GAAGR,KAAK,CAACa,KAAK,CAAC,MAAMb,KAAK,CAACa,KAAK,CAAC,CAAC;MACnE;IACF;EACF;AACF;AAEAT,WAAW,CAACU,SAAS,CAACC,OAAO,GAAG,IAAI;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,IAAIA,CAACK,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAChC,IAAIA,KAAK,EAAE;IACTF,MAAM,CAACC,GAAG,CAAC,GAAGC,KAAK;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}