{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n// Make VS Code show references to the above types.\n'';\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */\nexport function html(state, node) {\n  if (state.options.allowDangerousHtml) {\n    /** @type {Raw} */\n    const result = {\n      type: 'raw',\n      value: node.value\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["html", "state", "node", "options", "allowDangerousHtml", "result", "type", "value", "patch", "applyData", "undefined"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/mdast-util-to-hast/lib/handlers/html.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */\nexport function html(state, node) {\n  if (state.options.allowDangerousHtml) {\n    /** @type {Raw} */\n    const result = {type: 'raw', value: node.value}\n    state.patch(node, result)\n    return state.applyData(node, result)\n  }\n\n  return undefined\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC,IAAID,KAAK,CAACE,OAAO,CAACC,kBAAkB,EAAE;IACpC;IACA,MAAMC,MAAM,GAAG;MAACC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAEL,IAAI,CAACK;IAAK,CAAC;IAC/CN,KAAK,CAACO,KAAK,CAACN,IAAI,EAAEG,MAAM,CAAC;IACzB,OAAOJ,KAAK,CAACQ,SAAS,CAACP,IAAI,EAAEG,MAAM,CAAC;EACtC;EAEA,OAAOK,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}