{"ast": null, "code": "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuation\n};\n\n/** @type {Construct} */\nexport const codeFenced = {\n  concrete: true,\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this;\n  /** @type {Construct} */\n  const closeStart = {\n    partial: true,\n    tokenize: tokenizeCloseStart\n  };\n  let initialPrefix = 0;\n  let sizeOpen = 0;\n  /** @type {NonNullable<Code>} */\n  let marker;\n  return start;\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code);\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    assert(code === codes.graveAccent || code === codes.tilde, 'expected `` ` `` or `~`');\n    const tail = self.events[self.events.length - 1];\n    initialPrefix = tail && tail[1].type === types.linePrefix ? tail[2].sliceSerialize(tail[1], true).length : 0;\n    marker = code;\n    effects.enter(types.codeFenced);\n    effects.enter(types.codeFencedFence);\n    effects.enter(types.codeFencedFenceSequence);\n    return sequenceOpen(code);\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++;\n      effects.consume(code);\n      return sequenceOpen;\n    }\n    if (sizeOpen < constants.codeFencedSequenceSizeMin) {\n      return nok(code);\n    }\n    effects.exit(types.codeFencedFenceSequence);\n    return markdownSpace(code) ? factorySpace(effects, infoBefore, types.whitespace)(code) : infoBefore(code);\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFencedFence);\n      return self.interrupt ? ok(code) : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code);\n    }\n    effects.enter(types.codeFencedFenceInfo);\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return info(code);\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString);\n      effects.exit(types.codeFencedFenceInfo);\n      return infoBefore(code);\n    }\n    if (markdownSpace(code)) {\n      effects.exit(types.chunkString);\n      effects.exit(types.codeFencedFenceInfo);\n      return factorySpace(effects, metaBefore, types.whitespace)(code);\n    }\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return info;\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return infoBefore(code);\n    }\n    effects.enter(types.codeFencedFenceMeta);\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return meta(code);\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString);\n      effects.exit(types.codeFencedFenceMeta);\n      return infoBefore(code);\n    }\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return meta;\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    return effects.attempt(closeStart, after, contentBefore)(code);\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return contentStart;\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && markdownSpace(code) ? factorySpace(effects, beforeContentChunk, types.linePrefix, initialPrefix + 1)(code) : beforeContentChunk(code);\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code);\n    }\n    effects.enter(types.codeFlowValue);\n    return contentChunk(code);\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue);\n      return beforeContentChunk(code);\n    }\n    effects.consume(code);\n    return contentChunk;\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(types.codeFenced);\n    return ok(code);\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0;\n    return startBefore;\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      assert(markdownLineEnding(code), 'expected eol');\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      return start;\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n\n      // To do: `enter` here or in next state?\n      effects.enter(types.codeFencedFence);\n      return markdownSpace(code) ? factorySpace(effects, beforeSequenceClose, types.linePrefix, self.parser.constructs.disable.null.includes('codeIndented') ? undefined : constants.tabSize)(code) : beforeSequenceClose(code);\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(types.codeFencedFenceSequence);\n        return sequenceClose(code);\n      }\n      return nok(code);\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++;\n        effects.consume(code);\n        return sequenceClose;\n      }\n      if (size >= sizeOpen) {\n        effects.exit(types.codeFencedFenceSequence);\n        return markdownSpace(code) ? factorySpace(effects, sequenceCloseAfter, types.whitespace)(code) : sequenceCloseAfter(code);\n      }\n      return nok(code);\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(types.codeFencedFence);\n        return ok(code);\n      }\n      return nok(code);\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.eof) {\n      return nok(code);\n    }\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return lineStart;\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factorySpace", "markdownLineEnding", "markdownSpace", "codes", "constants", "types", "nonLazyContinuation", "partial", "tokenize", "tokenizeNonLazyContinuation", "codeFenced", "concrete", "name", "tokenizeCodeFenced", "effects", "nok", "self", "closeStart", "tokenizeCloseStart", "initialPrefix", "sizeOpen", "marker", "start", "code", "beforeSequenceOpen", "graveAccent", "tilde", "tail", "events", "length", "type", "linePrefix", "sliceSerialize", "enter", "codeFencedFence", "codeFencedFenceSequence", "sequenceOpen", "consume", "codeFencedSequenceSizeMin", "exit", "infoBefore", "whitespace", "eof", "interrupt", "check", "atNonLazyBreak", "after", "codeFencedFenceInfo", "chunkString", "contentType", "contentTypeString", "info", "metaBefore", "codeFencedFenceMeta", "meta", "attempt", "contentBefore", "lineEnding", "contentStart", "beforeContentChunk", "codeFlowValue", "contentChunk", "size", "startBefore", "parser", "constructs", "disable", "null", "beforeSequenceClose", "includes", "undefined", "tabSize", "sequenceClose", "sequenceCloseAfter", "lineStart", "lazy", "now", "line"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/micromark-core-commonmark/dev/lib/code-fenced.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuation\n}\n\n/** @type {Construct} */\nexport const codeFenced = {\n  concrete: true,\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this\n  /** @type {Construct} */\n  const closeStart = {partial: true, tokenize: tokenizeCloseStart}\n  let initialPrefix = 0\n  let sizeOpen = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code)\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    assert(\n      code === codes.graveAccent || code === codes.tilde,\n      'expected `` ` `` or `~`'\n    )\n\n    const tail = self.events[self.events.length - 1]\n    initialPrefix =\n      tail && tail[1].type === types.linePrefix\n        ? tail[2].sliceSerialize(tail[1], true).length\n        : 0\n\n    marker = code\n    effects.enter(types.codeFenced)\n    effects.enter(types.codeFencedFence)\n    effects.enter(types.codeFencedFenceSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    if (sizeOpen < constants.codeFencedSequenceSizeMin) {\n      return nok(code)\n    }\n\n    effects.exit(types.codeFencedFenceSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, infoBefore, types.whitespace)(code)\n      : infoBefore(code)\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFencedFence)\n      return self.interrupt\n        ? ok(code)\n        : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFencedFenceInfo)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return info(code)\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return infoBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return factorySpace(effects, metaBefore, types.whitespace)(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return info\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return infoBefore(code)\n    }\n\n    effects.enter(types.codeFencedFenceMeta)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceMeta)\n      return infoBefore(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    return effects.attempt(closeStart, after, contentBefore)(code)\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return contentStart\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && markdownSpace(code)\n      ? factorySpace(\n          effects,\n          beforeContentChunk,\n          types.linePrefix,\n          initialPrefix + 1\n        )(code)\n      : beforeContentChunk(code)\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFlowValue)\n    return contentChunk(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue)\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(types.codeFenced)\n    return ok(code)\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0\n\n    return startBefore\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      assert(markdownLineEnding(code), 'expected eol')\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return start\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      // To do: `enter` here or in next state?\n      effects.enter(types.codeFencedFence)\n      return markdownSpace(code)\n        ? factorySpace(\n            effects,\n            beforeSequenceClose,\n            types.linePrefix,\n            self.parser.constructs.disable.null.includes('codeIndented')\n              ? undefined\n              : constants.tabSize\n          )(code)\n        : beforeSequenceClose(code)\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(types.codeFencedFenceSequence)\n        return sequenceClose(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size >= sizeOpen) {\n        effects.exit(types.codeFencedFenceSequence)\n        return markdownSpace(code)\n          ? factorySpace(effects, sequenceCloseAfter, types.whitespace)(code)\n          : sequenceCloseAfter(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(types.codeFencedFence)\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineStart\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,EAAEC,aAAa,QAAO,0BAA0B;AAC1E,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,YAAY;EAClBJ,QAAQ,EAAEK;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASA,kBAAkBA,CAACC,OAAO,EAAEhB,EAAE,EAAEiB,GAAG,EAAE;EAC5C,MAAMC,IAAI,GAAG,IAAI;EACjB;EACA,MAAMC,UAAU,GAAG;IAACV,OAAO,EAAE,IAAI;IAAEC,QAAQ,EAAEU;EAAkB,CAAC;EAChE,IAAIC,aAAa,GAAG,CAAC;EACrB,IAAIC,QAAQ,GAAG,CAAC;EAChB;EACA,IAAIC,MAAM;EAEV,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB;IACA,OAAOC,kBAAkB,CAACD,IAAI,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,kBAAkBA,CAACD,IAAI,EAAE;IAChCxB,MAAM,CACJwB,IAAI,KAAKpB,KAAK,CAACsB,WAAW,IAAIF,IAAI,KAAKpB,KAAK,CAACuB,KAAK,EAClD,yBACF,CAAC;IAED,MAAMC,IAAI,GAAGX,IAAI,CAACY,MAAM,CAACZ,IAAI,CAACY,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAChDV,aAAa,GACXQ,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKzB,KAAK,CAAC0B,UAAU,GACrCJ,IAAI,CAAC,CAAC,CAAC,CAACK,cAAc,CAACL,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACE,MAAM,GAC5C,CAAC;IAEPR,MAAM,GAAGE,IAAI;IACbT,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAACK,UAAU,CAAC;IAC/BI,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAAC6B,eAAe,CAAC;IACpCpB,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAAC8B,uBAAuB,CAAC;IAC5C,OAAOC,YAAY,CAACb,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASa,YAAYA,CAACb,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAKF,MAAM,EAAE;MACnBD,QAAQ,EAAE;MACVN,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;MACrB,OAAOa,YAAY;IACrB;IAEA,IAAIhB,QAAQ,GAAGhB,SAAS,CAACkC,yBAAyB,EAAE;MAClD,OAAOvB,GAAG,CAACQ,IAAI,CAAC;IAClB;IAEAT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAAC8B,uBAAuB,CAAC;IAC3C,OAAOjC,aAAa,CAACqB,IAAI,CAAC,GACtBvB,YAAY,CAACc,OAAO,EAAE0B,UAAU,EAAEnC,KAAK,CAACoC,UAAU,CAAC,CAAClB,IAAI,CAAC,GACzDiB,UAAU,CAACjB,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiB,UAAUA,CAACjB,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKpB,KAAK,CAACuC,GAAG,IAAIzC,kBAAkB,CAACsB,IAAI,CAAC,EAAE;MAClDT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAAC6B,eAAe,CAAC;MACnC,OAAOlB,IAAI,CAAC2B,SAAS,GACjB7C,EAAE,CAACyB,IAAI,CAAC,GACRT,OAAO,CAAC8B,KAAK,CAACtC,mBAAmB,EAAEuC,cAAc,EAAEC,KAAK,CAAC,CAACvB,IAAI,CAAC;IACrE;IAEAT,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAAC0C,mBAAmB,CAAC;IACxCjC,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAAC2C,WAAW,EAAE;MAACC,WAAW,EAAE7C,SAAS,CAAC8C;IAAiB,CAAC,CAAC;IAC5E,OAAOC,IAAI,CAAC5B,IAAI,CAAC;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS4B,IAAIA,CAAC5B,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKpB,KAAK,CAACuC,GAAG,IAAIzC,kBAAkB,CAACsB,IAAI,CAAC,EAAE;MAClDT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAAC2C,WAAW,CAAC;MAC/BlC,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAAC0C,mBAAmB,CAAC;MACvC,OAAOP,UAAU,CAACjB,IAAI,CAAC;IACzB;IAEA,IAAIrB,aAAa,CAACqB,IAAI,CAAC,EAAE;MACvBT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAAC2C,WAAW,CAAC;MAC/BlC,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAAC0C,mBAAmB,CAAC;MACvC,OAAO/C,YAAY,CAACc,OAAO,EAAEsC,UAAU,EAAE/C,KAAK,CAACoC,UAAU,CAAC,CAAClB,IAAI,CAAC;IAClE;IAEA,IAAIA,IAAI,KAAKpB,KAAK,CAACsB,WAAW,IAAIF,IAAI,KAAKF,MAAM,EAAE;MACjD,OAAON,GAAG,CAACQ,IAAI,CAAC;IAClB;IAEAT,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrB,OAAO4B,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,UAAUA,CAAC7B,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKpB,KAAK,CAACuC,GAAG,IAAIzC,kBAAkB,CAACsB,IAAI,CAAC,EAAE;MAClD,OAAOiB,UAAU,CAACjB,IAAI,CAAC;IACzB;IAEAT,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAACgD,mBAAmB,CAAC;IACxCvC,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAAC2C,WAAW,EAAE;MAACC,WAAW,EAAE7C,SAAS,CAAC8C;IAAiB,CAAC,CAAC;IAC5E,OAAOI,IAAI,CAAC/B,IAAI,CAAC;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS+B,IAAIA,CAAC/B,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKpB,KAAK,CAACuC,GAAG,IAAIzC,kBAAkB,CAACsB,IAAI,CAAC,EAAE;MAClDT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAAC2C,WAAW,CAAC;MAC/BlC,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAACgD,mBAAmB,CAAC;MACvC,OAAOb,UAAU,CAACjB,IAAI,CAAC;IACzB;IAEA,IAAIA,IAAI,KAAKpB,KAAK,CAACsB,WAAW,IAAIF,IAAI,KAAKF,MAAM,EAAE;MACjD,OAAON,GAAG,CAACQ,IAAI,CAAC;IAClB;IAEAT,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrB,OAAO+B,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAST,cAAcA,CAACtB,IAAI,EAAE;IAC5BxB,MAAM,CAACE,kBAAkB,CAACsB,IAAI,CAAC,EAAE,cAAc,CAAC;IAChD,OAAOT,OAAO,CAACyC,OAAO,CAACtC,UAAU,EAAE6B,KAAK,EAAEU,aAAa,CAAC,CAACjC,IAAI,CAAC;EAChE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiC,aAAaA,CAACjC,IAAI,EAAE;IAC3BxB,MAAM,CAACE,kBAAkB,CAACsB,IAAI,CAAC,EAAE,cAAc,CAAC;IAChDT,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAACoD,UAAU,CAAC;IAC/B3C,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrBT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAACoD,UAAU,CAAC;IAC9B,OAAOC,YAAY;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,YAAYA,CAACnC,IAAI,EAAE;IAC1B,OAAOJ,aAAa,GAAG,CAAC,IAAIjB,aAAa,CAACqB,IAAI,CAAC,GAC3CvB,YAAY,CACVc,OAAO,EACP6C,kBAAkB,EAClBtD,KAAK,CAAC0B,UAAU,EAChBZ,aAAa,GAAG,CAClB,CAAC,CAACI,IAAI,CAAC,GACPoC,kBAAkB,CAACpC,IAAI,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASoC,kBAAkBA,CAACpC,IAAI,EAAE;IAChC,IAAIA,IAAI,KAAKpB,KAAK,CAACuC,GAAG,IAAIzC,kBAAkB,CAACsB,IAAI,CAAC,EAAE;MAClD,OAAOT,OAAO,CAAC8B,KAAK,CAACtC,mBAAmB,EAAEuC,cAAc,EAAEC,KAAK,CAAC,CAACvB,IAAI,CAAC;IACxE;IAEAT,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAACuD,aAAa,CAAC;IAClC,OAAOC,YAAY,CAACtC,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsC,YAAYA,CAACtC,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAKpB,KAAK,CAACuC,GAAG,IAAIzC,kBAAkB,CAACsB,IAAI,CAAC,EAAE;MAClDT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAACuD,aAAa,CAAC;MACjC,OAAOD,kBAAkB,CAACpC,IAAI,CAAC;IACjC;IAEAT,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrB,OAAOsC,YAAY;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASf,KAAKA,CAACvB,IAAI,EAAE;IACnBT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAACK,UAAU,CAAC;IAC9B,OAAOZ,EAAE,CAACyB,IAAI,CAAC;EACjB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASL,kBAAkBA,CAACJ,OAAO,EAAEhB,EAAE,EAAEiB,GAAG,EAAE;IAC5C,IAAI+C,IAAI,GAAG,CAAC;IAEZ,OAAOC,WAAW;;IAElB;AACJ;AACA;AACA;AACA;IACI,SAASA,WAAWA,CAACxC,IAAI,EAAE;MACzBxB,MAAM,CAACE,kBAAkB,CAACsB,IAAI,CAAC,EAAE,cAAc,CAAC;MAChDT,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAACoD,UAAU,CAAC;MAC/B3C,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;MACrBT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAACoD,UAAU,CAAC;MAC9B,OAAOnC,KAAK;IACd;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASA,KAAKA,CAACC,IAAI,EAAE;MACnB;MACAxB,MAAM,CACJiB,IAAI,CAACgD,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCACF,CAAC;;MAED;MACArD,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAAC6B,eAAe,CAAC;MACpC,OAAOhC,aAAa,CAACqB,IAAI,CAAC,GACtBvB,YAAY,CACVc,OAAO,EACPsD,mBAAmB,EACnB/D,KAAK,CAAC0B,UAAU,EAChBf,IAAI,CAACgD,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC,GACxDC,SAAS,GACTlE,SAAS,CAACmE,OAChB,CAAC,CAAChD,IAAI,CAAC,GACP6C,mBAAmB,CAAC7C,IAAI,CAAC;IAC/B;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAAS6C,mBAAmBA,CAAC7C,IAAI,EAAE;MACjC,IAAIA,IAAI,KAAKF,MAAM,EAAE;QACnBP,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAAC8B,uBAAuB,CAAC;QAC5C,OAAOqC,aAAa,CAACjD,IAAI,CAAC;MAC5B;MAEA,OAAOR,GAAG,CAACQ,IAAI,CAAC;IAClB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASiD,aAAaA,CAACjD,IAAI,EAAE;MAC3B,IAAIA,IAAI,KAAKF,MAAM,EAAE;QACnByC,IAAI,EAAE;QACNhD,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;QACrB,OAAOiD,aAAa;MACtB;MAEA,IAAIV,IAAI,IAAI1C,QAAQ,EAAE;QACpBN,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAAC8B,uBAAuB,CAAC;QAC3C,OAAOjC,aAAa,CAACqB,IAAI,CAAC,GACtBvB,YAAY,CAACc,OAAO,EAAE2D,kBAAkB,EAAEpE,KAAK,CAACoC,UAAU,CAAC,CAAClB,IAAI,CAAC,GACjEkD,kBAAkB,CAAClD,IAAI,CAAC;MAC9B;MAEA,OAAOR,GAAG,CAACQ,IAAI,CAAC;IAClB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASkD,kBAAkBA,CAAClD,IAAI,EAAE;MAChC,IAAIA,IAAI,KAAKpB,KAAK,CAACuC,GAAG,IAAIzC,kBAAkB,CAACsB,IAAI,CAAC,EAAE;QAClDT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAAC6B,eAAe,CAAC;QACnC,OAAOpC,EAAE,CAACyB,IAAI,CAAC;MACjB;MAEA,OAAOR,GAAG,CAACQ,IAAI,CAAC;IAClB;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASd,2BAA2BA,CAACK,OAAO,EAAEhB,EAAE,EAAEiB,GAAG,EAAE;EACrD,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOM,KAAK;;EAEZ;AACF;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAKpB,KAAK,CAACuC,GAAG,EAAE;MACtB,OAAO3B,GAAG,CAACQ,IAAI,CAAC;IAClB;IAEAxB,MAAM,CAACE,kBAAkB,CAACsB,IAAI,CAAC,EAAE,cAAc,CAAC;IAChDT,OAAO,CAACmB,KAAK,CAAC5B,KAAK,CAACoD,UAAU,CAAC;IAC/B3C,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrBT,OAAO,CAACyB,IAAI,CAAClC,KAAK,CAACoD,UAAU,CAAC;IAC9B,OAAOiB,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASA,SAASA,CAACnD,IAAI,EAAE;IACvB,OAAOP,IAAI,CAACgD,MAAM,CAACW,IAAI,CAAC3D,IAAI,CAAC4D,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG9D,GAAG,CAACQ,IAAI,CAAC,GAAGzB,EAAE,CAACyB,IAAI,CAAC;EACjE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}