{"ast": null, "code": "/**\n * @typedef {import('trough').Pipeline} Pipeline\n *\n * @typedef {import('unist').Node} Node\n *\n * @typedef {import('vfile').Compatible} Compatible\n * @typedef {import('vfile').Value} Value\n *\n * @typedef {import('../index.js').CompileResultMap} CompileResultMap\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Settings} Settings\n */\n\n/**\n * @typedef {CompileResultMap[keyof CompileResultMap]} CompileResults\n *   Acceptable results from compilers.\n *\n *   To register custom results, add them to\n *   {@linkcode CompileResultMap}.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the compiler receives (default: `Node`).\n * @template {CompileResults} [Result=CompileResults]\n *   The thing that the compiler yields (default: `CompileResults`).\n * @callback Compiler\n *   A **compiler** handles the compiling of a syntax tree to something else\n *   (in most cases, text) (TypeScript type).\n *\n *   It is used in the stringify phase and called with a {@linkcode Node}\n *   and {@linkcode VFile} representation of the document to compile.\n *   It should return the textual representation of the given tree (typically\n *   `string`).\n *\n *   > **Note**: unified typically compiles by serializing: most compilers\n *   > return `string` (or `Uint8Array`).\n *   > Some compilers, such as the one configured with\n *   > [`rehype-react`][rehype-react], return other values (in this case, a\n *   > React tree).\n *   > If you’re using a compiler that doesn’t serialize, expect different\n *   > result values.\n *   >\n *   > To register custom results in TypeScript, add them to\n *   > {@linkcode CompileResultMap}.\n *\n *   [rehype-react]: https://github.com/rehypejs/rehype-react\n * @param {Tree} tree\n *   Tree to compile.\n * @param {VFile} file\n *   File associated with `tree`.\n * @returns {Result}\n *   New content: compiled text (`string` or `Uint8Array`, for `file.value`) or\n *   something else (for `file.result`).\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the parser yields (default: `Node`)\n * @callback Parser\n *   A **parser** handles the parsing of text to a syntax tree.\n *\n *   It is used in the parse phase and is called with a `string` and\n *   {@linkcode VFile} of the document to parse.\n *   It must return the syntax tree representation of the given file\n *   ({@linkcode Node}).\n * @param {string} document\n *   Document to parse.\n * @param {VFile} file\n *   File associated with `document`.\n * @returns {Tree}\n *   Node representing the given file.\n */\n\n/**\n * @typedef {(\n *   Plugin<Array<any>, any, any> |\n *   PluginTuple<Array<any>, any, any> |\n *   Preset\n * )} Pluggable\n *   Union of the different ways to add plugins and settings.\n */\n\n/**\n * @typedef {Array<Pluggable>} PluggableList\n *   List of plugins and presets.\n */\n\n// Note: we can’t use `callback` yet as it messes up `this`:\n//  <https://github.com/microsoft/TypeScript/issues/55197>.\n/**\n * @template {Array<unknown>} [PluginParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=Node]\n *   Value that is expected as input (default: `Node`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=Input]\n *   Value that is yielded as output (default: `Input`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   (this: Processor, ...parameters: PluginParameters) =>\n *     Input extends string ? // Parser.\n *        Output extends Node | undefined ? undefined | void : never :\n *     Output extends CompileResults ? // Compiler.\n *        Input extends Node | undefined ? undefined | void : never :\n *     Transformer<\n *       Input extends Node ? Input : Node,\n *       Output extends Node ? Output : Node\n *     > | undefined | void\n * )} Plugin\n *   Single plugin.\n *\n *   Plugins configure the processors they are applied on in the following\n *   ways:\n *\n *   *   they change the processor, such as the parser, the compiler, or by\n *       configuring data\n *   *   they specify how to handle trees and files\n *\n *   In practice, they are functions that can receive options and configure the\n *   processor (`this`).\n *\n *   > **Note**: plugins are called when the processor is *frozen*, not when\n *   > they are applied.\n */\n\n/**\n * Tuple of a plugin and its configuration.\n *\n * The first item is a plugin, the rest are its parameters.\n *\n * @template {Array<unknown>} [TupleParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=undefined]\n *   Value that is expected as input (optional).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=undefined] (optional).\n *   Value that is yielded as output.\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   [\n *     plugin: Plugin<TupleParameters, Input, Output>,\n *     ...parameters: TupleParameters\n *   ]\n * )} PluginTuple\n */\n\n/**\n * @typedef Preset\n *   Sharable configuration.\n *\n *   They can contain plugins and settings.\n * @property {PluggableList | undefined} [plugins]\n *   List of plugins and presets (optional).\n * @property {Settings | undefined} [settings]\n *   Shared settings for parsers and compilers (optional).\n */\n\n/**\n * @template {VFile} [File=VFile]\n *   The file that the callback receives (default: `VFile`).\n * @callback ProcessCallback\n *   Callback called when the process is done.\n *\n *   Called with either an error or a result.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {File | undefined} [file]\n *   Processed file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The tree that the callback receives (default: `Node`).\n * @callback RunCallback\n *   Callback called when transformers are done.\n *\n *   Called with either an error or results.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {Tree | undefined} [tree]\n *   Transformed tree (optional).\n * @param {VFile | undefined} [file]\n *   File (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Output=Node]\n *   Node type that the transformer yields (default: `Node`).\n * @callback TransformCallback\n *   Callback passed to transforms.\n *\n *   If the signature of a `transformer` accepts a third argument, the\n *   transformer may perform asynchronous operations, and must call it.\n * @param {Error | undefined} [error]\n *   Fatal error to stop the process (optional).\n * @param {Output | undefined} [tree]\n *   New, changed, tree (optional).\n * @param {VFile | undefined} [file]\n *   New, changed, file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Input=Node]\n *   Node type that the transformer expects (default: `Node`).\n * @template {Node} [Output=Input]\n *   Node type that the transformer yields (default: `Input`).\n * @callback Transformer\n *   Transformers handle syntax trees and files.\n *\n *   They are functions that are called each time a syntax tree and file are\n *   passed through the run phase.\n *   When an error occurs in them (either because it’s thrown, returned,\n *   rejected, or passed to `next`), the process stops.\n *\n *   The run phase is handled by [`trough`][trough], see its documentation for\n *   the exact semantics of these functions.\n *\n *   > **Note**: you should likely ignore `next`: don’t accept it.\n *   > it supports callback-style async work.\n *   > But promises are likely easier to reason about.\n *\n *   [trough]: https://github.com/wooorm/trough#function-fninput-next\n * @param {Input} tree\n *   Tree to handle.\n * @param {VFile} file\n *   File to handle.\n * @param {TransformCallback<Output>} next\n *   Callback.\n * @returns {(\n *   Promise<Output | undefined | void> |\n *   Promise<never> | // For some reason this is needed separately.\n *   Output |\n *   Error |\n *   undefined |\n *   void\n * )}\n *   If you accept `next`, nothing.\n *   Otherwise:\n *\n *   *   `Error` — fatal error to stop the process\n *   *   `Promise<undefined>` or `undefined` — the next transformer keeps using\n *       same tree\n *   *   `Promise<Node>` or `Node` — new, changed, tree\n */\n\n/**\n * @template {Node | undefined} ParseTree\n *   Output of `parse`.\n * @template {Node | undefined} HeadTree\n *   Input for `run`.\n * @template {Node | undefined} TailTree\n *   Output for `run`.\n * @template {Node | undefined} CompileTree\n *   Input of `stringify`.\n * @template {CompileResults | undefined} CompileResult\n *   Output of `stringify`.\n * @template {Node | string | undefined} Input\n *   Input of plugin.\n * @template Output\n *   Output of plugin (optional).\n * @typedef {(\n *   Input extends string\n *     ? Output extends Node | undefined\n *       ? // Parser.\n *         Processor<\n *           Output extends undefined ? ParseTree : Output,\n *           HeadTree,\n *           TailTree,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Output extends CompileResults\n *     ? Input extends Node | undefined\n *       ? // Compiler.\n *         Processor<\n *           ParseTree,\n *           HeadTree,\n *           TailTree,\n *           Input extends undefined ? CompileTree : Input,\n *           Output extends undefined ? CompileResult : Output\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Input extends Node | undefined\n *     ? Output extends Node | undefined\n *       ? // Transform.\n *         Processor<\n *           ParseTree,\n *           HeadTree extends undefined ? Input : HeadTree,\n *           Output extends undefined ? TailTree : Output,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : // Unknown.\n *       Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n * )} UsePlugin\n *   Create a processor based on the input/output of a {@link Plugin plugin}.\n */\n\n/**\n * @template {CompileResults | undefined} Result\n *   Node type that the transformer yields.\n * @typedef {(\n *   Result extends Value | undefined ?\n *     VFile :\n *     VFile & {result: Result}\n *   )} VFileWithOutput\n *   Type to generate a {@linkcode VFile} corresponding to a compiler result.\n *\n *   If a result that is not acceptable on a `VFile` is used, that will\n *   be stored on the `result` field of {@linkcode VFile}.\n */\n\nimport { bail } from 'bail';\nimport extend from 'extend';\nimport { ok as assert } from 'devlop';\nimport isPlainObj from 'is-plain-obj';\nimport { trough } from 'trough';\nimport { VFile } from 'vfile';\nimport { CallableInstance } from './callable-instance.js';\n\n// To do: next major: drop `Compiler`, `Parser`: prefer lowercase.\n\n// To do: we could start yielding `never` in TS when a parser is missing and\n// `parse` is called.\n// Currently, we allow directly setting `processor.parser`, which is untyped.\n\nconst own = {}.hasOwnProperty;\n\n/**\n * @template {Node | undefined} [ParseTree=undefined]\n *   Output of `parse` (optional).\n * @template {Node | undefined} [HeadTree=undefined]\n *   Input for `run` (optional).\n * @template {Node | undefined} [TailTree=undefined]\n *   Output for `run` (optional).\n * @template {Node | undefined} [CompileTree=undefined]\n *   Input of `stringify` (optional).\n * @template {CompileResults | undefined} [CompileResult=undefined]\n *   Output of `stringify` (optional).\n * @extends {CallableInstance<[], Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>>}\n */\nexport class Processor extends CallableInstance {\n  /**\n   * Create a processor.\n   */\n  constructor() {\n    // If `Processor()` is called (w/o new), `copy` is called instead.\n    super('copy');\n\n    /**\n     * Compiler to use (deprecated).\n     *\n     * @deprecated\n     *   Use `compiler` instead.\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.Compiler = undefined;\n\n    /**\n     * Parser to use (deprecated).\n     *\n     * @deprecated\n     *   Use `parser` instead.\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.Parser = undefined;\n\n    // Note: the following fields are considered private.\n    // However, they are needed for tests, and TSC generates an untyped\n    // `private freezeIndex` field for, which trips `type-coverage` up.\n    // Instead, we use `@deprecated` to visualize that they shouldn’t be used.\n    /**\n     * Internal list of configured plugins.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Array<PluginTuple<Array<unknown>>>}\n     */\n    this.attachers = [];\n\n    /**\n     * Compiler to use.\n     *\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.compiler = undefined;\n\n    /**\n     * Internal state to track where we are while freezing.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {number}\n     */\n    this.freezeIndex = -1;\n\n    /**\n     * Internal state to track whether we’re frozen.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {boolean | undefined}\n     */\n    this.frozen = undefined;\n\n    /**\n     * Internal state.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Data}\n     */\n    this.namespace = {};\n\n    /**\n     * Parser to use.\n     *\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.parser = undefined;\n\n    /**\n     * Internal list of configured transformers.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Pipeline}\n     */\n    this.transformers = trough();\n  }\n\n  /**\n   * Copy a processor.\n   *\n   * @deprecated\n   *   This is a private internal method and should not be used.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   New *unfrozen* processor ({@linkcode Processor}) that is\n   *   configured to work the same as its ancestor.\n   *   When the descendant processor is configured in the future it does not\n   *   affect the ancestral processor.\n   */\n  copy() {\n    // Cast as the type parameters will be the same after attaching.\n    const destination = /** @type {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>} */\n    new Processor();\n    let index = -1;\n    while (++index < this.attachers.length) {\n      const attacher = this.attachers[index];\n      destination.use(...attacher);\n    }\n    destination.data(extend(true, {}, this.namespace));\n    return destination;\n  }\n\n  /**\n   * Configure the processor with info available to all plugins.\n   * Information is stored in an object.\n   *\n   * Typically, options can be given to a specific plugin, but sometimes it\n   * makes sense to have information shared with several plugins.\n   * For example, a list of HTML elements that are self-closing, which is\n   * needed during all phases.\n   *\n   * > **Note**: setting information cannot occur on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * > **Note**: to register custom data in TypeScript, augment the\n   * > {@linkcode Data} interface.\n   *\n   * @example\n   *   This example show how to get and set info:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   const processor = unified().data('alpha', 'bravo')\n   *\n   *   processor.data('alpha') // => 'bravo'\n   *\n   *   processor.data() // => {alpha: 'bravo'}\n   *\n   *   processor.data({charlie: 'delta'})\n   *\n   *   processor.data() // => {charlie: 'delta'}\n   *   ```\n   *\n   * @template {keyof Data} Key\n   *\n   * @overload\n   * @returns {Data}\n   *\n   * @overload\n   * @param {Data} dataset\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Key} key\n   * @returns {Data[Key]}\n   *\n   * @overload\n   * @param {Key} key\n   * @param {Data[Key]} value\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @param {Data | Key} [key]\n   *   Key to get or set, or entire dataset to set, or nothing to get the\n   *   entire dataset (optional).\n   * @param {Data[Key]} [value]\n   *   Value to set (optional).\n   * @returns {unknown}\n   *   The current processor when setting, the value at `key` when getting, or\n   *   the entire dataset when getting without key.\n   */\n  data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', this.frozen);\n        this.namespace[key] = value;\n        return this;\n      }\n\n      // Get `key`.\n      return own.call(this.namespace, key) && this.namespace[key] || undefined;\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', this.frozen);\n      this.namespace = key;\n      return this;\n    }\n\n    // Get space.\n    return this.namespace;\n  }\n\n  /**\n   * Freeze a processor.\n   *\n   * Frozen processors are meant to be extended and not to be configured\n   * directly.\n   *\n   * When a processor is frozen it cannot be unfrozen.\n   * New processors working the same way can be created by calling the\n   * processor.\n   *\n   * It’s possible to freeze processors explicitly by calling `.freeze()`.\n   * Processors freeze automatically when `.parse()`, `.run()`, `.runSync()`,\n   * `.stringify()`, `.process()`, or `.processSync()` are called.\n   *\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   The current processor.\n   */\n  freeze() {\n    if (this.frozen) {\n      return this;\n    }\n\n    // Cast so that we can type plugins easier.\n    // Plugins are supposed to be usable on different processors, not just on\n    // this exact processor.\n    const self = /** @type {Processor} */ /** @type {unknown} */this;\n    while (++this.freezeIndex < this.attachers.length) {\n      const [attacher, ...options] = this.attachers[this.freezeIndex];\n      if (options[0] === false) {\n        continue;\n      }\n      if (options[0] === true) {\n        options[0] = undefined;\n      }\n      const transformer = attacher.call(self, ...options);\n      if (typeof transformer === 'function') {\n        this.transformers.use(transformer);\n      }\n    }\n    this.frozen = true;\n    this.freezeIndex = Number.POSITIVE_INFINITY;\n    return this;\n  }\n\n  /**\n   * Parse text to a syntax tree.\n   *\n   * > **Note**: `parse` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `parse` performs the parse phase, not the run phase or other\n   * > phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   file to parse (optional); typically `string` or `VFile`; any value\n   *   accepted as `x` in `new VFile(x)`.\n   * @returns {ParseTree extends undefined ? Node : ParseTree}\n   *   Syntax tree representing `file`.\n   */\n  parse(file) {\n    this.freeze();\n    const realFile = vfile(file);\n    const parser = this.parser || this.Parser;\n    assertParser('parse', parser);\n    return parser(String(realFile), realFile);\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * > **Note**: `process` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `process` performs the parse, run, and stringify phases.\n   *\n   * @overload\n   * @param {Compatible | undefined} file\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<VFileWithOutput<CompileResult>>}\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`]; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>> | undefined} [done]\n   *   Callback (optional).\n   * @returns {Promise<VFile> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise a promise, rejected with a fatal error or resolved with the\n   *   processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  process(file, done) {\n    const self = this;\n    this.freeze();\n    assertParser('process', this.parser || this.Parser);\n    assertCompiler('process', this.compiler || this.Compiler);\n    return done ? executor(undefined, done) : new Promise(executor);\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {((file: VFileWithOutput<CompileResult>) => undefined | void) | undefined} resolve\n     * @param {(error: Error | undefined) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      const realFile = vfile(file);\n      // Assume `ParseTree` (the result of the parser) matches `HeadTree` (the\n      // input of the first transform).\n      const parseTree = /** @type {HeadTree extends undefined ? Node : HeadTree} */\n      /** @type {unknown} */self.parse(realFile);\n      self.run(parseTree, realFile, function (error, tree, file) {\n        if (error || !tree || !file) {\n          return realDone(error);\n        }\n\n        // Assume `TailTree` (the output of the last transform) matches\n        // `CompileTree` (the input of the compiler).\n        const compileTree = /** @type {CompileTree extends undefined ? Node : CompileTree} */\n        /** @type {unknown} */tree;\n        const compileResult = self.stringify(compileTree, file);\n        if (looksLikeAValue(compileResult)) {\n          file.value = compileResult;\n        } else {\n          file.result = compileResult;\n        }\n        realDone(error, /** @type {VFileWithOutput<CompileResult>} */file);\n      });\n\n      /**\n       * @param {Error | undefined} error\n       * @param {VFileWithOutput<CompileResult> | undefined} [file]\n       * @returns {undefined}\n       */\n      function realDone(error, file) {\n        if (error || !file) {\n          reject(error);\n        } else if (resolve) {\n          resolve(file);\n        } else {\n          assert(done, '`done` is defined if `resolve` is not');\n          done(undefined, file);\n        }\n      }\n    }\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `processSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `processSync` performs the parse, run, and stringify phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @returns {VFileWithOutput<CompileResult>}\n   *   The processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  processSync(file) {\n    /** @type {boolean} */\n    let complete = false;\n    /** @type {VFileWithOutput<CompileResult> | undefined} */\n    let result;\n    this.freeze();\n    assertParser('processSync', this.parser || this.Parser);\n    assertCompiler('processSync', this.compiler || this.Compiler);\n    this.process(file, realDone);\n    assertDone('processSync', 'process', complete);\n    assert(result, 'we either bailed on an error or have a tree');\n    return result;\n\n    /**\n     * @type {ProcessCallback<VFileWithOutput<CompileResult>>}\n     */\n    function realDone(error, file) {\n      complete = true;\n      bail(error);\n      result = file;\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * > **Note**: `run` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `run` performs the run phase, not other phases.\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} file\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree>}\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {(\n   *   RunCallback<TailTree extends undefined ? Node : TailTree> |\n   *   Compatible\n   * )} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} [done]\n   *   Callback (optional).\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise, a promise rejected with a fatal error or resolved with the\n   *   transformed tree.\n   */\n  run(tree, file, done) {\n    assertNode(tree);\n    this.freeze();\n    const transformers = this.transformers;\n    if (!done && typeof file === 'function') {\n      done = file;\n      file = undefined;\n    }\n    return done ? executor(undefined, done) : new Promise(executor);\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {(\n     *   ((tree: TailTree extends undefined ? Node : TailTree) => undefined | void) |\n     *   undefined\n     * )} resolve\n     * @param {(error: Error) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      assert(typeof file !== 'function', '`file` can’t be a `done` anymore, we checked');\n      const realFile = vfile(file);\n      transformers.run(tree, realFile, realDone);\n\n      /**\n       * @param {Error | undefined} error\n       * @param {Node} outputTree\n       * @param {VFile} file\n       * @returns {undefined}\n       */\n      function realDone(error, outputTree, file) {\n        const resultingTree = /** @type {TailTree extends undefined ? Node : TailTree} */\n        outputTree || tree;\n        if (error) {\n          reject(error);\n        } else if (resolve) {\n          resolve(resultingTree);\n        } else {\n          assert(done, '`done` is defined if `resolve` is not');\n          done(undefined, resultingTree, file);\n        }\n      }\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `runSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `runSync` performs the run phase, not other phases.\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {TailTree extends undefined ? Node : TailTree}\n   *   Transformed tree.\n   */\n  runSync(tree, file) {\n    /** @type {boolean} */\n    let complete = false;\n    /** @type {(TailTree extends undefined ? Node : TailTree) | undefined} */\n    let result;\n    this.run(tree, file, realDone);\n    assertDone('runSync', 'run', complete);\n    assert(result, 'we either bailed on an error or have a tree');\n    return result;\n\n    /**\n     * @type {RunCallback<TailTree extends undefined ? Node : TailTree>}\n     */\n    function realDone(error, tree) {\n      bail(error);\n      result = tree;\n      complete = true;\n    }\n  }\n\n  /**\n   * Compile a syntax tree.\n   *\n   * > **Note**: `stringify` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `stringify` performs the stringify phase, not the run phase\n   * > or other phases.\n   *\n   * @param {CompileTree extends undefined ? Node : CompileTree} tree\n   *   Tree to compile.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {CompileResult extends undefined ? Value : CompileResult}\n   *   Textual representation of the tree (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most compilers\n   *   > return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  stringify(tree, file) {\n    this.freeze();\n    const realFile = vfile(file);\n    const compiler = this.compiler || this.Compiler;\n    assertCompiler('stringify', compiler);\n    assertNode(tree);\n    return compiler(tree, realFile);\n  }\n\n  /**\n   * Configure the processor to use a plugin, a list of usable values, or a\n   * preset.\n   *\n   * If the processor is already using a plugin, the previous plugin\n   * configuration is changed based on the options that are passed in.\n   * In other words, the plugin is not added a second time.\n   *\n   * > **Note**: `use` cannot be called on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * @example\n   *   There are many ways to pass plugins to `.use()`.\n   *   This example gives an overview:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   unified()\n   *     // Plugin with options:\n   *     .use(pluginA, {x: true, y: true})\n   *     // Passing the same plugin again merges configuration (to `{x: true, y: false, z: true}`):\n   *     .use(pluginA, {y: false, z: true})\n   *     // Plugins:\n   *     .use([pluginB, pluginC])\n   *     // Two plugins, the second with options:\n   *     .use([pluginD, [pluginE, {}]])\n   *     // Preset with plugins and settings:\n   *     .use({plugins: [pluginF, [pluginG, {}]], settings: {position: false}})\n   *     // Settings only:\n   *     .use({settings: {position: false}})\n   *   ```\n   *\n   * @template {Array<unknown>} [Parameters=[]]\n   * @template {Node | string | undefined} [Input=undefined]\n   * @template [Output=Input]\n   *\n   * @overload\n   * @param {Preset | null | undefined} [preset]\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {PluggableList} list\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Plugin<Parameters, Input, Output>} plugin\n   * @param {...(Parameters | [boolean])} parameters\n   * @returns {UsePlugin<ParseTree, HeadTree, TailTree, CompileTree, CompileResult, Input, Output>}\n   *\n   * @param {PluggableList | Plugin | Preset | null | undefined} value\n   *   Usable value.\n   * @param {...unknown} parameters\n   *   Parameters, when a plugin is given as a usable value.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   Current processor.\n   */\n  use(value, ...parameters) {\n    const attachers = this.attachers;\n    const namespace = this.namespace;\n    assertUnfrozen('use', this.frozen);\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, parameters);\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value);\n      } else {\n        addPreset(value);\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`');\n    }\n    return this;\n\n    /**\n     * @param {Pluggable} value\n     * @returns {undefined}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value, []);\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...parameters] = /** @type {PluginTuple<Array<unknown>>} */value;\n          addPlugin(plugin, parameters);\n        } else {\n          addPreset(value);\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`');\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {undefined}\n     */\n    function addPreset(result) {\n      if (!('plugins' in result) && !('settings' in result)) {\n        throw new Error('Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither');\n      }\n      addList(result.plugins);\n      if (result.settings) {\n        namespace.settings = extend(true, namespace.settings, result.settings);\n      }\n    }\n\n    /**\n     * @param {PluggableList | null | undefined} plugins\n     * @returns {undefined}\n     */\n    function addList(plugins) {\n      let index = -1;\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index];\n          add(thing);\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`');\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {Array<unknown>} parameters\n     * @returns {undefined}\n     */\n    function addPlugin(plugin, parameters) {\n      let index = -1;\n      let entryIndex = -1;\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entryIndex = index;\n          break;\n        }\n      }\n      if (entryIndex === -1) {\n        attachers.push([plugin, ...parameters]);\n      }\n      // Only set if there was at least a `primary` value, otherwise we’d change\n      // `arguments.length`.\n      else if (parameters.length > 0) {\n        let [primary, ...rest] = parameters;\n        const currentPrimary = attachers[entryIndex][1];\n        if (isPlainObj(currentPrimary) && isPlainObj(primary)) {\n          primary = extend(true, currentPrimary, primary);\n        }\n        attachers[entryIndex] = [plugin, primary, ...rest];\n      }\n    }\n  }\n}\n\n// Note: this returns a *callable* instance.\n// That’s why it’s documented as a function.\n/**\n * Create a new processor.\n *\n * @example\n *   This example shows how a new processor can be created (from `remark`) and linked\n *   to **stdin**(4) and **stdout**(4).\n *\n *   ```js\n *   import process from 'node:process'\n *   import concatStream from 'concat-stream'\n *   import {remark} from 'remark'\n *\n *   process.stdin.pipe(\n *     concatStream(function (buf) {\n *       process.stdout.write(String(remark().processSync(buf)))\n *     })\n *   )\n *   ```\n *\n * @returns\n *   New *unfrozen* processor (`processor`).\n *\n *   This processor is configured to work the same as its ancestor.\n *   When the descendant processor is configured in the future it does not\n *   affect the ancestral processor.\n */\nexport const unified = new Processor().freeze();\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `parser`');\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `compiler`');\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error('Cannot call `' + name + '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.');\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!isPlainObj(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`');\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error('`' + name + '` finished async. Use `' + asyncName + '` instead');\n  }\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new VFile(value);\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(value && typeof value === 'object' && 'message' in value && 'messages' in value);\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is Value}\n */\nfunction looksLikeAValue(value) {\n  return typeof value === 'string' || isUint8Array(value);\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(value && typeof value === 'object' && 'byteLength' in value && 'byteOffset' in value);\n}", "map": {"version": 3, "names": ["bail", "extend", "ok", "assert", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trough", "VFile", "CallableInstance", "own", "hasOwnProperty", "Processor", "constructor", "Compiler", "undefined", "<PERSON><PERSON><PERSON>", "attachers", "compiler", "freezeIndex", "frozen", "namespace", "parser", "transformers", "copy", "destination", "index", "length", "attacher", "use", "data", "key", "value", "arguments", "assertUnfrozen", "call", "freeze", "self", "options", "transformer", "Number", "POSITIVE_INFINITY", "parse", "file", "realFile", "vfile", "assertParser", "String", "process", "done", "assertCompiler", "executor", "Promise", "resolve", "reject", "parseTree", "run", "error", "tree", "realDone", "compileTree", "compileResult", "stringify", "looksLikeAValue", "result", "processSync", "complete", "assertDone", "assertNode", "outputTree", "resultingTree", "runSync", "parameters", "addPlugin", "Array", "isArray", "addList", "addPreset", "TypeError", "add", "plugin", "Error", "plugins", "settings", "thing", "entryIndex", "push", "primary", "rest", "currentPrimary", "unified", "name", "node", "type", "asyncName", "looksLikeAVFile", "Boolean", "isUint8Array"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/unified/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('trough').Pipeline} Pipeline\n *\n * @typedef {import('unist').Node} Node\n *\n * @typedef {import('vfile').Compatible} Compatible\n * @typedef {import('vfile').Value} Value\n *\n * @typedef {import('../index.js').CompileResultMap} CompileResultMap\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Settings} Settings\n */\n\n/**\n * @typedef {CompileResultMap[keyof CompileResultMap]} CompileResults\n *   Acceptable results from compilers.\n *\n *   To register custom results, add them to\n *   {@linkcode CompileResultMap}.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the compiler receives (default: `Node`).\n * @template {CompileResults} [Result=CompileResults]\n *   The thing that the compiler yields (default: `CompileResults`).\n * @callback Compiler\n *   A **compiler** handles the compiling of a syntax tree to something else\n *   (in most cases, text) (TypeScript type).\n *\n *   It is used in the stringify phase and called with a {@linkcode Node}\n *   and {@linkcode VFile} representation of the document to compile.\n *   It should return the textual representation of the given tree (typically\n *   `string`).\n *\n *   > **Note**: unified typically compiles by serializing: most compilers\n *   > return `string` (or `Uint8Array`).\n *   > Some compilers, such as the one configured with\n *   > [`rehype-react`][rehype-react], return other values (in this case, a\n *   > React tree).\n *   > If you’re using a compiler that doesn’t serialize, expect different\n *   > result values.\n *   >\n *   > To register custom results in TypeScript, add them to\n *   > {@linkcode CompileResultMap}.\n *\n *   [rehype-react]: https://github.com/rehypejs/rehype-react\n * @param {Tree} tree\n *   Tree to compile.\n * @param {VFile} file\n *   File associated with `tree`.\n * @returns {Result}\n *   New content: compiled text (`string` or `Uint8Array`, for `file.value`) or\n *   something else (for `file.result`).\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the parser yields (default: `Node`)\n * @callback Parser\n *   A **parser** handles the parsing of text to a syntax tree.\n *\n *   It is used in the parse phase and is called with a `string` and\n *   {@linkcode VFile} of the document to parse.\n *   It must return the syntax tree representation of the given file\n *   ({@linkcode Node}).\n * @param {string} document\n *   Document to parse.\n * @param {VFile} file\n *   File associated with `document`.\n * @returns {Tree}\n *   Node representing the given file.\n */\n\n/**\n * @typedef {(\n *   Plugin<Array<any>, any, any> |\n *   PluginTuple<Array<any>, any, any> |\n *   Preset\n * )} Pluggable\n *   Union of the different ways to add plugins and settings.\n */\n\n/**\n * @typedef {Array<Pluggable>} PluggableList\n *   List of plugins and presets.\n */\n\n// Note: we can’t use `callback` yet as it messes up `this`:\n//  <https://github.com/microsoft/TypeScript/issues/55197>.\n/**\n * @template {Array<unknown>} [PluginParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=Node]\n *   Value that is expected as input (default: `Node`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=Input]\n *   Value that is yielded as output (default: `Input`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   (this: Processor, ...parameters: PluginParameters) =>\n *     Input extends string ? // Parser.\n *        Output extends Node | undefined ? undefined | void : never :\n *     Output extends CompileResults ? // Compiler.\n *        Input extends Node | undefined ? undefined | void : never :\n *     Transformer<\n *       Input extends Node ? Input : Node,\n *       Output extends Node ? Output : Node\n *     > | undefined | void\n * )} Plugin\n *   Single plugin.\n *\n *   Plugins configure the processors they are applied on in the following\n *   ways:\n *\n *   *   they change the processor, such as the parser, the compiler, or by\n *       configuring data\n *   *   they specify how to handle trees and files\n *\n *   In practice, they are functions that can receive options and configure the\n *   processor (`this`).\n *\n *   > **Note**: plugins are called when the processor is *frozen*, not when\n *   > they are applied.\n */\n\n/**\n * Tuple of a plugin and its configuration.\n *\n * The first item is a plugin, the rest are its parameters.\n *\n * @template {Array<unknown>} [TupleParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=undefined]\n *   Value that is expected as input (optional).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=undefined] (optional).\n *   Value that is yielded as output.\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   [\n *     plugin: Plugin<TupleParameters, Input, Output>,\n *     ...parameters: TupleParameters\n *   ]\n * )} PluginTuple\n */\n\n/**\n * @typedef Preset\n *   Sharable configuration.\n *\n *   They can contain plugins and settings.\n * @property {PluggableList | undefined} [plugins]\n *   List of plugins and presets (optional).\n * @property {Settings | undefined} [settings]\n *   Shared settings for parsers and compilers (optional).\n */\n\n/**\n * @template {VFile} [File=VFile]\n *   The file that the callback receives (default: `VFile`).\n * @callback ProcessCallback\n *   Callback called when the process is done.\n *\n *   Called with either an error or a result.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {File | undefined} [file]\n *   Processed file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The tree that the callback receives (default: `Node`).\n * @callback RunCallback\n *   Callback called when transformers are done.\n *\n *   Called with either an error or results.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {Tree | undefined} [tree]\n *   Transformed tree (optional).\n * @param {VFile | undefined} [file]\n *   File (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Output=Node]\n *   Node type that the transformer yields (default: `Node`).\n * @callback TransformCallback\n *   Callback passed to transforms.\n *\n *   If the signature of a `transformer` accepts a third argument, the\n *   transformer may perform asynchronous operations, and must call it.\n * @param {Error | undefined} [error]\n *   Fatal error to stop the process (optional).\n * @param {Output | undefined} [tree]\n *   New, changed, tree (optional).\n * @param {VFile | undefined} [file]\n *   New, changed, file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Input=Node]\n *   Node type that the transformer expects (default: `Node`).\n * @template {Node} [Output=Input]\n *   Node type that the transformer yields (default: `Input`).\n * @callback Transformer\n *   Transformers handle syntax trees and files.\n *\n *   They are functions that are called each time a syntax tree and file are\n *   passed through the run phase.\n *   When an error occurs in them (either because it’s thrown, returned,\n *   rejected, or passed to `next`), the process stops.\n *\n *   The run phase is handled by [`trough`][trough], see its documentation for\n *   the exact semantics of these functions.\n *\n *   > **Note**: you should likely ignore `next`: don’t accept it.\n *   > it supports callback-style async work.\n *   > But promises are likely easier to reason about.\n *\n *   [trough]: https://github.com/wooorm/trough#function-fninput-next\n * @param {Input} tree\n *   Tree to handle.\n * @param {VFile} file\n *   File to handle.\n * @param {TransformCallback<Output>} next\n *   Callback.\n * @returns {(\n *   Promise<Output | undefined | void> |\n *   Promise<never> | // For some reason this is needed separately.\n *   Output |\n *   Error |\n *   undefined |\n *   void\n * )}\n *   If you accept `next`, nothing.\n *   Otherwise:\n *\n *   *   `Error` — fatal error to stop the process\n *   *   `Promise<undefined>` or `undefined` — the next transformer keeps using\n *       same tree\n *   *   `Promise<Node>` or `Node` — new, changed, tree\n */\n\n/**\n * @template {Node | undefined} ParseTree\n *   Output of `parse`.\n * @template {Node | undefined} HeadTree\n *   Input for `run`.\n * @template {Node | undefined} TailTree\n *   Output for `run`.\n * @template {Node | undefined} CompileTree\n *   Input of `stringify`.\n * @template {CompileResults | undefined} CompileResult\n *   Output of `stringify`.\n * @template {Node | string | undefined} Input\n *   Input of plugin.\n * @template Output\n *   Output of plugin (optional).\n * @typedef {(\n *   Input extends string\n *     ? Output extends Node | undefined\n *       ? // Parser.\n *         Processor<\n *           Output extends undefined ? ParseTree : Output,\n *           HeadTree,\n *           TailTree,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Output extends CompileResults\n *     ? Input extends Node | undefined\n *       ? // Compiler.\n *         Processor<\n *           ParseTree,\n *           HeadTree,\n *           TailTree,\n *           Input extends undefined ? CompileTree : Input,\n *           Output extends undefined ? CompileResult : Output\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Input extends Node | undefined\n *     ? Output extends Node | undefined\n *       ? // Transform.\n *         Processor<\n *           ParseTree,\n *           HeadTree extends undefined ? Input : HeadTree,\n *           Output extends undefined ? TailTree : Output,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : // Unknown.\n *       Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n * )} UsePlugin\n *   Create a processor based on the input/output of a {@link Plugin plugin}.\n */\n\n/**\n * @template {CompileResults | undefined} Result\n *   Node type that the transformer yields.\n * @typedef {(\n *   Result extends Value | undefined ?\n *     VFile :\n *     VFile & {result: Result}\n *   )} VFileWithOutput\n *   Type to generate a {@linkcode VFile} corresponding to a compiler result.\n *\n *   If a result that is not acceptable on a `VFile` is used, that will\n *   be stored on the `result` field of {@linkcode VFile}.\n */\n\nimport {bail} from 'bail'\nimport extend from 'extend'\nimport {ok as assert} from 'devlop'\nimport isPlainObj from 'is-plain-obj'\nimport {trough} from 'trough'\nimport {VFile} from 'vfile'\nimport {CallableInstance} from './callable-instance.js'\n\n// To do: next major: drop `Compiler`, `Parser`: prefer lowercase.\n\n// To do: we could start yielding `never` in TS when a parser is missing and\n// `parse` is called.\n// Currently, we allow directly setting `processor.parser`, which is untyped.\n\nconst own = {}.hasOwnProperty\n\n/**\n * @template {Node | undefined} [ParseTree=undefined]\n *   Output of `parse` (optional).\n * @template {Node | undefined} [HeadTree=undefined]\n *   Input for `run` (optional).\n * @template {Node | undefined} [TailTree=undefined]\n *   Output for `run` (optional).\n * @template {Node | undefined} [CompileTree=undefined]\n *   Input of `stringify` (optional).\n * @template {CompileResults | undefined} [CompileResult=undefined]\n *   Output of `stringify` (optional).\n * @extends {CallableInstance<[], Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>>}\n */\nexport class Processor extends CallableInstance {\n  /**\n   * Create a processor.\n   */\n  constructor() {\n    // If `Processor()` is called (w/o new), `copy` is called instead.\n    super('copy')\n\n    /**\n     * Compiler to use (deprecated).\n     *\n     * @deprecated\n     *   Use `compiler` instead.\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.Compiler = undefined\n\n    /**\n     * Parser to use (deprecated).\n     *\n     * @deprecated\n     *   Use `parser` instead.\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.Parser = undefined\n\n    // Note: the following fields are considered private.\n    // However, they are needed for tests, and TSC generates an untyped\n    // `private freezeIndex` field for, which trips `type-coverage` up.\n    // Instead, we use `@deprecated` to visualize that they shouldn’t be used.\n    /**\n     * Internal list of configured plugins.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Array<PluginTuple<Array<unknown>>>}\n     */\n    this.attachers = []\n\n    /**\n     * Compiler to use.\n     *\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.compiler = undefined\n\n    /**\n     * Internal state to track where we are while freezing.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {number}\n     */\n    this.freezeIndex = -1\n\n    /**\n     * Internal state to track whether we’re frozen.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {boolean | undefined}\n     */\n    this.frozen = undefined\n\n    /**\n     * Internal state.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Data}\n     */\n    this.namespace = {}\n\n    /**\n     * Parser to use.\n     *\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.parser = undefined\n\n    /**\n     * Internal list of configured transformers.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Pipeline}\n     */\n    this.transformers = trough()\n  }\n\n  /**\n   * Copy a processor.\n   *\n   * @deprecated\n   *   This is a private internal method and should not be used.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   New *unfrozen* processor ({@linkcode Processor}) that is\n   *   configured to work the same as its ancestor.\n   *   When the descendant processor is configured in the future it does not\n   *   affect the ancestral processor.\n   */\n  copy() {\n    // Cast as the type parameters will be the same after attaching.\n    const destination =\n      /** @type {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>} */ (\n        new Processor()\n      )\n    let index = -1\n\n    while (++index < this.attachers.length) {\n      const attacher = this.attachers[index]\n      destination.use(...attacher)\n    }\n\n    destination.data(extend(true, {}, this.namespace))\n\n    return destination\n  }\n\n  /**\n   * Configure the processor with info available to all plugins.\n   * Information is stored in an object.\n   *\n   * Typically, options can be given to a specific plugin, but sometimes it\n   * makes sense to have information shared with several plugins.\n   * For example, a list of HTML elements that are self-closing, which is\n   * needed during all phases.\n   *\n   * > **Note**: setting information cannot occur on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * > **Note**: to register custom data in TypeScript, augment the\n   * > {@linkcode Data} interface.\n   *\n   * @example\n   *   This example show how to get and set info:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   const processor = unified().data('alpha', 'bravo')\n   *\n   *   processor.data('alpha') // => 'bravo'\n   *\n   *   processor.data() // => {alpha: 'bravo'}\n   *\n   *   processor.data({charlie: 'delta'})\n   *\n   *   processor.data() // => {charlie: 'delta'}\n   *   ```\n   *\n   * @template {keyof Data} Key\n   *\n   * @overload\n   * @returns {Data}\n   *\n   * @overload\n   * @param {Data} dataset\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Key} key\n   * @returns {Data[Key]}\n   *\n   * @overload\n   * @param {Key} key\n   * @param {Data[Key]} value\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @param {Data | Key} [key]\n   *   Key to get or set, or entire dataset to set, or nothing to get the\n   *   entire dataset (optional).\n   * @param {Data[Key]} [value]\n   *   Value to set (optional).\n   * @returns {unknown}\n   *   The current processor when setting, the value at `key` when getting, or\n   *   the entire dataset when getting without key.\n   */\n  data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', this.frozen)\n        this.namespace[key] = value\n        return this\n      }\n\n      // Get `key`.\n      return (own.call(this.namespace, key) && this.namespace[key]) || undefined\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', this.frozen)\n      this.namespace = key\n      return this\n    }\n\n    // Get space.\n    return this.namespace\n  }\n\n  /**\n   * Freeze a processor.\n   *\n   * Frozen processors are meant to be extended and not to be configured\n   * directly.\n   *\n   * When a processor is frozen it cannot be unfrozen.\n   * New processors working the same way can be created by calling the\n   * processor.\n   *\n   * It’s possible to freeze processors explicitly by calling `.freeze()`.\n   * Processors freeze automatically when `.parse()`, `.run()`, `.runSync()`,\n   * `.stringify()`, `.process()`, or `.processSync()` are called.\n   *\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   The current processor.\n   */\n  freeze() {\n    if (this.frozen) {\n      return this\n    }\n\n    // Cast so that we can type plugins easier.\n    // Plugins are supposed to be usable on different processors, not just on\n    // this exact processor.\n    const self = /** @type {Processor} */ (/** @type {unknown} */ (this))\n\n    while (++this.freezeIndex < this.attachers.length) {\n      const [attacher, ...options] = this.attachers[this.freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      const transformer = attacher.call(self, ...options)\n\n      if (typeof transformer === 'function') {\n        this.transformers.use(transformer)\n      }\n    }\n\n    this.frozen = true\n    this.freezeIndex = Number.POSITIVE_INFINITY\n\n    return this\n  }\n\n  /**\n   * Parse text to a syntax tree.\n   *\n   * > **Note**: `parse` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `parse` performs the parse phase, not the run phase or other\n   * > phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   file to parse (optional); typically `string` or `VFile`; any value\n   *   accepted as `x` in `new VFile(x)`.\n   * @returns {ParseTree extends undefined ? Node : ParseTree}\n   *   Syntax tree representing `file`.\n   */\n  parse(file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const parser = this.parser || this.Parser\n    assertParser('parse', parser)\n    return parser(String(realFile), realFile)\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * > **Note**: `process` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `process` performs the parse, run, and stringify phases.\n   *\n   * @overload\n   * @param {Compatible | undefined} file\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<VFileWithOutput<CompileResult>>}\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`]; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>> | undefined} [done]\n   *   Callback (optional).\n   * @returns {Promise<VFile> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise a promise, rejected with a fatal error or resolved with the\n   *   processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  process(file, done) {\n    const self = this\n\n    this.freeze()\n    assertParser('process', this.parser || this.Parser)\n    assertCompiler('process', this.compiler || this.Compiler)\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {((file: VFileWithOutput<CompileResult>) => undefined | void) | undefined} resolve\n     * @param {(error: Error | undefined) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      const realFile = vfile(file)\n      // Assume `ParseTree` (the result of the parser) matches `HeadTree` (the\n      // input of the first transform).\n      const parseTree =\n        /** @type {HeadTree extends undefined ? Node : HeadTree} */ (\n          /** @type {unknown} */ (self.parse(realFile))\n        )\n\n      self.run(parseTree, realFile, function (error, tree, file) {\n        if (error || !tree || !file) {\n          return realDone(error)\n        }\n\n        // Assume `TailTree` (the output of the last transform) matches\n        // `CompileTree` (the input of the compiler).\n        const compileTree =\n          /** @type {CompileTree extends undefined ? Node : CompileTree} */ (\n            /** @type {unknown} */ (tree)\n          )\n\n        const compileResult = self.stringify(compileTree, file)\n\n        if (looksLikeAValue(compileResult)) {\n          file.value = compileResult\n        } else {\n          file.result = compileResult\n        }\n\n        realDone(error, /** @type {VFileWithOutput<CompileResult>} */ (file))\n      })\n\n      /**\n       * @param {Error | undefined} error\n       * @param {VFileWithOutput<CompileResult> | undefined} [file]\n       * @returns {undefined}\n       */\n      function realDone(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `processSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `processSync` performs the parse, run, and stringify phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @returns {VFileWithOutput<CompileResult>}\n   *   The processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  processSync(file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {VFileWithOutput<CompileResult> | undefined} */\n    let result\n\n    this.freeze()\n    assertParser('processSync', this.parser || this.Parser)\n    assertCompiler('processSync', this.compiler || this.Compiler)\n\n    this.process(file, realDone)\n    assertDone('processSync', 'process', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n\n    return result\n\n    /**\n     * @type {ProcessCallback<VFileWithOutput<CompileResult>>}\n     */\n    function realDone(error, file) {\n      complete = true\n      bail(error)\n      result = file\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * > **Note**: `run` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `run` performs the run phase, not other phases.\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} file\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree>}\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {(\n   *   RunCallback<TailTree extends undefined ? Node : TailTree> |\n   *   Compatible\n   * )} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} [done]\n   *   Callback (optional).\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise, a promise rejected with a fatal error or resolved with the\n   *   transformed tree.\n   */\n  run(tree, file, done) {\n    assertNode(tree)\n    this.freeze()\n\n    const transformers = this.transformers\n\n    if (!done && typeof file === 'function') {\n      done = file\n      file = undefined\n    }\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {(\n     *   ((tree: TailTree extends undefined ? Node : TailTree) => undefined | void) |\n     *   undefined\n     * )} resolve\n     * @param {(error: Error) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      assert(\n        typeof file !== 'function',\n        '`file` can’t be a `done` anymore, we checked'\n      )\n      const realFile = vfile(file)\n      transformers.run(tree, realFile, realDone)\n\n      /**\n       * @param {Error | undefined} error\n       * @param {Node} outputTree\n       * @param {VFile} file\n       * @returns {undefined}\n       */\n      function realDone(error, outputTree, file) {\n        const resultingTree =\n          /** @type {TailTree extends undefined ? Node : TailTree} */ (\n            outputTree || tree\n          )\n\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(resultingTree)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, resultingTree, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `runSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `runSync` performs the run phase, not other phases.\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {TailTree extends undefined ? Node : TailTree}\n   *   Transformed tree.\n   */\n  runSync(tree, file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {(TailTree extends undefined ? Node : TailTree) | undefined} */\n    let result\n\n    this.run(tree, file, realDone)\n\n    assertDone('runSync', 'run', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n    return result\n\n    /**\n     * @type {RunCallback<TailTree extends undefined ? Node : TailTree>}\n     */\n    function realDone(error, tree) {\n      bail(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * Compile a syntax tree.\n   *\n   * > **Note**: `stringify` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `stringify` performs the stringify phase, not the run phase\n   * > or other phases.\n   *\n   * @param {CompileTree extends undefined ? Node : CompileTree} tree\n   *   Tree to compile.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {CompileResult extends undefined ? Value : CompileResult}\n   *   Textual representation of the tree (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most compilers\n   *   > return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  stringify(tree, file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const compiler = this.compiler || this.Compiler\n    assertCompiler('stringify', compiler)\n    assertNode(tree)\n\n    return compiler(tree, realFile)\n  }\n\n  /**\n   * Configure the processor to use a plugin, a list of usable values, or a\n   * preset.\n   *\n   * If the processor is already using a plugin, the previous plugin\n   * configuration is changed based on the options that are passed in.\n   * In other words, the plugin is not added a second time.\n   *\n   * > **Note**: `use` cannot be called on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * @example\n   *   There are many ways to pass plugins to `.use()`.\n   *   This example gives an overview:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   unified()\n   *     // Plugin with options:\n   *     .use(pluginA, {x: true, y: true})\n   *     // Passing the same plugin again merges configuration (to `{x: true, y: false, z: true}`):\n   *     .use(pluginA, {y: false, z: true})\n   *     // Plugins:\n   *     .use([pluginB, pluginC])\n   *     // Two plugins, the second with options:\n   *     .use([pluginD, [pluginE, {}]])\n   *     // Preset with plugins and settings:\n   *     .use({plugins: [pluginF, [pluginG, {}]], settings: {position: false}})\n   *     // Settings only:\n   *     .use({settings: {position: false}})\n   *   ```\n   *\n   * @template {Array<unknown>} [Parameters=[]]\n   * @template {Node | string | undefined} [Input=undefined]\n   * @template [Output=Input]\n   *\n   * @overload\n   * @param {Preset | null | undefined} [preset]\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {PluggableList} list\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Plugin<Parameters, Input, Output>} plugin\n   * @param {...(Parameters | [boolean])} parameters\n   * @returns {UsePlugin<ParseTree, HeadTree, TailTree, CompileTree, CompileResult, Input, Output>}\n   *\n   * @param {PluggableList | Plugin | Preset | null | undefined} value\n   *   Usable value.\n   * @param {...unknown} parameters\n   *   Parameters, when a plugin is given as a usable value.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   Current processor.\n   */\n  use(value, ...parameters) {\n    const attachers = this.attachers\n    const namespace = this.namespace\n\n    assertUnfrozen('use', this.frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, parameters)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    return this\n\n    /**\n     * @param {Pluggable} value\n     * @returns {undefined}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value, [])\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...parameters] =\n            /** @type {PluginTuple<Array<unknown>>} */ (value)\n          addPlugin(plugin, parameters)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {undefined}\n     */\n    function addPreset(result) {\n      if (!('plugins' in result) && !('settings' in result)) {\n        throw new Error(\n          'Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither'\n        )\n      }\n\n      addList(result.plugins)\n\n      if (result.settings) {\n        namespace.settings = extend(true, namespace.settings, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList | null | undefined} plugins\n     * @returns {undefined}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {Array<unknown>} parameters\n     * @returns {undefined}\n     */\n    function addPlugin(plugin, parameters) {\n      let index = -1\n      let entryIndex = -1\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entryIndex = index\n          break\n        }\n      }\n\n      if (entryIndex === -1) {\n        attachers.push([plugin, ...parameters])\n      }\n      // Only set if there was at least a `primary` value, otherwise we’d change\n      // `arguments.length`.\n      else if (parameters.length > 0) {\n        let [primary, ...rest] = parameters\n        const currentPrimary = attachers[entryIndex][1]\n        if (isPlainObj(currentPrimary) && isPlainObj(primary)) {\n          primary = extend(true, currentPrimary, primary)\n        }\n\n        attachers[entryIndex] = [plugin, primary, ...rest]\n      }\n    }\n  }\n}\n\n// Note: this returns a *callable* instance.\n// That’s why it’s documented as a function.\n/**\n * Create a new processor.\n *\n * @example\n *   This example shows how a new processor can be created (from `remark`) and linked\n *   to **stdin**(4) and **stdout**(4).\n *\n *   ```js\n *   import process from 'node:process'\n *   import concatStream from 'concat-stream'\n *   import {remark} from 'remark'\n *\n *   process.stdin.pipe(\n *     concatStream(function (buf) {\n *       process.stdout.write(String(remark().processSync(buf)))\n *     })\n *   )\n *   ```\n *\n * @returns\n *   New *unfrozen* processor (`processor`).\n *\n *   This processor is configured to work the same as its ancestor.\n *   When the descendant processor is configured in the future it does not\n *   affect the ancestral processor.\n */\nexport const unified = new Processor().freeze()\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!isPlainObj(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new VFile(value)\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is Value}\n */\nfunction looksLikeAValue(value) {\n  return typeof value === 'string' || isUint8Array(value)\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,IAAI,QAAO,MAAM;AACzB,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAAQC,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAAQC,MAAM,QAAO,QAAQ;AAC7B,SAAQC,KAAK,QAAO,OAAO;AAC3B,SAAQC,gBAAgB,QAAO,wBAAwB;;AAEvD;;AAEA;AACA;AACA;;AAEA,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,SAASH,gBAAgB,CAAC;EAC9C;AACF;AACA;EACEI,WAAWA,CAAA,EAAG;IACZ;IACA,KAAK,CAAC,MAAM,CAAC;;IAEb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,QAAQ,GAAGC,SAAS;;IAEzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM,GAAGD,SAAS;;IAEvB;IACA;IACA;IACA;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACE,SAAS,GAAG,EAAE;;IAEnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,QAAQ,GAAGH,SAAS;;IAEzB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACI,WAAW,GAAG,CAAC,CAAC;;IAErB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM,GAAGL,SAAS;;IAEvB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACM,SAAS,GAAG,CAAC,CAAC;;IAEnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM,GAAGP,SAAS;;IAEvB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACQ,YAAY,GAAGhB,MAAM,CAAC,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEiB,IAAIA,CAAA,EAAG;IACL;IACA,MAAMC,WAAW,GACf;IACE,IAAIb,SAAS,CAAC,CACf;IACH,IAAIc,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,EAAEA,KAAK,GAAG,IAAI,CAACT,SAAS,CAACU,MAAM,EAAE;MACtC,MAAMC,QAAQ,GAAG,IAAI,CAACX,SAAS,CAACS,KAAK,CAAC;MACtCD,WAAW,CAACI,GAAG,CAAC,GAAGD,QAAQ,CAAC;IAC9B;IAEAH,WAAW,CAACK,IAAI,CAAC3B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,CAACkB,SAAS,CAAC,CAAC;IAElD,OAAOI,WAAW;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEK,IAAIA,CAACC,GAAG,EAAEC,KAAK,EAAE;IACf,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MAC3B;MACA,IAAIE,SAAS,CAACN,MAAM,KAAK,CAAC,EAAE;QAC1BO,cAAc,CAAC,MAAM,EAAE,IAAI,CAACd,MAAM,CAAC;QACnC,IAAI,CAACC,SAAS,CAACU,GAAG,CAAC,GAAGC,KAAK;QAC3B,OAAO,IAAI;MACb;;MAEA;MACA,OAAQtB,GAAG,CAACyB,IAAI,CAAC,IAAI,CAACd,SAAS,EAAEU,GAAG,CAAC,IAAI,IAAI,CAACV,SAAS,CAACU,GAAG,CAAC,IAAKhB,SAAS;IAC5E;;IAEA;IACA,IAAIgB,GAAG,EAAE;MACPG,cAAc,CAAC,MAAM,EAAE,IAAI,CAACd,MAAM,CAAC;MACnC,IAAI,CAACC,SAAS,GAAGU,GAAG;MACpB,OAAO,IAAI;IACb;;IAEA;IACA,OAAO,IAAI,CAACV,SAAS;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEe,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,OAAO,IAAI;IACb;;IAEA;IACA;IACA;IACA,MAAMiB,IAAI,GAAG,yBAA0B,sBAAwB,IAAM;IAErE,OAAO,EAAE,IAAI,CAAClB,WAAW,GAAG,IAAI,CAACF,SAAS,CAACU,MAAM,EAAE;MACjD,MAAM,CAACC,QAAQ,EAAE,GAAGU,OAAO,CAAC,GAAG,IAAI,CAACrB,SAAS,CAAC,IAAI,CAACE,WAAW,CAAC;MAE/D,IAAImB,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;QACxB;MACF;MAEA,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QACvBA,OAAO,CAAC,CAAC,CAAC,GAAGvB,SAAS;MACxB;MAEA,MAAMwB,WAAW,GAAGX,QAAQ,CAACO,IAAI,CAACE,IAAI,EAAE,GAAGC,OAAO,CAAC;MAEnD,IAAI,OAAOC,WAAW,KAAK,UAAU,EAAE;QACrC,IAAI,CAAChB,YAAY,CAACM,GAAG,CAACU,WAAW,CAAC;MACpC;IACF;IAEA,IAAI,CAACnB,MAAM,GAAG,IAAI;IAClB,IAAI,CAACD,WAAW,GAAGqB,MAAM,CAACC,iBAAiB;IAE3C,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,KAAKA,CAACC,IAAI,EAAE;IACV,IAAI,CAACP,MAAM,CAAC,CAAC;IACb,MAAMQ,QAAQ,GAAGC,KAAK,CAACF,IAAI,CAAC;IAC5B,MAAMrB,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,IAAI,CAACN,MAAM;IACzC8B,YAAY,CAAC,OAAO,EAAExB,MAAM,CAAC;IAC7B,OAAOA,MAAM,CAACyB,MAAM,CAACH,QAAQ,CAAC,EAAEA,QAAQ,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEI,OAAOA,CAACL,IAAI,EAAEM,IAAI,EAAE;IAClB,MAAMZ,IAAI,GAAG,IAAI;IAEjB,IAAI,CAACD,MAAM,CAAC,CAAC;IACbU,YAAY,CAAC,SAAS,EAAE,IAAI,CAACxB,MAAM,IAAI,IAAI,CAACN,MAAM,CAAC;IACnDkC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAChC,QAAQ,IAAI,IAAI,CAACJ,QAAQ,CAAC;IAEzD,OAAOmC,IAAI,GAAGE,QAAQ,CAACpC,SAAS,EAAEkC,IAAI,CAAC,GAAG,IAAIG,OAAO,CAACD,QAAQ,CAAC;;IAE/D;IACA;AACJ;AACA;AACA;AACA;IACI,SAASA,QAAQA,CAACE,OAAO,EAAEC,MAAM,EAAE;MACjC,MAAMV,QAAQ,GAAGC,KAAK,CAACF,IAAI,CAAC;MAC5B;MACA;MACA,MAAMY,SAAS,GACb;MACE,sBAAwBlB,IAAI,CAACK,KAAK,CAACE,QAAQ,CAC5C;MAEHP,IAAI,CAACmB,GAAG,CAACD,SAAS,EAAEX,QAAQ,EAAE,UAAUa,KAAK,EAAEC,IAAI,EAAEf,IAAI,EAAE;QACzD,IAAIc,KAAK,IAAI,CAACC,IAAI,IAAI,CAACf,IAAI,EAAE;UAC3B,OAAOgB,QAAQ,CAACF,KAAK,CAAC;QACxB;;QAEA;QACA;QACA,MAAMG,WAAW,GACf;QACE,sBAAwBF,IACzB;QAEH,MAAMG,aAAa,GAAGxB,IAAI,CAACyB,SAAS,CAACF,WAAW,EAAEjB,IAAI,CAAC;QAEvD,IAAIoB,eAAe,CAACF,aAAa,CAAC,EAAE;UAClClB,IAAI,CAACX,KAAK,GAAG6B,aAAa;QAC5B,CAAC,MAAM;UACLlB,IAAI,CAACqB,MAAM,GAAGH,aAAa;QAC7B;QAEAF,QAAQ,CAACF,KAAK,EAAE,6CAA+Cd,IAAK,CAAC;MACvE,CAAC,CAAC;;MAEF;AACN;AACA;AACA;AACA;MACM,SAASgB,QAAQA,CAACF,KAAK,EAAEd,IAAI,EAAE;QAC7B,IAAIc,KAAK,IAAI,CAACd,IAAI,EAAE;UAClBW,MAAM,CAACG,KAAK,CAAC;QACf,CAAC,MAAM,IAAIJ,OAAO,EAAE;UAClBA,OAAO,CAACV,IAAI,CAAC;QACf,CAAC,MAAM;UACLtC,MAAM,CAAC4C,IAAI,EAAE,uCAAuC,CAAC;UACrDA,IAAI,CAAClC,SAAS,EAAE4B,IAAI,CAAC;QACvB;MACF;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsB,WAAWA,CAACtB,IAAI,EAAE;IAChB;IACA,IAAIuB,QAAQ,GAAG,KAAK;IACpB;IACA,IAAIF,MAAM;IAEV,IAAI,CAAC5B,MAAM,CAAC,CAAC;IACbU,YAAY,CAAC,aAAa,EAAE,IAAI,CAACxB,MAAM,IAAI,IAAI,CAACN,MAAM,CAAC;IACvDkC,cAAc,CAAC,aAAa,EAAE,IAAI,CAAChC,QAAQ,IAAI,IAAI,CAACJ,QAAQ,CAAC;IAE7D,IAAI,CAACkC,OAAO,CAACL,IAAI,EAAEgB,QAAQ,CAAC;IAC5BQ,UAAU,CAAC,aAAa,EAAE,SAAS,EAAED,QAAQ,CAAC;IAC9C7D,MAAM,CAAC2D,MAAM,EAAE,6CAA6C,CAAC;IAE7D,OAAOA,MAAM;;IAEb;AACJ;AACA;IACI,SAASL,QAAQA,CAACF,KAAK,EAAEd,IAAI,EAAE;MAC7BuB,QAAQ,GAAG,IAAI;MACfhE,IAAI,CAACuD,KAAK,CAAC;MACXO,MAAM,GAAGrB,IAAI;IACf;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEa,GAAGA,CAACE,IAAI,EAAEf,IAAI,EAAEM,IAAI,EAAE;IACpBmB,UAAU,CAACV,IAAI,CAAC;IAChB,IAAI,CAACtB,MAAM,CAAC,CAAC;IAEb,MAAMb,YAAY,GAAG,IAAI,CAACA,YAAY;IAEtC,IAAI,CAAC0B,IAAI,IAAI,OAAON,IAAI,KAAK,UAAU,EAAE;MACvCM,IAAI,GAAGN,IAAI;MACXA,IAAI,GAAG5B,SAAS;IAClB;IAEA,OAAOkC,IAAI,GAAGE,QAAQ,CAACpC,SAAS,EAAEkC,IAAI,CAAC,GAAG,IAAIG,OAAO,CAACD,QAAQ,CAAC;;IAE/D;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASA,QAAQA,CAACE,OAAO,EAAEC,MAAM,EAAE;MACjCjD,MAAM,CACJ,OAAOsC,IAAI,KAAK,UAAU,EAC1B,8CACF,CAAC;MACD,MAAMC,QAAQ,GAAGC,KAAK,CAACF,IAAI,CAAC;MAC5BpB,YAAY,CAACiC,GAAG,CAACE,IAAI,EAAEd,QAAQ,EAAEe,QAAQ,CAAC;;MAE1C;AACN;AACA;AACA;AACA;AACA;MACM,SAASA,QAAQA,CAACF,KAAK,EAAEY,UAAU,EAAE1B,IAAI,EAAE;QACzC,MAAM2B,aAAa,GACjB;QACED,UAAU,IAAIX,IACf;QAEH,IAAID,KAAK,EAAE;UACTH,MAAM,CAACG,KAAK,CAAC;QACf,CAAC,MAAM,IAAIJ,OAAO,EAAE;UAClBA,OAAO,CAACiB,aAAa,CAAC;QACxB,CAAC,MAAM;UACLjE,MAAM,CAAC4C,IAAI,EAAE,uCAAuC,CAAC;UACrDA,IAAI,CAAClC,SAAS,EAAEuD,aAAa,EAAE3B,IAAI,CAAC;QACtC;MACF;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE4B,OAAOA,CAACb,IAAI,EAAEf,IAAI,EAAE;IAClB;IACA,IAAIuB,QAAQ,GAAG,KAAK;IACpB;IACA,IAAIF,MAAM;IAEV,IAAI,CAACR,GAAG,CAACE,IAAI,EAAEf,IAAI,EAAEgB,QAAQ,CAAC;IAE9BQ,UAAU,CAAC,SAAS,EAAE,KAAK,EAAED,QAAQ,CAAC;IACtC7D,MAAM,CAAC2D,MAAM,EAAE,6CAA6C,CAAC;IAC7D,OAAOA,MAAM;;IAEb;AACJ;AACA;IACI,SAASL,QAAQA,CAACF,KAAK,EAAEC,IAAI,EAAE;MAC7BxD,IAAI,CAACuD,KAAK,CAAC;MACXO,MAAM,GAAGN,IAAI;MACbQ,QAAQ,GAAG,IAAI;IACjB;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEJ,SAASA,CAACJ,IAAI,EAAEf,IAAI,EAAE;IACpB,IAAI,CAACP,MAAM,CAAC,CAAC;IACb,MAAMQ,QAAQ,GAAGC,KAAK,CAACF,IAAI,CAAC;IAC5B,MAAMzB,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACJ,QAAQ;IAC/CoC,cAAc,CAAC,WAAW,EAAEhC,QAAQ,CAAC;IACrCkD,UAAU,CAACV,IAAI,CAAC;IAEhB,OAAOxC,QAAQ,CAACwC,IAAI,EAAEd,QAAQ,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEf,GAAGA,CAACG,KAAK,EAAE,GAAGwC,UAAU,EAAE;IACxB,MAAMvD,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMI,SAAS,GAAG,IAAI,CAACA,SAAS;IAEhCa,cAAc,CAAC,KAAK,EAAE,IAAI,CAACd,MAAM,CAAC;IAElC,IAAIY,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKjB,SAAS,EAAE;MACzC;IAAA,CACD,MAAM,IAAI,OAAOiB,KAAK,KAAK,UAAU,EAAE;MACtCyC,SAAS,CAACzC,KAAK,EAAEwC,UAAU,CAAC;IAC9B,CAAC,MAAM,IAAI,OAAOxC,KAAK,KAAK,QAAQ,EAAE;MACpC,IAAI0C,KAAK,CAACC,OAAO,CAAC3C,KAAK,CAAC,EAAE;QACxB4C,OAAO,CAAC5C,KAAK,CAAC;MAChB,CAAC,MAAM;QACL6C,SAAS,CAAC7C,KAAK,CAAC;MAClB;IACF,CAAC,MAAM;MACL,MAAM,IAAI8C,SAAS,CAAC,8BAA8B,GAAG9C,KAAK,GAAG,GAAG,CAAC;IACnE;IAEA,OAAO,IAAI;;IAEX;AACJ;AACA;AACA;IACI,SAAS+C,GAAGA,CAAC/C,KAAK,EAAE;MAClB,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;QAC/ByC,SAAS,CAACzC,KAAK,EAAE,EAAE,CAAC;MACtB,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACpC,IAAI0C,KAAK,CAACC,OAAO,CAAC3C,KAAK,CAAC,EAAE;UACxB,MAAM,CAACgD,MAAM,EAAE,GAAGR,UAAU,CAAC,GAC3B,0CAA4CxC,KAAM;UACpDyC,SAAS,CAACO,MAAM,EAAER,UAAU,CAAC;QAC/B,CAAC,MAAM;UACLK,SAAS,CAAC7C,KAAK,CAAC;QAClB;MACF,CAAC,MAAM;QACL,MAAM,IAAI8C,SAAS,CAAC,8BAA8B,GAAG9C,KAAK,GAAG,GAAG,CAAC;MACnE;IACF;;IAEA;AACJ;AACA;AACA;IACI,SAAS6C,SAASA,CAACb,MAAM,EAAE;MACzB,IAAI,EAAE,SAAS,IAAIA,MAAM,CAAC,IAAI,EAAE,UAAU,IAAIA,MAAM,CAAC,EAAE;QACrD,MAAM,IAAIiB,KAAK,CACb,4KACF,CAAC;MACH;MAEAL,OAAO,CAACZ,MAAM,CAACkB,OAAO,CAAC;MAEvB,IAAIlB,MAAM,CAACmB,QAAQ,EAAE;QACnB9D,SAAS,CAAC8D,QAAQ,GAAGhF,MAAM,CAAC,IAAI,EAAEkB,SAAS,CAAC8D,QAAQ,EAAEnB,MAAM,CAACmB,QAAQ,CAAC;MACxE;IACF;;IAEA;AACJ;AACA;AACA;IACI,SAASP,OAAOA,CAACM,OAAO,EAAE;MACxB,IAAIxD,KAAK,GAAG,CAAC,CAAC;MAEd,IAAIwD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKnE,SAAS,EAAE;QAC7C;MAAA,CACD,MAAM,IAAI2D,KAAK,CAACC,OAAO,CAACO,OAAO,CAAC,EAAE;QACjC,OAAO,EAAExD,KAAK,GAAGwD,OAAO,CAACvD,MAAM,EAAE;UAC/B,MAAMyD,KAAK,GAAGF,OAAO,CAACxD,KAAK,CAAC;UAC5BqD,GAAG,CAACK,KAAK,CAAC;QACZ;MACF,CAAC,MAAM;QACL,MAAM,IAAIN,SAAS,CAAC,mCAAmC,GAAGI,OAAO,GAAG,GAAG,CAAC;MAC1E;IACF;;IAEA;AACJ;AACA;AACA;AACA;IACI,SAAST,SAASA,CAACO,MAAM,EAAER,UAAU,EAAE;MACrC,IAAI9C,KAAK,GAAG,CAAC,CAAC;MACd,IAAI2D,UAAU,GAAG,CAAC,CAAC;MAEnB,OAAO,EAAE3D,KAAK,GAAGT,SAAS,CAACU,MAAM,EAAE;QACjC,IAAIV,SAAS,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,KAAKsD,MAAM,EAAE;UAClCK,UAAU,GAAG3D,KAAK;UAClB;QACF;MACF;MAEA,IAAI2D,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBpE,SAAS,CAACqE,IAAI,CAAC,CAACN,MAAM,EAAE,GAAGR,UAAU,CAAC,CAAC;MACzC;MACA;MACA;MAAA,KACK,IAAIA,UAAU,CAAC7C,MAAM,GAAG,CAAC,EAAE;QAC9B,IAAI,CAAC4D,OAAO,EAAE,GAAGC,IAAI,CAAC,GAAGhB,UAAU;QACnC,MAAMiB,cAAc,GAAGxE,SAAS,CAACoE,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI/E,UAAU,CAACmF,cAAc,CAAC,IAAInF,UAAU,CAACiF,OAAO,CAAC,EAAE;UACrDA,OAAO,GAAGpF,MAAM,CAAC,IAAI,EAAEsF,cAAc,EAAEF,OAAO,CAAC;QACjD;QAEAtE,SAAS,CAACoE,UAAU,CAAC,GAAG,CAACL,MAAM,EAAEO,OAAO,EAAE,GAAGC,IAAI,CAAC;MACpD;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,OAAO,GAAG,IAAI9E,SAAS,CAAC,CAAC,CAACwB,MAAM,CAAC,CAAC;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,YAAYA,CAAC6C,IAAI,EAAE3D,KAAK,EAAE;EACjC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAI8C,SAAS,CAAC,UAAU,GAAGa,IAAI,GAAG,oBAAoB,CAAC;EAC/D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASzC,cAAcA,CAACyC,IAAI,EAAE3D,KAAK,EAAE;EACnC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAI8C,SAAS,CAAC,UAAU,GAAGa,IAAI,GAAG,sBAAsB,CAAC;EACjE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASzD,cAAcA,CAACyD,IAAI,EAAEvE,MAAM,EAAE;EACpC,IAAIA,MAAM,EAAE;IACV,MAAM,IAAI6D,KAAK,CACb,eAAe,GACbU,IAAI,GACJ,kHACJ,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASvB,UAAUA,CAACwB,IAAI,EAAE;EACxB;EACA;EACA,IAAI,CAACtF,UAAU,CAACsF,IAAI,CAAC,IAAI,OAAOA,IAAI,CAACC,IAAI,KAAK,QAAQ,EAAE;IACtD,MAAM,IAAIf,SAAS,CAAC,sBAAsB,GAAGc,IAAI,GAAG,GAAG,CAAC;IACxD;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASzB,UAAUA,CAACwB,IAAI,EAAEG,SAAS,EAAE5B,QAAQ,EAAE;EAC7C,IAAI,CAACA,QAAQ,EAAE;IACb,MAAM,IAAIe,KAAK,CACb,GAAG,GAAGU,IAAI,GAAG,yBAAyB,GAAGG,SAAS,GAAG,WACvD,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASjD,KAAKA,CAACb,KAAK,EAAE;EACpB,OAAO+D,eAAe,CAAC/D,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAIxB,KAAK,CAACwB,KAAK,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA,SAAS+D,eAAeA,CAAC/D,KAAK,EAAE;EAC9B,OAAOgE,OAAO,CACZhE,KAAK,IACH,OAAOA,KAAK,KAAK,QAAQ,IACzB,SAAS,IAAIA,KAAK,IAClB,UAAU,IAAIA,KAClB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAAS+B,eAAeA,CAAC/B,KAAK,EAAE;EAC9B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIiE,YAAY,CAACjE,KAAK,CAAC;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiE,YAAYA,CAACjE,KAAK,EAAE;EAC3B,OAAOgE,OAAO,CACZhE,KAAK,IACH,OAAOA,KAAK,KAAK,QAAQ,IACzB,YAAY,IAAIA,KAAK,IACrB,YAAY,IAAIA,KACpB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}