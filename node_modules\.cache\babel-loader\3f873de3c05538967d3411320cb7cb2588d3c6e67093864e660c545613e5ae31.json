{"ast": null, "code": "import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\n/**\n * Send a template to the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {object} templatePrams - the template params, what will be set to the EmailJS template\n * @param {string} userID - the EmailJS user ID\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const send = (serviceID, templateID, templatePrams, userID) => {\n  const uID = userID || store._userID;\n  validateParams(uID, serviceID, templateID);\n  const params = {\n    lib_version: '3.2.0',\n    user_id: uID,\n    service_id: serviceID,\n    template_id: templateID,\n    template_params: templatePrams\n  };\n  return sendPost('/api/v1.0/email/send', JSON.stringify(params), {\n    'Content-type': 'application/json'\n  });\n};", "map": {"version": 3, "names": ["store", "validateParams", "sendPost", "send", "serviceID", "templateID", "templatePrams", "userID", "uID", "_userID", "params", "lib_version", "user_id", "service_id", "template_id", "template_params", "JSON", "stringify"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/emailjs-com/es/methods/send/send.js"], "sourcesContent": ["import { store } from '../../store/store';\nimport { validateParams } from '../../utils/validateParams';\nimport { sendPost } from '../../api/sendPost';\n/**\n * Send a template to the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {object} templatePrams - the template params, what will be set to the EmailJS template\n * @param {string} userID - the EmailJS user ID\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const send = (serviceID, templateID, templatePrams, userID) => {\n    const uID = userID || store._userID;\n    validateParams(uID, serviceID, templateID);\n    const params = {\n        lib_version: '3.2.0',\n        user_id: uID,\n        service_id: serviceID,\n        template_id: templateID,\n        template_params: templatePrams,\n    };\n    return sendPost('/api/v1.0/email/send', JSON.stringify(params), {\n        'Content-type': 'application/json',\n    });\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,mBAAmB;AACzC,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAGA,CAACC,SAAS,EAAEC,UAAU,EAAEC,aAAa,EAAEC,MAAM,KAAK;EAClE,MAAMC,GAAG,GAAGD,MAAM,IAAIP,KAAK,CAACS,OAAO;EACnCR,cAAc,CAACO,GAAG,EAAEJ,SAAS,EAAEC,UAAU,CAAC;EAC1C,MAAMK,MAAM,GAAG;IACXC,WAAW,EAAE,OAAO;IACpBC,OAAO,EAAEJ,GAAG;IACZK,UAAU,EAAET,SAAS;IACrBU,WAAW,EAAET,UAAU;IACvBU,eAAe,EAAET;EACrB,CAAC;EACD,OAAOJ,QAAQ,CAAC,sBAAsB,EAAEc,IAAI,CAACC,SAAS,CAACP,MAAM,CAAC,EAAE;IAC5D,cAAc,EAAE;EACpB,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}