{"ast": null, "code": "import GoTrueAdmin<PERSON><PERSON> from './GoTrueAdminApi';\nimport { DEFAULT_HEADERS, EXPIRY_MARGIN_MS, AUTO_REFRESH_TICK_DURATION_MS, AUTO_REFRESH_TICK_THRESHOLD, GOTRUE_URL, STORAGE_KEY, JW<PERSON>_TTL } from './lib/constants';\nimport { AuthImplicitGrantRedirectError, AuthPKCEGrantCodeExchangeError, AuthInvalidCredentialsError, AuthSessionMissingError, AuthInvalidTokenResponseError, AuthUnknownError, isAuthApiError, isAuthError, isAuthRetryableFetchError, isAuthSessionMissingError, isAuthImplicitGrantRedirectError, AuthInvalidJwtError } from './lib/errors';\nimport { _request, _sessionResponse, _sessionResponsePassword, _userResponse, _ssoResponse } from './lib/fetch';\nimport { Deferred, getItemAsync, isBrowser, removeItemAsync, resolveFetch, setItemAsync, uuid, retryable, sleep, supportsLocalStorage, parseParametersFromURL, getCodeChallengeAndMethod, getAlgorithm, validateExp, decodeJWT } from './lib/helpers';\nimport { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage';\nimport { polyfillGlobalThis } from './lib/polyfills';\nimport { version } from './lib/version';\nimport { LockAcquireTimeoutError, navigatorLock } from './lib/locks';\nimport { stringToUint8Array } from './lib/base64url';\npolyfillGlobalThis(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n  url: GOTRUE_URL,\n  storageKey: STORAGE_KEY,\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  headers: DEFAULT_HEADERS,\n  flowType: 'implicit',\n  debug: false,\n  hasCustomAuthorizationHeader: false\n};\nasync function lockNoOp(name, acquireTimeout, fn) {\n  return await fn();\n}\nexport default class GoTrueClient {\n  /**\n   * Create a new client for use in the browser.\n   */\n  constructor(options) {\n    var _a, _b;\n    this.memoryStorage = null;\n    this.stateChangeEmitters = new Map();\n    this.autoRefreshTicker = null;\n    this.visibilityChangedCallback = null;\n    this.refreshingDeferred = null;\n    /**\n     * Keeps track of the async client initialization.\n     * When null or not yet resolved the auth state is `unknown`\n     * Once resolved the the auth state is known and it's save to call any further client methods.\n     * Keep extra care to never reject or throw uncaught errors\n     */\n    this.initializePromise = null;\n    this.detectSessionInUrl = true;\n    this.hasCustomAuthorizationHeader = false;\n    this.suppressGetSessionWarning = false;\n    this.lockAcquired = false;\n    this.pendingInLock = [];\n    /**\n     * Used to broadcast state change events to other tabs listening.\n     */\n    this.broadcastChannel = null;\n    this.logger = console.log;\n    this.instanceID = GoTrueClient.nextInstanceID;\n    GoTrueClient.nextInstanceID += 1;\n    if (this.instanceID > 0 && isBrowser()) {\n      console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n    }\n    const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n    this.logDebugMessages = !!settings.debug;\n    if (typeof settings.debug === 'function') {\n      this.logger = settings.debug;\n    }\n    this.persistSession = settings.persistSession;\n    this.storageKey = settings.storageKey;\n    this.autoRefreshToken = settings.autoRefreshToken;\n    this.admin = new GoTrueAdminApi({\n      url: settings.url,\n      headers: settings.headers,\n      fetch: settings.fetch\n    });\n    this.url = settings.url;\n    this.headers = settings.headers;\n    this.fetch = resolveFetch(settings.fetch);\n    this.lock = settings.lock || lockNoOp;\n    this.detectSessionInUrl = settings.detectSessionInUrl;\n    this.flowType = settings.flowType;\n    this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader;\n    if (settings.lock) {\n      this.lock = settings.lock;\n    } else if (isBrowser() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {\n      this.lock = navigatorLock;\n    } else {\n      this.lock = lockNoOp;\n    }\n    this.jwks = {\n      keys: []\n    };\n    this.jwks_cached_at = Number.MIN_SAFE_INTEGER;\n    this.mfa = {\n      verify: this._verify.bind(this),\n      enroll: this._enroll.bind(this),\n      unenroll: this._unenroll.bind(this),\n      challenge: this._challenge.bind(this),\n      listFactors: this._listFactors.bind(this),\n      challengeAndVerify: this._challengeAndVerify.bind(this),\n      getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this)\n    };\n    if (this.persistSession) {\n      if (settings.storage) {\n        this.storage = settings.storage;\n      } else {\n        if (supportsLocalStorage()) {\n          this.storage = localStorageAdapter;\n        } else {\n          this.memoryStorage = {};\n          this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n        }\n      }\n    } else {\n      this.memoryStorage = {};\n      this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n    }\n    if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n      try {\n        this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n      } catch (e) {\n        console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n      }\n      (_b = this.broadcastChannel) === null || _b === void 0 ? void 0 : _b.addEventListener('message', async event => {\n        this._debug('received broadcast notification from other tab or client', event);\n        await this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n      });\n    }\n    this.initialize();\n  }\n  _debug(...args) {\n    if (this.logDebugMessages) {\n      this.logger(`GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`, ...args);\n    }\n    return this;\n  }\n  /**\n   * Initializes the client session either from the url or from storage.\n   * This method is automatically called when instantiating the client, but should also be called\n   * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n   */\n  async initialize() {\n    if (this.initializePromise) {\n      return await this.initializePromise;\n    }\n    this.initializePromise = (async () => {\n      return await this._acquireLock(-1, async () => {\n        return await this._initialize();\n      });\n    })();\n    return await this.initializePromise;\n  }\n  /**\n   * IMPORTANT:\n   * 1. Never throw in this method, as it is called from the constructor\n   * 2. Never return a session from this method as it would be cached over\n   *    the whole lifetime of the client\n   */\n  async _initialize() {\n    var _a;\n    try {\n      const params = parseParametersFromURL(window.location.href);\n      let callbackUrlType = 'none';\n      if (this._isImplicitGrantCallback(params)) {\n        callbackUrlType = 'implicit';\n      } else if (await this._isPKCECallback(params)) {\n        callbackUrlType = 'pkce';\n      }\n      /**\n       * Attempt to get the session from the URL only if these conditions are fulfilled\n       *\n       * Note: If the URL isn't one of the callback url types (implicit or pkce),\n       * then there could be an existing session so we don't want to prematurely remove it\n       */\n      if (isBrowser() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n        const {\n          data,\n          error\n        } = await this._getSessionFromURL(params, callbackUrlType);\n        if (error) {\n          this._debug('#_initialize()', 'error detecting session from URL', error);\n          if (isAuthImplicitGrantRedirectError(error)) {\n            const errorCode = (_a = error.details) === null || _a === void 0 ? void 0 : _a.code;\n            if (errorCode === 'identity_already_exists' || errorCode === 'identity_not_found' || errorCode === 'single_identity_not_deletable') {\n              return {\n                error\n              };\n            }\n          }\n          // failed login attempt via url,\n          // remove old session as in verifyOtp, signUp and signInWith*\n          await this._removeSession();\n          return {\n            error\n          };\n        }\n        const {\n          session,\n          redirectType\n        } = data;\n        this._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n        await this._saveSession(session);\n        setTimeout(async () => {\n          if (redirectType === 'recovery') {\n            await this._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n          } else {\n            await this._notifyAllSubscribers('SIGNED_IN', session);\n          }\n        }, 0);\n        return {\n          error: null\n        };\n      }\n      // no login attempt via callback url try to recover session from storage\n      await this._recoverAndRefresh();\n      return {\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          error\n        };\n      }\n      return {\n        error: new AuthUnknownError('Unexpected error during initialization', error)\n      };\n    } finally {\n      await this._handleVisibilityChange();\n      this._debug('#_initialize()', 'end');\n    }\n  }\n  /**\n   * Creates a new anonymous user.\n   *\n   * @returns A session where the is_anonymous claim in the access token JWT set to true\n   */\n  async signInAnonymously(credentials) {\n    var _a, _b, _c;\n    try {\n      const res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n        headers: this.headers,\n        body: {\n          data: (_b = (_a = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _a === void 0 ? void 0 : _a.data) !== null && _b !== void 0 ? _b : {},\n          gotrue_meta_security: {\n            captcha_token: (_c = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _c === void 0 ? void 0 : _c.captchaToken\n          }\n        },\n        xform: _sessionResponse\n      });\n      const {\n        data,\n        error\n      } = res;\n      if (error || !data) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: error\n        };\n      }\n      const session = data.session;\n      const user = data.user;\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Creates a new user.\n   *\n   * Be aware that if a user account exists in the system you may get back an\n   * error message that attempts to hide this information from the user.\n   * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n   *\n   * @returns A logged-in session if the server has \"autoconfirm\" ON\n   * @returns A user if the server has \"autoconfirm\" OFF\n   */\n  async signUp(credentials) {\n    var _a, _b, _c;\n    try {\n      let res;\n      if ('email' in credentials) {\n        const {\n          email,\n          password,\n          options\n        } = credentials;\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n          ;\n          [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n        }\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n          body: {\n            email,\n            password,\n            data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          },\n          xform: _sessionResponse\n        });\n      } else if ('phone' in credentials) {\n        const {\n          phone,\n          password,\n          options\n        } = credentials;\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n            channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponse\n        });\n      } else {\n        throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n      }\n      const {\n        data,\n        error\n      } = res;\n      if (error || !data) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: error\n        };\n      }\n      const session = data.session;\n      const user = data.user;\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in an existing user with an email and password or phone and password.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or that the\n   * email/phone and password combination is wrong or that the account can only\n   * be accessed via social login.\n   */\n  async signInWithPassword(credentials) {\n    try {\n      let res;\n      if ('email' in credentials) {\n        const {\n          email,\n          password,\n          options\n        } = credentials;\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            email,\n            password,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponsePassword\n        });\n      } else if ('phone' in credentials) {\n        const {\n          phone,\n          password,\n          options\n        } = credentials;\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponsePassword\n        });\n      } else {\n        throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n      }\n      const {\n        data,\n        error\n      } = res;\n      if (error) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data: Object.assign({\n          user: data.user,\n          session: data.session\n        }, data.weak_password ? {\n          weakPassword: data.weak_password\n        } : null),\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in an existing user via a third-party provider.\n   * This method supports the PKCE flow.\n   */\n  async signInWithOAuth(credentials) {\n    var _a, _b, _c, _d;\n    return await this._handleProviderSignIn(credentials.provider, {\n      redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n      scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n      queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n      skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect\n    });\n  }\n  /**\n   * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n   */\n  async exchangeCodeForSession(authCode) {\n    await this.initializePromise;\n    return this._acquireLock(-1, async () => {\n      return this._exchangeCodeForSession(authCode);\n    });\n  }\n  async _exchangeCodeForSession(authCode) {\n    const storageItem = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n    const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n    try {\n      const {\n        data,\n        error\n      } = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=pkce`, {\n        headers: this.headers,\n        body: {\n          auth_code: authCode,\n          code_verifier: codeVerifier\n        },\n        xform: _sessionResponse\n      });\n      await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n      if (error) {\n        throw error;\n      }\n      if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null,\n            redirectType: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data: Object.assign(Object.assign({}, data), {\n          redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null\n        }),\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null,\n            redirectType: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Allows signing in with an OIDC ID token. The authentication provider used\n   * should be enabled and configured.\n   */\n  async signInWithIdToken(credentials) {\n    try {\n      const {\n        options,\n        provider,\n        token,\n        access_token,\n        nonce\n      } = credentials;\n      const res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n        headers: this.headers,\n        body: {\n          provider,\n          id_token: token,\n          access_token,\n          nonce,\n          gotrue_meta_security: {\n            captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n          }\n        },\n        xform: _sessionResponse\n      });\n      const {\n        data,\n        error\n      } = res;\n      if (error) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in a user using magiclink or a one-time password (OTP).\n   *\n   * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n   * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n   * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or, that the account\n   * can only be accessed via social login.\n   *\n   * Do note that you will need to configure a Whatsapp sender on Twilio\n   * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n   * channel is not supported on other providers\n   * at this time.\n   * This method supports PKCE when an email is passed.\n   */\n  async signInWithOtp(credentials) {\n    var _a, _b, _c, _d, _e;\n    try {\n      if ('email' in credentials) {\n        const {\n          email,\n          options\n        } = credentials;\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n          ;\n          [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n        }\n        const {\n          error\n        } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            email,\n            data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n            create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          },\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n        });\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      if ('phone' in credentials) {\n        const {\n          phone,\n          options\n        } = credentials;\n        const {\n          data,\n          error\n        } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            phone,\n            data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n            create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            },\n            channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms'\n          }\n        });\n        return {\n          data: {\n            user: null,\n            session: null,\n            messageId: data === null || data === void 0 ? void 0 : data.message_id\n          },\n          error\n        };\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number.');\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n   */\n  async verifyOtp(params) {\n    var _a, _b;\n    try {\n      let redirectTo = undefined;\n      let captchaToken = undefined;\n      if ('options' in params) {\n        redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n        captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n      }\n      const {\n        data,\n        error\n      } = await _request(this.fetch, 'POST', `${this.url}/verify`, {\n        headers: this.headers,\n        body: Object.assign(Object.assign({}, params), {\n          gotrue_meta_security: {\n            captcha_token: captchaToken\n          }\n        }),\n        redirectTo,\n        xform: _sessionResponse\n      });\n      if (error) {\n        throw error;\n      }\n      if (!data) {\n        throw new Error('An error occurred on token verification.');\n      }\n      const session = data.session;\n      const user = data.user;\n      if (session === null || session === void 0 ? void 0 : session.access_token) {\n        await this._saveSession(session);\n        await this._notifyAllSubscribers(params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Attempts a single-sign on using an enterprise Identity Provider. A\n   * successful SSO attempt will redirect the current page to the identity\n   * provider authorization page. The redirect URL is implementation and SSO\n   * protocol specific.\n   *\n   * You can use it by providing a SSO domain. Typically you can extract this\n   * domain by asking users for their email address. If this domain is\n   * registered on the Auth instance the redirect will use that organization's\n   * currently active SSO Identity Provider for the login.\n   *\n   * If you have built an organization-specific login page, you can use the\n   * organization's SSO Identity Provider UUID directly instead.\n   */\n  async signInWithSSO(params) {\n    var _a, _b, _c;\n    try {\n      let codeChallenge = null;\n      let codeChallengeMethod = null;\n      if (this.flowType === 'pkce') {\n        ;\n        [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n      }\n      return await _request(this.fetch, 'POST', `${this.url}/sso`, {\n        body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, 'providerId' in params ? {\n          provider_id: params.providerId\n        } : null), 'domain' in params ? {\n          domain: params.domain\n        } : null), {\n          redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined\n        }), ((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken) ? {\n          gotrue_meta_security: {\n            captcha_token: params.options.captchaToken\n          }\n        } : null), {\n          skip_http_redirect: true,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod\n        }),\n        headers: this.headers,\n        xform: _ssoResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Sends a reauthentication OTP to the user's email or phone number.\n   * Requires the user to be signed-in.\n   */\n  async reauthenticate() {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._reauthenticate();\n    });\n  }\n  async _reauthenticate() {\n    try {\n      return await this._useSession(async result => {\n        const {\n          data: {\n            session\n          },\n          error: sessionError\n        } = result;\n        if (sessionError) throw sessionError;\n        if (!session) throw new AuthSessionMissingError();\n        const {\n          error\n        } = await _request(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n          headers: this.headers,\n          jwt: session.access_token\n        });\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n   */\n  async resend(credentials) {\n    try {\n      const endpoint = `${this.url}/resend`;\n      if ('email' in credentials) {\n        const {\n          email,\n          type,\n          options\n        } = credentials;\n        const {\n          error\n        } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            email,\n            type,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n        });\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      } else if ('phone' in credentials) {\n        const {\n          phone,\n          type,\n          options\n        } = credentials;\n        const {\n          data,\n          error\n        } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            phone,\n            type,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          }\n        });\n        return {\n          data: {\n            user: null,\n            session: null,\n            messageId: data === null || data === void 0 ? void 0 : data.message_id\n          },\n          error\n        };\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Returns the session, refreshing it if necessary.\n   *\n   * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n   *\n   * **IMPORTANT:** This method loads values directly from the storage attached\n   * to the client. If that storage is based on request cookies for example,\n   * the values in it may not be authentic and therefore it's strongly advised\n   * against using this method and its results in such circumstances. A warning\n   * will be emitted if this is detected. Use {@link #getUser()} instead.\n   */\n  async getSession() {\n    await this.initializePromise;\n    const result = await this._acquireLock(-1, async () => {\n      return this._useSession(async result => {\n        return result;\n      });\n    });\n    return result;\n  }\n  /**\n   * Acquires a global lock based on the storage key.\n   */\n  async _acquireLock(acquireTimeout, fn) {\n    this._debug('#_acquireLock', 'begin', acquireTimeout);\n    try {\n      if (this.lockAcquired) {\n        const last = this.pendingInLock.length ? this.pendingInLock[this.pendingInLock.length - 1] : Promise.resolve();\n        const result = (async () => {\n          await last;\n          return await fn();\n        })();\n        this.pendingInLock.push((async () => {\n          try {\n            await result;\n          } catch (e) {\n            // we just care if it finished\n          }\n        })());\n        return result;\n      }\n      return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n        this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey);\n        try {\n          this.lockAcquired = true;\n          const result = fn();\n          this.pendingInLock.push((async () => {\n            try {\n              await result;\n            } catch (e) {\n              // we just care if it finished\n            }\n          })());\n          await result;\n          // keep draining the queue until there's nothing to wait on\n          while (this.pendingInLock.length) {\n            const waitOn = [...this.pendingInLock];\n            await Promise.all(waitOn);\n            this.pendingInLock.splice(0, waitOn.length);\n          }\n          return await result;\n        } finally {\n          this._debug('#_acquireLock', 'lock released for storage key', this.storageKey);\n          this.lockAcquired = false;\n        }\n      });\n    } finally {\n      this._debug('#_acquireLock', 'end');\n    }\n  }\n  /**\n   * Use instead of {@link #getSession} inside the library. It is\n   * semantically usually what you want, as getting a session involves some\n   * processing afterwards that requires only one client operating on the\n   * session at once across multiple tabs or processes.\n   */\n  async _useSession(fn) {\n    this._debug('#_useSession', 'begin');\n    try {\n      // the use of __loadSession here is the only correct use of the function!\n      const result = await this.__loadSession();\n      return await fn(result);\n    } finally {\n      this._debug('#_useSession', 'end');\n    }\n  }\n  /**\n   * NEVER USE DIRECTLY!\n   *\n   * Always use {@link #_useSession}.\n   */\n  async __loadSession() {\n    this._debug('#__loadSession()', 'begin');\n    if (!this.lockAcquired) {\n      this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n    }\n    try {\n      let currentSession = null;\n      const maybeSession = await getItemAsync(this.storage, this.storageKey);\n      this._debug('#getSession()', 'session from storage', maybeSession);\n      if (maybeSession !== null) {\n        if (this._isValidSession(maybeSession)) {\n          currentSession = maybeSession;\n        } else {\n          this._debug('#getSession()', 'session from storage is not valid');\n          await this._removeSession();\n        }\n      }\n      if (!currentSession) {\n        return {\n          data: {\n            session: null\n          },\n          error: null\n        };\n      }\n      // A session is considered expired before the access token _actually_\n      // expires. When the autoRefreshToken option is off (or when the tab is\n      // in the background), very eager users of getSession() -- like\n      // realtime-js -- might send a valid JWT which will expire by the time it\n      // reaches the server.\n      const hasExpired = currentSession.expires_at ? currentSession.expires_at * 1000 - Date.now() < EXPIRY_MARGIN_MS : false;\n      this._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n      if (!hasExpired) {\n        if (this.storage.isServer) {\n          let suppressWarning = this.suppressGetSessionWarning;\n          const proxySession = new Proxy(currentSession, {\n            get: (target, prop, receiver) => {\n              if (!suppressWarning && prop === 'user') {\n                // only show warning when the user object is being accessed from the server\n                console.warn('Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.');\n                suppressWarning = true; // keeps this proxy instance from logging additional warnings\n                this.suppressGetSessionWarning = true; // keeps this client's future proxy instances from warning\n              }\n              return Reflect.get(target, prop, receiver);\n            }\n          });\n          currentSession = proxySession;\n        }\n        return {\n          data: {\n            session: currentSession\n          },\n          error: null\n        };\n      }\n      const {\n        session,\n        error\n      } = await this._callRefreshToken(currentSession.refresh_token);\n      if (error) {\n        return {\n          data: {\n            session: null\n          },\n          error\n        };\n      }\n      return {\n        data: {\n          session\n        },\n        error: null\n      };\n    } finally {\n      this._debug('#__loadSession()', 'end');\n    }\n  }\n  /**\n   * Gets the current user details if there is an existing session. This method\n   * performs a network request to the Supabase Auth server, so the returned\n   * value is authentic and can be used to base authorization rules on.\n   *\n   * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n   */\n  async getUser(jwt) {\n    if (jwt) {\n      return await this._getUser(jwt);\n    }\n    await this.initializePromise;\n    const result = await this._acquireLock(-1, async () => {\n      return await this._getUser();\n    });\n    return result;\n  }\n  async _getUser(jwt) {\n    try {\n      if (jwt) {\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: jwt,\n          xform: _userResponse\n        });\n      }\n      return await this._useSession(async result => {\n        var _a, _b, _c;\n        const {\n          data,\n          error\n        } = result;\n        if (error) {\n          throw error;\n        }\n        // returns an error if there is no access_token or custom authorization header\n        if (!((_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) && !this.hasCustomAuthorizationHeader) {\n          return {\n            data: {\n              user: null\n            },\n            error: new AuthSessionMissingError()\n          };\n        }\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: (_c = (_b = data.session) === null || _b === void 0 ? void 0 : _b.access_token) !== null && _c !== void 0 ? _c : undefined,\n          xform: _userResponse\n        });\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        if (isAuthSessionMissingError(error)) {\n          // JWT contains a `session_id` which does not correspond to an active\n          // session in the database, indicating the user is signed out.\n          await this._removeSession();\n          await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n        }\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Updates user data for a logged in user.\n   */\n  async updateUser(attributes, options = {}) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._updateUser(attributes, options);\n    });\n  }\n  async _updateUser(attributes, options = {}) {\n    try {\n      return await this._useSession(async result => {\n        const {\n          data: sessionData,\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          throw sessionError;\n        }\n        if (!sessionData.session) {\n          throw new AuthSessionMissingError();\n        }\n        const session = sessionData.session;\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce' && attributes.email != null) {\n          ;\n          [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n        }\n        const {\n          data,\n          error: userError\n        } = await _request(this.fetch, 'PUT', `${this.url}/user`, {\n          headers: this.headers,\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n          body: Object.assign(Object.assign({}, attributes), {\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          }),\n          jwt: session.access_token,\n          xform: _userResponse\n        });\n        if (userError) throw userError;\n        session.user = data.user;\n        await this._saveSession(session);\n        await this._notifyAllSubscribers('USER_UPDATED', session);\n        return {\n          data: {\n            user: session.user\n          },\n          error: null\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n   * If the refresh token or access token in the current session is invalid, an error will be thrown.\n   * @param currentSession The current session that minimally contains an access token and refresh token.\n   */\n  async setSession(currentSession) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._setSession(currentSession);\n    });\n  }\n  async _setSession(currentSession) {\n    try {\n      if (!currentSession.access_token || !currentSession.refresh_token) {\n        throw new AuthSessionMissingError();\n      }\n      const timeNow = Date.now() / 1000;\n      let expiresAt = timeNow;\n      let hasExpired = true;\n      let session = null;\n      const {\n        payload\n      } = decodeJWT(currentSession.access_token);\n      if (payload.exp) {\n        expiresAt = payload.exp;\n        hasExpired = expiresAt <= timeNow;\n      }\n      if (hasExpired) {\n        const {\n          session: refreshedSession,\n          error\n        } = await this._callRefreshToken(currentSession.refresh_token);\n        if (error) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: error\n          };\n        }\n        if (!refreshedSession) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: null\n          };\n        }\n        session = refreshedSession;\n      } else {\n        const {\n          data,\n          error\n        } = await this._getUser(currentSession.access_token);\n        if (error) {\n          throw error;\n        }\n        session = {\n          access_token: currentSession.access_token,\n          refresh_token: currentSession.refresh_token,\n          user: data.user,\n          token_type: 'bearer',\n          expires_in: expiresAt - timeNow,\n          expires_at: expiresAt\n        };\n        await this._saveSession(session);\n        await this._notifyAllSubscribers('SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user: session.user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            session: null,\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Returns a new session, regardless of expiry status.\n   * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n   * If the current session's refresh token is invalid, an error will be thrown.\n   * @param currentSession The current session. If passed in, it must contain a refresh token.\n   */\n  async refreshSession(currentSession) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._refreshSession(currentSession);\n    });\n  }\n  async _refreshSession(currentSession) {\n    try {\n      return await this._useSession(async result => {\n        var _a;\n        if (!currentSession) {\n          const {\n            data,\n            error\n          } = result;\n          if (error) {\n            throw error;\n          }\n          currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n        }\n        if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n          throw new AuthSessionMissingError();\n        }\n        const {\n          session,\n          error\n        } = await this._callRefreshToken(currentSession.refresh_token);\n        if (error) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: error\n          };\n        }\n        if (!session) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: null\n          };\n        }\n        return {\n          data: {\n            user: session.user,\n            session\n          },\n          error: null\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Gets the session data from a URL string\n   */\n  async _getSessionFromURL(params, callbackUrlType) {\n    try {\n      if (!isBrowser()) throw new AuthImplicitGrantRedirectError('No browser detected.');\n      // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n      if (params.error || params.error_description || params.error_code) {\n        // The error class returned implies that the redirect is from an implicit grant flow\n        // but it could also be from a redirect error from a PKCE flow.\n        throw new AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n          error: params.error || 'unspecified_error',\n          code: params.error_code || 'unspecified_code'\n        });\n      }\n      // Checks for mismatches between the flowType initialised in the client and the URL parameters\n      switch (callbackUrlType) {\n        case 'implicit':\n          if (this.flowType === 'pkce') {\n            throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n          }\n          break;\n        case 'pkce':\n          if (this.flowType === 'implicit') {\n            throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n          }\n          break;\n        default:\n        // there's no mismatch so we continue\n      }\n      // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n      if (callbackUrlType === 'pkce') {\n        this._debug('#_initialize()', 'begin', 'is PKCE flow', true);\n        if (!params.code) throw new AuthPKCEGrantCodeExchangeError('No code detected.');\n        const {\n          data,\n          error\n        } = await this._exchangeCodeForSession(params.code);\n        if (error) throw error;\n        const url = new URL(window.location.href);\n        url.searchParams.delete('code');\n        window.history.replaceState(window.history.state, '', url.toString());\n        return {\n          data: {\n            session: data.session,\n            redirectType: null\n          },\n          error: null\n        };\n      }\n      const {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        refresh_token,\n        expires_in,\n        expires_at,\n        token_type\n      } = params;\n      if (!access_token || !expires_in || !refresh_token || !token_type) {\n        throw new AuthImplicitGrantRedirectError('No session defined in URL');\n      }\n      const timeNow = Math.round(Date.now() / 1000);\n      const expiresIn = parseInt(expires_in);\n      let expiresAt = timeNow + expiresIn;\n      if (expires_at) {\n        expiresAt = parseInt(expires_at);\n      }\n      const actuallyExpiresIn = expiresAt - timeNow;\n      if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION_MS) {\n        console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n      }\n      const issuedAt = expiresAt - expiresIn;\n      if (timeNow - issuedAt >= 120) {\n        console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n      } else if (timeNow - issuedAt < 0) {\n        console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew', issuedAt, expiresAt, timeNow);\n      }\n      const {\n        data,\n        error\n      } = await this._getUser(access_token);\n      if (error) throw error;\n      const session = {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        expires_in: expiresIn,\n        expires_at: expiresAt,\n        refresh_token,\n        token_type,\n        user: data.user\n      };\n      // Remove tokens from URL\n      window.location.hash = '';\n      this._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n      return {\n        data: {\n          session,\n          redirectType: params.type\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            session: null,\n            redirectType: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n   */\n  _isImplicitGrantCallback(params) {\n    return Boolean(params.access_token || params.error_description);\n  }\n  /**\n   * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n   */\n  async _isPKCECallback(params) {\n    const currentStorageContent = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n    return !!(params.code && currentStorageContent);\n  }\n  /**\n   * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n   *\n   * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n   * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n   *\n   * If using `others` scope, no `SIGNED_OUT` event is fired!\n   */\n  async signOut(options = {\n    scope: 'global'\n  }) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._signOut(options);\n    });\n  }\n  async _signOut({\n    scope\n  } = {\n    scope: 'global'\n  }) {\n    return await this._useSession(async result => {\n      var _a;\n      const {\n        data,\n        error: sessionError\n      } = result;\n      if (sessionError) {\n        return {\n          error: sessionError\n        };\n      }\n      const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n      if (accessToken) {\n        const {\n          error\n        } = await this.admin.signOut(accessToken, scope);\n        if (error) {\n          // ignore 404s since user might not exist anymore\n          // ignore 401s since an invalid or expired JWT should sign out the current session\n          if (!(isAuthApiError(error) && (error.status === 404 || error.status === 401 || error.status === 403))) {\n            return {\n              error\n            };\n          }\n        }\n      }\n      if (scope !== 'others') {\n        await this._removeSession();\n        await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n      }\n      return {\n        error: null\n      };\n    });\n  }\n  /**\n   * Receive a notification every time an auth event happens.\n   * @param callback A callback function to be invoked when an auth event happens.\n   */\n  onAuthStateChange(callback) {\n    const id = uuid();\n    const subscription = {\n      id,\n      callback,\n      unsubscribe: () => {\n        this._debug('#unsubscribe()', 'state change callback with id removed', id);\n        this.stateChangeEmitters.delete(id);\n      }\n    };\n    this._debug('#onAuthStateChange()', 'registered callback with id', id);\n    this.stateChangeEmitters.set(id, subscription);\n    (async () => {\n      await this.initializePromise;\n      await this._acquireLock(-1, async () => {\n        this._emitInitialSession(id);\n      });\n    })();\n    return {\n      data: {\n        subscription\n      }\n    };\n  }\n  async _emitInitialSession(id) {\n    return await this._useSession(async result => {\n      var _a, _b;\n      try {\n        const {\n          data: {\n            session\n          },\n          error\n        } = result;\n        if (error) throw error;\n        await ((_a = this.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session));\n        this._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n      } catch (err) {\n        await ((_b = this.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null));\n        this._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n        console.error(err);\n      }\n    });\n  }\n  /**\n   * Sends a password reset request to an email address. This method supports the PKCE flow.\n   *\n   * @param email The email address of the user.\n   * @param options.redirectTo The URL to send the user to after they click the password reset link.\n   * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n   */\n  async resetPasswordForEmail(email, options = {}) {\n    let codeChallenge = null;\n    let codeChallengeMethod = null;\n    if (this.flowType === 'pkce') {\n      ;\n      [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey, true // isPasswordRecovery\n      );\n    }\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/recover`, {\n        body: {\n          email,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n          gotrue_meta_security: {\n            captcha_token: options.captchaToken\n          }\n        },\n        headers: this.headers,\n        redirectTo: options.redirectTo\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Gets all the identities linked to a user.\n   */\n  async getUserIdentities() {\n    var _a;\n    try {\n      const {\n        data,\n        error\n      } = await this.getUser();\n      if (error) throw error;\n      return {\n        data: {\n          identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : []\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Links an oauth identity to an existing user.\n   * This method supports the PKCE flow.\n   */\n  async linkIdentity(credentials) {\n    var _a;\n    try {\n      const {\n        data,\n        error\n      } = await this._useSession(async result => {\n        var _a, _b, _c, _d, _e;\n        const {\n          data,\n          error\n        } = result;\n        if (error) throw error;\n        const url = await this._getUrlForProvider(`${this.url}/user/identities/authorize`, credentials.provider, {\n          redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n          scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n          queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n          skipBrowserRedirect: true\n        });\n        return await _request(this.fetch, 'GET', url, {\n          headers: this.headers,\n          jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined\n        });\n      });\n      if (error) throw error;\n      if (isBrowser() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n        window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n      }\n      return {\n        data: {\n          provider: credentials.provider,\n          url: data === null || data === void 0 ? void 0 : data.url\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            provider: credentials.provider,\n            url: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n   */\n  async unlinkIdentity(identity) {\n    try {\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data,\n          error\n        } = result;\n        if (error) {\n          throw error;\n        }\n        return await _request(this.fetch, 'DELETE', `${this.url}/user/identities/${identity.identity_id}`, {\n          headers: this.headers,\n          jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined\n        });\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Generates a new JWT.\n   * @param refreshToken A valid refresh token that was returned on login.\n   */\n  async _refreshAccessToken(refreshToken) {\n    const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n    this._debug(debugName, 'begin');\n    try {\n      const startedAt = Date.now();\n      // will attempt to refresh the token with exponential backoff\n      return await retryable(async attempt => {\n        if (attempt > 0) {\n          await sleep(200 * Math.pow(2, attempt - 1)); // 200, 400, 800, ...\n        }\n        this._debug(debugName, 'refreshing attempt', attempt);\n        return await _request(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n          body: {\n            refresh_token: refreshToken\n          },\n          headers: this.headers,\n          xform: _sessionResponse\n        });\n      }, (attempt, error) => {\n        const nextBackOffInterval = 200 * Math.pow(2, attempt);\n        return error && isAuthRetryableFetchError(error) &&\n        // retryable only if the request can be sent before the backoff overflows the tick duration\n        Date.now() + nextBackOffInterval - startedAt < AUTO_REFRESH_TICK_DURATION_MS;\n      });\n    } catch (error) {\n      this._debug(debugName, 'error', error);\n      if (isAuthError(error)) {\n        return {\n          data: {\n            session: null,\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    } finally {\n      this._debug(debugName, 'end');\n    }\n  }\n  _isValidSession(maybeSession) {\n    const isValidSession = typeof maybeSession === 'object' && maybeSession !== null && 'access_token' in maybeSession && 'refresh_token' in maybeSession && 'expires_at' in maybeSession;\n    return isValidSession;\n  }\n  async _handleProviderSignIn(provider, options) {\n    const url = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n      redirectTo: options.redirectTo,\n      scopes: options.scopes,\n      queryParams: options.queryParams\n    });\n    this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n    // try to open on the browser\n    if (isBrowser() && !options.skipBrowserRedirect) {\n      window.location.assign(url);\n    }\n    return {\n      data: {\n        provider,\n        url\n      },\n      error: null\n    };\n  }\n  /**\n   * Recovers the session from LocalStorage and refreshes the token\n   * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n   */\n  async _recoverAndRefresh() {\n    var _a;\n    const debugName = '#_recoverAndRefresh()';\n    this._debug(debugName, 'begin');\n    try {\n      const currentSession = await getItemAsync(this.storage, this.storageKey);\n      this._debug(debugName, 'session from storage', currentSession);\n      if (!this._isValidSession(currentSession)) {\n        this._debug(debugName, 'session is not valid');\n        if (currentSession !== null) {\n          await this._removeSession();\n        }\n        return;\n      }\n      const expiresWithMargin = ((_a = currentSession.expires_at) !== null && _a !== void 0 ? _a : Infinity) * 1000 - Date.now() < EXPIRY_MARGIN_MS;\n      this._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN_MS}s`);\n      if (expiresWithMargin) {\n        if (this.autoRefreshToken && currentSession.refresh_token) {\n          const {\n            error\n          } = await this._callRefreshToken(currentSession.refresh_token);\n          if (error) {\n            console.error(error);\n            if (!isAuthRetryableFetchError(error)) {\n              this._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n              await this._removeSession();\n            }\n          }\n        }\n      } else {\n        // no need to persist currentSession again, as we just loaded it from\n        // local storage; persisting it again may overwrite a value saved by\n        // another client with access to the same local storage\n        await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n      }\n    } catch (err) {\n      this._debug(debugName, 'error', err);\n      console.error(err);\n      return;\n    } finally {\n      this._debug(debugName, 'end');\n    }\n  }\n  async _callRefreshToken(refreshToken) {\n    var _a, _b;\n    if (!refreshToken) {\n      throw new AuthSessionMissingError();\n    }\n    // refreshing is already in progress\n    if (this.refreshingDeferred) {\n      return this.refreshingDeferred.promise;\n    }\n    const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n    this._debug(debugName, 'begin');\n    try {\n      this.refreshingDeferred = new Deferred();\n      const {\n        data,\n        error\n      } = await this._refreshAccessToken(refreshToken);\n      if (error) throw error;\n      if (!data.session) throw new AuthSessionMissingError();\n      await this._saveSession(data.session);\n      await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n      const result = {\n        session: data.session,\n        error: null\n      };\n      this.refreshingDeferred.resolve(result);\n      return result;\n    } catch (error) {\n      this._debug(debugName, 'error', error);\n      if (isAuthError(error)) {\n        const result = {\n          session: null,\n          error\n        };\n        if (!isAuthRetryableFetchError(error)) {\n          await this._removeSession();\n        }\n        (_a = this.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n        return result;\n      }\n      (_b = this.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n      throw error;\n    } finally {\n      this.refreshingDeferred = null;\n      this._debug(debugName, 'end');\n    }\n  }\n  async _notifyAllSubscribers(event, session, broadcast = true) {\n    const debugName = `#_notifyAllSubscribers(${event})`;\n    this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n    try {\n      if (this.broadcastChannel && broadcast) {\n        this.broadcastChannel.postMessage({\n          event,\n          session\n        });\n      }\n      const errors = [];\n      const promises = Array.from(this.stateChangeEmitters.values()).map(async x => {\n        try {\n          await x.callback(event, session);\n        } catch (e) {\n          errors.push(e);\n        }\n      });\n      await Promise.all(promises);\n      if (errors.length > 0) {\n        for (let i = 0; i < errors.length; i += 1) {\n          console.error(errors[i]);\n        }\n        throw errors[0];\n      }\n    } finally {\n      this._debug(debugName, 'end');\n    }\n  }\n  /**\n   * set currentSession and currentUser\n   * process to _startAutoRefreshToken if possible\n   */\n  async _saveSession(session) {\n    this._debug('#_saveSession()', session);\n    // _saveSession is always called whenever a new session has been acquired\n    // so we can safely suppress the warning returned by future getSession calls\n    this.suppressGetSessionWarning = true;\n    await setItemAsync(this.storage, this.storageKey, session);\n  }\n  async _removeSession() {\n    this._debug('#_removeSession()');\n    await removeItemAsync(this.storage, this.storageKey);\n    await this._notifyAllSubscribers('SIGNED_OUT', null);\n  }\n  /**\n   * Removes any registered visibilitychange callback.\n   *\n   * {@see #startAutoRefresh}\n   * {@see #stopAutoRefresh}\n   */\n  _removeVisibilityChangedCallback() {\n    this._debug('#_removeVisibilityChangedCallback()');\n    const callback = this.visibilityChangedCallback;\n    this.visibilityChangedCallback = null;\n    try {\n      if (callback && isBrowser() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n        window.removeEventListener('visibilitychange', callback);\n      }\n    } catch (e) {\n      console.error('removing visibilitychange callback failed', e);\n    }\n  }\n  /**\n   * This is the private implementation of {@link #startAutoRefresh}. Use this\n   * within the library.\n   */\n  async _startAutoRefresh() {\n    await this._stopAutoRefresh();\n    this._debug('#_startAutoRefresh()');\n    const ticker = setInterval(() => this._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION_MS);\n    this.autoRefreshTicker = ticker;\n    if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n      // ticker is a NodeJS Timeout object that has an `unref` method\n      // https://nodejs.org/api/timers.html#timeoutunref\n      // When auto refresh is used in NodeJS (like for testing) the\n      // `setInterval` is preventing the process from being marked as\n      // finished and tests run endlessly. This can be prevented by calling\n      // `unref()` on the returned object.\n      ticker.unref();\n      // @ts-expect-error TS has no context of Deno\n    } else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n      // similar like for NodeJS, but with the Deno API\n      // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n      // @ts-expect-error TS has no context of Deno\n      Deno.unrefTimer(ticker);\n    }\n    // run the tick immediately, but in the next pass of the event loop so that\n    // #_initialize can be allowed to complete without recursively waiting on\n    // itself\n    setTimeout(async () => {\n      await this.initializePromise;\n      await this._autoRefreshTokenTick();\n    }, 0);\n  }\n  /**\n   * This is the private implementation of {@link #stopAutoRefresh}. Use this\n   * within the library.\n   */\n  async _stopAutoRefresh() {\n    this._debug('#_stopAutoRefresh()');\n    const ticker = this.autoRefreshTicker;\n    this.autoRefreshTicker = null;\n    if (ticker) {\n      clearInterval(ticker);\n    }\n  }\n  /**\n   * Starts an auto-refresh process in the background. The session is checked\n   * every few seconds. Close to the time of expiration a process is started to\n   * refresh the session. If refreshing fails it will be retried for as long as\n   * necessary.\n   *\n   * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n   * to call this function, it will be called for you.\n   *\n   * On browsers the refresh process works only when the tab/window is in the\n   * foreground to conserve resources as well as prevent race conditions and\n   * flooding auth with requests. If you call this method any managed\n   * visibility change callback will be removed and you must manage visibility\n   * changes on your own.\n   *\n   * On non-browser platforms the refresh process works *continuously* in the\n   * background, which may not be desirable. You should hook into your\n   * platform's foreground indication mechanism and call these methods\n   * appropriately to conserve resources.\n   *\n   * {@see #stopAutoRefresh}\n   */\n  async startAutoRefresh() {\n    this._removeVisibilityChangedCallback();\n    await this._startAutoRefresh();\n  }\n  /**\n   * Stops an active auto refresh process running in the background (if any).\n   *\n   * If you call this method any managed visibility change callback will be\n   * removed and you must manage visibility changes on your own.\n   *\n   * See {@link #startAutoRefresh} for more details.\n   */\n  async stopAutoRefresh() {\n    this._removeVisibilityChangedCallback();\n    await this._stopAutoRefresh();\n  }\n  /**\n   * Runs the auto refresh token tick.\n   */\n  async _autoRefreshTokenTick() {\n    this._debug('#_autoRefreshTokenTick()', 'begin');\n    try {\n      await this._acquireLock(0, async () => {\n        try {\n          const now = Date.now();\n          try {\n            return await this._useSession(async result => {\n              const {\n                data: {\n                  session\n                }\n              } = result;\n              if (!session || !session.refresh_token || !session.expires_at) {\n                this._debug('#_autoRefreshTokenTick()', 'no session');\n                return;\n              }\n              // session will expire in this many ticks (or has already expired if <= 0)\n              const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION_MS);\n              this._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n              if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                await this._callRefreshToken(session.refresh_token);\n              }\n            });\n          } catch (e) {\n            console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n          }\n        } finally {\n          this._debug('#_autoRefreshTokenTick()', 'end');\n        }\n      });\n    } catch (e) {\n      if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n        this._debug('auto refresh token tick lock not available');\n      } else {\n        throw e;\n      }\n    }\n  }\n  /**\n   * Registers callbacks on the browser / platform, which in-turn run\n   * algorithms when the browser window/tab are in foreground. On non-browser\n   * platforms it assumes always foreground.\n   */\n  async _handleVisibilityChange() {\n    this._debug('#_handleVisibilityChange()');\n    if (!isBrowser() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n      if (this.autoRefreshToken) {\n        // in non-browser environments the refresh token ticker runs always\n        this.startAutoRefresh();\n      }\n      return false;\n    }\n    try {\n      this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false);\n      window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', this.visibilityChangedCallback);\n      // now immediately call the visbility changed callback to setup with the\n      // current visbility state\n      await this._onVisibilityChanged(true); // initial call\n    } catch (error) {\n      console.error('_handleVisibilityChange', error);\n    }\n  }\n  /**\n   * Callback registered with `window.addEventListener('visibilitychange')`.\n   */\n  async _onVisibilityChanged(calledFromInitialize) {\n    const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n    this._debug(methodName, 'visibilityState', document.visibilityState);\n    if (document.visibilityState === 'visible') {\n      if (this.autoRefreshToken) {\n        // in browser environments the refresh token ticker runs only on focused tabs\n        // which prevents race conditions\n        this._startAutoRefresh();\n      }\n      if (!calledFromInitialize) {\n        // called when the visibility has changed, i.e. the browser\n        // transitioned from hidden -> visible so we need to see if the session\n        // should be recovered immediately... but to do that we need to acquire\n        // the lock first asynchronously\n        await this.initializePromise;\n        await this._acquireLock(-1, async () => {\n          if (document.visibilityState !== 'visible') {\n            this._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n            // visibility has changed while waiting for the lock, abort\n            return;\n          }\n          // recover the session\n          await this._recoverAndRefresh();\n        });\n      }\n    } else if (document.visibilityState === 'hidden') {\n      if (this.autoRefreshToken) {\n        this._stopAutoRefresh();\n      }\n    }\n  }\n  /**\n   * Generates the relevant login URL for a third-party provider.\n   * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n   * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n   * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n   */\n  async _getUrlForProvider(url, provider, options) {\n    const urlParams = [`provider=${encodeURIComponent(provider)}`];\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n      urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n    }\n    if (options === null || options === void 0 ? void 0 : options.scopes) {\n      urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n    }\n    if (this.flowType === 'pkce') {\n      const [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(this.storage, this.storageKey);\n      const flowParams = new URLSearchParams({\n        code_challenge: `${encodeURIComponent(codeChallenge)}`,\n        code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`\n      });\n      urlParams.push(flowParams.toString());\n    }\n    if (options === null || options === void 0 ? void 0 : options.queryParams) {\n      const query = new URLSearchParams(options.queryParams);\n      urlParams.push(query.toString());\n    }\n    if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n      urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n    }\n    return `${url}?${urlParams.join('&')}`;\n  }\n  async _unenroll(params) {\n    try {\n      return await this._useSession(async result => {\n        var _a;\n        const {\n          data: sessionData,\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          return {\n            data: null,\n            error: sessionError\n          };\n        }\n        return await _request(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n          headers: this.headers,\n          jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n        });\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  async _enroll(params) {\n    try {\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data: sessionData,\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          return {\n            data: null,\n            error: sessionError\n          };\n        }\n        const body = Object.assign({\n          friendly_name: params.friendlyName,\n          factor_type: params.factorType\n        }, params.factorType === 'phone' ? {\n          phone: params.phone\n        } : {\n          issuer: params.issuer\n        });\n        const {\n          data,\n          error\n        } = await _request(this.fetch, 'POST', `${this.url}/factors`, {\n          body,\n          headers: this.headers,\n          jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n        });\n        if (error) {\n          return {\n            data: null,\n            error\n          };\n        }\n        if (params.factorType === 'totp' && ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code)) {\n          data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n        }\n        return {\n          data,\n          error: null\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * {@see GoTrueMFAApi#verify}\n   */\n  async _verify(params) {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async result => {\n          var _a;\n          const {\n            data: sessionData,\n            error: sessionError\n          } = result;\n          if (sessionError) {\n            return {\n              data: null,\n              error: sessionError\n            };\n          }\n          const {\n            data,\n            error\n          } = await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/verify`, {\n            body: {\n              code: params.code,\n              challenge_id: params.challengeId\n            },\n            headers: this.headers,\n            jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n          });\n          if (error) {\n            return {\n              data: null,\n              error\n            };\n          }\n          await this._saveSession(Object.assign({\n            expires_at: Math.round(Date.now() / 1000) + data.expires_in\n          }, data));\n          await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n          return {\n            data,\n            error\n          };\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * {@see GoTrueMFAApi#challenge}\n   */\n  async _challenge(params) {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async result => {\n          var _a;\n          const {\n            data: sessionData,\n            error: sessionError\n          } = result;\n          if (sessionError) {\n            return {\n              data: null,\n              error: sessionError\n            };\n          }\n          return await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/challenge`, {\n            body: {\n              channel: params.channel\n            },\n            headers: this.headers,\n            jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n          });\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * {@see GoTrueMFAApi#challengeAndVerify}\n   */\n  async _challengeAndVerify(params) {\n    // both _challenge and _verify independently acquire the lock, so no need\n    // to acquire it here\n    const {\n      data: challengeData,\n      error: challengeError\n    } = await this._challenge({\n      factorId: params.factorId\n    });\n    if (challengeError) {\n      return {\n        data: null,\n        error: challengeError\n      };\n    }\n    return await this._verify({\n      factorId: params.factorId,\n      challengeId: challengeData.id,\n      code: params.code\n    });\n  }\n  /**\n   * {@see GoTrueMFAApi#listFactors}\n   */\n  async _listFactors() {\n    // use #getUser instead of #_getUser as the former acquires a lock\n    const {\n      data: {\n        user\n      },\n      error: userError\n    } = await this.getUser();\n    if (userError) {\n      return {\n        data: null,\n        error: userError\n      };\n    }\n    const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n    const totp = factors.filter(factor => factor.factor_type === 'totp' && factor.status === 'verified');\n    const phone = factors.filter(factor => factor.factor_type === 'phone' && factor.status === 'verified');\n    return {\n      data: {\n        all: factors,\n        totp,\n        phone\n      },\n      error: null\n    };\n  }\n  /**\n   * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   */\n  async _getAuthenticatorAssuranceLevel() {\n    return this._acquireLock(-1, async () => {\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data: {\n            session\n          },\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          return {\n            data: null,\n            error: sessionError\n          };\n        }\n        if (!session) {\n          return {\n            data: {\n              currentLevel: null,\n              nextLevel: null,\n              currentAuthenticationMethods: []\n            },\n            error: null\n          };\n        }\n        const {\n          payload\n        } = decodeJWT(session.access_token);\n        let currentLevel = null;\n        if (payload.aal) {\n          currentLevel = payload.aal;\n        }\n        let nextLevel = currentLevel;\n        const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter(factor => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n        if (verifiedFactors.length > 0) {\n          nextLevel = 'aal2';\n        }\n        const currentAuthenticationMethods = payload.amr || [];\n        return {\n          data: {\n            currentLevel,\n            nextLevel,\n            currentAuthenticationMethods\n          },\n          error: null\n        };\n      });\n    });\n  }\n  async fetchJwk(kid, jwks = {\n    keys: []\n  }) {\n    // try fetching from the supplied jwks\n    let jwk = jwks.keys.find(key => key.kid === kid);\n    if (jwk) {\n      return jwk;\n    }\n    // try fetching from cache\n    jwk = this.jwks.keys.find(key => key.kid === kid);\n    // jwk exists and jwks isn't stale\n    if (jwk && this.jwks_cached_at + JWKS_TTL > Date.now()) {\n      return jwk;\n    }\n    // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n    const {\n      data,\n      error\n    } = await _request(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n      headers: this.headers\n    });\n    if (error) {\n      throw error;\n    }\n    if (!data.keys || data.keys.length === 0) {\n      throw new AuthInvalidJwtError('JWKS is empty');\n    }\n    this.jwks = data;\n    this.jwks_cached_at = Date.now();\n    // Find the signing key\n    jwk = data.keys.find(key => key.kid === kid);\n    if (!jwk) {\n      throw new AuthInvalidJwtError('No matching signing key found in JWKS');\n    }\n    return jwk;\n  }\n  /**\n   * @experimental This method may change in future versions.\n   * @description Gets the claims from a JWT. If the JWT is symmetric JWTs, it will call getUser() to verify against the server. If the JWT is asymmetric, it will be verified against the JWKS using the WebCrypto API.\n   */\n  async getClaims(jwt, jwks = {\n    keys: []\n  }) {\n    try {\n      let token = jwt;\n      if (!token) {\n        const {\n          data,\n          error\n        } = await this.getSession();\n        if (error || !data.session) {\n          return {\n            data: null,\n            error\n          };\n        }\n        token = data.session.access_token;\n      }\n      const {\n        header,\n        payload,\n        signature,\n        raw: {\n          header: rawHeader,\n          payload: rawPayload\n        }\n      } = decodeJWT(token);\n      // Reject expired JWTs\n      validateExp(payload.exp);\n      // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n      if (!header.kid || header.alg === 'HS256' || !('crypto' in globalThis && 'subtle' in globalThis.crypto)) {\n        const {\n          error\n        } = await this.getUser(token);\n        if (error) {\n          throw error;\n        }\n        // getUser succeeds so the claims in the JWT can be trusted\n        return {\n          data: {\n            claims: payload,\n            header,\n            signature\n          },\n          error: null\n        };\n      }\n      const algorithm = getAlgorithm(header.alg);\n      const signingKey = await this.fetchJwk(header.kid, jwks);\n      // Convert JWK to CryptoKey\n      const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, ['verify']);\n      // Verify the signature\n      const isValid = await crypto.subtle.verify(algorithm, publicKey, signature, stringToUint8Array(`${rawHeader}.${rawPayload}`));\n      if (!isValid) {\n        throw new AuthInvalidJwtError('Invalid JWT signature');\n      }\n      // If verification succeeds, decode and return claims\n      return {\n        data: {\n          claims: payload,\n          header,\n          signature\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n}\nGoTrueClient.nextInstanceID = 0;", "map": {"version": 3, "names": ["GoTrueAdminApi", "DEFAULT_HEADERS", "EXPIRY_MARGIN_MS", "AUTO_REFRESH_TICK_DURATION_MS", "AUTO_REFRESH_TICK_THRESHOLD", "GOTRUE_URL", "STORAGE_KEY", "JWKS_TTL", "AuthImplicitGrantRedirectError", "AuthPKCEGrantCodeExchangeError", "AuthInvalidCredentialsError", "AuthSessionMissingError", "AuthInvalidTokenResponseError", "AuthUnknownError", "isAuthApiError", "isAuthError", "isAuthRetryableFetchError", "isAuthSessionMissingError", "isAuthImplicitGrantRedirectError", "AuthInvalidJwtError", "_request", "_sessionResponse", "_sessionResponsePassword", "_userResponse", "_ssoResponse", "Deferred", "getItemAsync", "<PERSON><PERSON><PERSON><PERSON>", "removeItemAsync", "resolveFetch", "setItemAsync", "uuid", "retryable", "sleep", "supportsLocalStorage", "parseParametersFromURL", "getCodeChallengeAndMethod", "getAlgorithm", "validateExp", "decodeJWT", "localStorageAdapter", "memoryLocalStorageAdapter", "polyfillGlobalThis", "version", "LockAcquireTimeoutError", "navigator<PERSON><PERSON>", "stringToUint8Array", "DEFAULT_OPTIONS", "url", "storageKey", "autoRefreshToken", "persistSession", "detectSessionInUrl", "headers", "flowType", "debug", "hasCustomAuthorizationHeader", "lockNoOp", "name", "acquireTimeout", "fn", "GoTrueClient", "constructor", "options", "memoryStorage", "stateChangeEmitters", "Map", "autoRefreshTicker", "visibilityChangedCallback", "refreshing<PERSON><PERSON>erred", "initializePromise", "suppressGetSessionWarning", "lockAcquired", "pendingInLock", "broadcastChannel", "logger", "console", "log", "instanceID", "nextInstanceID", "warn", "settings", "Object", "assign", "logDebugMessages", "admin", "fetch", "lock", "_a", "globalThis", "navigator", "locks", "jwks", "keys", "jwks_cached_at", "Number", "MIN_SAFE_INTEGER", "mfa", "verify", "_verify", "bind", "enroll", "_enroll", "unenroll", "_unenroll", "challenge", "_challenge", "listFactors", "_listFactors", "challengeAndVerify", "_challengeAndVerify", "getAuthenticatorAssuranceLevel", "_getAuthenticatorAssuranceLevel", "storage", "BroadcastChannel", "e", "error", "_b", "addEventListener", "event", "_debug", "_notifyAllSubscribers", "data", "session", "initialize", "args", "Date", "toISOString", "_acquireLock", "_initialize", "params", "window", "location", "href", "callbackUrlType", "_isImplicitGrantCallback", "_isPKCECallback", "_getSessionFromURL", "errorCode", "details", "code", "_removeSession", "redirectType", "_saveSession", "setTimeout", "_recoverAndRefresh", "_handleVisibilityChange", "signInAnonymously", "credentials", "res", "body", "gotrue_meta_security", "captcha_token", "_c", "captchaToken", "xform", "user", "signUp", "email", "password", "codeChallenge", "codeChallengeMethod", "redirectTo", "emailRedirectTo", "code_challenge", "code_challenge_method", "phone", "channel", "signInWithPassword", "weak_password", "weakPassword", "signInWithOAuth", "_handleProviderSignIn", "provider", "scopes", "queryParams", "skipBrowserRedirect", "_d", "exchangeCodeForSession", "authCode", "_exchangeCodeForSession", "storageItem", "codeVerifier", "split", "auth_code", "code_verifier", "signInWithIdToken", "token", "access_token", "nonce", "id_token", "signInWithOtp", "create_user", "shouldCreateUser", "_e", "messageId", "message_id", "verifyOtp", "undefined", "Error", "type", "signInWithSSO", "provider_id", "providerId", "domain", "redirect_to", "skip_http_redirect", "reauthenticate", "_reauthenticate", "_useSession", "result", "sessionError", "jwt", "resend", "endpoint", "getSession", "last", "length", "Promise", "resolve", "push", "waitOn", "all", "splice", "__loadSession", "stack", "currentSession", "maybeSession", "_isValidSession", "hasExpired", "expires_at", "now", "isServer", "suppressWarning", "proxySession", "Proxy", "get", "target", "prop", "receiver", "Reflect", "_callRefreshToken", "refresh_token", "getUser", "_getUser", "updateUser", "attributes", "_updateUser", "sessionData", "userError", "setSession", "_setSession", "timeNow", "expiresAt", "payload", "exp", "refreshedSession", "token_type", "expires_in", "refreshSession", "_refreshSession", "error_description", "error_code", "URL", "searchParams", "delete", "history", "replaceState", "state", "toString", "provider_token", "provider_refresh_token", "Math", "round", "expiresIn", "parseInt", "actuallyExpiresIn", "issuedAt", "hash", "Boolean", "currentStorageContent", "signOut", "scope", "_signOut", "accessToken", "status", "onAuthStateChange", "callback", "id", "subscription", "unsubscribe", "set", "_emitInitialSession", "err", "resetPasswordForEmail", "getUserIdentities", "identities", "linkIdentity", "_getUrl<PERSON><PERSON><PERSON><PERSON><PERSON>", "unlinkIdentity", "identity", "identity_id", "_refreshAccessToken", "refreshToken", "debugName", "substring", "startedAt", "attempt", "pow", "nextBackOffInterval", "isValidSession", "expiresWithMargin", "Infinity", "promise", "reject", "broadcast", "postMessage", "errors", "promises", "Array", "from", "values", "map", "x", "i", "_removeVisibilityChangedCallback", "removeEventListener", "_startAutoRefresh", "_stopAutoRefresh", "ticker", "setInterval", "_autoRefreshTokenTick", "unref", "<PERSON><PERSON>", "unrefTimer", "clearInterval", "startAutoRefresh", "stopAutoRefresh", "expiresInTicks", "floor", "isAcquireTimeout", "_onVisibilityChanged", "calledFromInitialize", "methodName", "document", "visibilityState", "urlParams", "encodeURIComponent", "flowParams", "URLSearchParams", "query", "join", "factorId", "friendly_name", "friendlyName", "factor_type", "factorType", "issuer", "totp", "qr_code", "challenge_id", "challengeId", "challengeData", "challengeError", "factors", "filter", "factor", "currentLevel", "nextLevel", "currentAuthenticationMethods", "aal", "verifiedFactors", "amr", "fetchJwk", "kid", "jwk", "find", "key", "getClaims", "header", "signature", "raw", "<PERSON><PERSON><PERSON><PERSON>", "rawPayload", "alg", "crypto", "claims", "algorithm", "<PERSON><PERSON><PERSON>", "public<PERSON>ey", "subtle", "importKey", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(6)\\aich(5)\\node_modules\\@supabase\\auth-js\\src\\GoTrueClient.ts"], "sourcesContent": ["import GoTrueAdmin<PERSON>pi from './GoTrueAdminApi'\nimport {\n  DEFAULT_HEADERS,\n  EXPIRY_MARGIN_MS,\n  AUTO_REFRESH_TICK_DURATION_MS,\n  AUTO_REFRESH_TICK_THRESHOLD,\n  GOTRUE_URL,\n  STORAGE_KEY,\n  J<PERSON><PERSON>_TTL,\n} from './lib/constants'\nimport {\n  AuthError,\n  AuthImplicitGrantRedirectError,\n  AuthPKCEGrantCodeExchangeError,\n  AuthInvalidCredentialsError,\n  AuthSessionMissingError,\n  AuthInvalidTokenResponseError,\n  AuthUnknownError,\n  isAuthApiError,\n  isAuthError,\n  isAuthRetryableFetchError,\n  isAuthSessionMissingError,\n  isAuthImplicitGrantRedirectError,\n  AuthInvalidJwtError,\n} from './lib/errors'\nimport {\n  Fetch,\n  _request,\n  _sessionResponse,\n  _sessionResponsePassword,\n  _userResponse,\n  _ssoResponse,\n} from './lib/fetch'\nimport {\n  Deferred,\n  getItemAsync,\n  isBrowser,\n  removeItemAsync,\n  resolveFetch,\n  setItemAsync,\n  uuid,\n  retryable,\n  sleep,\n  supportsLocalStorage,\n  parseParametersFromURL,\n  getCodeChallengeAndMethod,\n  getAlgorithm,\n  validateExp,\n  decodeJWT,\n} from './lib/helpers'\nimport { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage'\nimport { polyfillGlobalThis } from './lib/polyfills'\nimport { version } from './lib/version'\nimport { LockAcquireTimeoutError, navigatorLock } from './lib/locks'\n\nimport type {\n  AuthChangeEvent,\n  AuthResponse,\n  AuthResponsePassword,\n  AuthTokenResponse,\n  AuthTokenResponsePassword,\n  AuthOtpResponse,\n  CallRefreshTokenResult,\n  GoTrueClientOptions,\n  InitializeResult,\n  OAuthResponse,\n  SSOResponse,\n  Provider,\n  Session,\n  SignInWithIdTokenCredentials,\n  SignInWithOAuthCredentials,\n  SignInWithPasswordCredentials,\n  SignInWithPasswordlessCredentials,\n  SignUpWithPasswordCredentials,\n  SignInWithSSO,\n  SignOut,\n  Subscription,\n  SupportedStorage,\n  User,\n  UserAttributes,\n  UserResponse,\n  VerifyOtpParams,\n  GoTrueMFAApi,\n  MFAEnrollParams,\n  AuthMFAEnrollResponse,\n  MFAChallengeParams,\n  AuthMFAChallengeResponse,\n  MFAUnenrollParams,\n  AuthMFAUnenrollResponse,\n  MFAVerifyParams,\n  AuthMFAVerifyResponse,\n  AuthMFAListFactorsResponse,\n  AuthMFAGetAuthenticatorAssuranceLevelResponse,\n  AuthenticatorAssuranceLevels,\n  Factor,\n  MFAChallengeAndVerifyParams,\n  ResendParams,\n  AuthFlowType,\n  LockFunc,\n  UserIdentity,\n  SignInAnonymouslyCredentials,\n  MFAEnrollTOTPParams,\n  MFAEnrollPhoneParams,\n  AuthMFAEnrollTOTPResponse,\n  AuthMFAEnrollPhoneResponse,\n  JWK,\n  JwtPayload,\n  JwtHeader,\n} from './lib/types'\nimport { stringToUint8Array } from './lib/base64url'\n\npolyfillGlobalThis() // Make \"globalThis\" available\n\nconst DEFAULT_OPTIONS: Omit<Required<GoTrueClientOptions>, 'fetch' | 'storage' | 'lock'> = {\n  url: GOTRUE_URL,\n  storageKey: STORAGE_KEY,\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  headers: DEFAULT_HEADERS,\n  flowType: 'implicit',\n  debug: false,\n  hasCustomAuthorizationHeader: false,\n}\n\nasync function lockNoOp<R>(name: string, acquireTimeout: number, fn: () => Promise<R>): Promise<R> {\n  return await fn()\n}\n\nexport default class GoTrueClient {\n  private static nextInstanceID = 0\n\n  private instanceID: number\n\n  /**\n   * Namespace for the GoTrue admin methods.\n   * These methods should only be used in a trusted server-side environment.\n   */\n  admin: GoTrueAdminApi\n  /**\n   * Namespace for the MFA methods.\n   */\n  mfa: GoTrueMFAApi\n  /**\n   * The storage key used to identify the values saved in localStorage\n   */\n  protected storageKey: string\n\n  protected flowType: AuthFlowType\n  /**\n   * The JWKS used for verifying asymmetric JWTs\n   */\n  protected jwks: { keys: JWK[] }\n  protected jwks_cached_at: number\n  protected autoRefreshToken: boolean\n  protected persistSession: boolean\n  protected storage: SupportedStorage\n  protected memoryStorage: { [key: string]: string } | null = null\n  protected stateChangeEmitters: Map<string, Subscription> = new Map()\n  protected autoRefreshTicker: ReturnType<typeof setInterval> | null = null\n  protected visibilityChangedCallback: (() => Promise<any>) | null = null\n  protected refreshingDeferred: Deferred<CallRefreshTokenResult> | null = null\n  /**\n   * Keeps track of the async client initialization.\n   * When null or not yet resolved the auth state is `unknown`\n   * Once resolved the the auth state is known and it's save to call any further client methods.\n   * Keep extra care to never reject or throw uncaught errors\n   */\n  protected initializePromise: Promise<InitializeResult> | null = null\n  protected detectSessionInUrl = true\n  protected url: string\n  protected headers: {\n    [key: string]: string\n  }\n  protected hasCustomAuthorizationHeader = false\n  protected suppressGetSessionWarning = false\n  protected fetch: Fetch\n  protected lock: LockFunc\n  protected lockAcquired = false\n  protected pendingInLock: Promise<any>[] = []\n\n  /**\n   * Used to broadcast state change events to other tabs listening.\n   */\n  protected broadcastChannel: BroadcastChannel | null = null\n\n  protected logDebugMessages: boolean\n  protected logger: (message: string, ...args: any[]) => void = console.log\n\n  /**\n   * Create a new client for use in the browser.\n   */\n  constructor(options: GoTrueClientOptions) {\n    this.instanceID = GoTrueClient.nextInstanceID\n    GoTrueClient.nextInstanceID += 1\n\n    if (this.instanceID > 0 && isBrowser()) {\n      console.warn(\n        'Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.'\n      )\n    }\n\n    const settings = { ...DEFAULT_OPTIONS, ...options }\n\n    this.logDebugMessages = !!settings.debug\n    if (typeof settings.debug === 'function') {\n      this.logger = settings.debug\n    }\n\n    this.persistSession = settings.persistSession\n    this.storageKey = settings.storageKey\n    this.autoRefreshToken = settings.autoRefreshToken\n    this.admin = new GoTrueAdminApi({\n      url: settings.url,\n      headers: settings.headers,\n      fetch: settings.fetch,\n    })\n\n    this.url = settings.url\n    this.headers = settings.headers\n    this.fetch = resolveFetch(settings.fetch)\n    this.lock = settings.lock || lockNoOp\n    this.detectSessionInUrl = settings.detectSessionInUrl\n    this.flowType = settings.flowType\n    this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader\n\n    if (settings.lock) {\n      this.lock = settings.lock\n    } else if (isBrowser() && globalThis?.navigator?.locks) {\n      this.lock = navigatorLock\n    } else {\n      this.lock = lockNoOp\n    }\n    this.jwks = { keys: [] }\n    this.jwks_cached_at = Number.MIN_SAFE_INTEGER\n    this.mfa = {\n      verify: this._verify.bind(this),\n      enroll: this._enroll.bind(this),\n      unenroll: this._unenroll.bind(this),\n      challenge: this._challenge.bind(this),\n      listFactors: this._listFactors.bind(this),\n      challengeAndVerify: this._challengeAndVerify.bind(this),\n      getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n    }\n\n    if (this.persistSession) {\n      if (settings.storage) {\n        this.storage = settings.storage\n      } else {\n        if (supportsLocalStorage()) {\n          this.storage = localStorageAdapter\n        } else {\n          this.memoryStorage = {}\n          this.storage = memoryLocalStorageAdapter(this.memoryStorage)\n        }\n      }\n    } else {\n      this.memoryStorage = {}\n      this.storage = memoryLocalStorageAdapter(this.memoryStorage)\n    }\n\n    if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n      try {\n        this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey)\n      } catch (e: any) {\n        console.error(\n          'Failed to create a new BroadcastChannel, multi-tab state changes will not be available',\n          e\n        )\n      }\n\n      this.broadcastChannel?.addEventListener('message', async (event) => {\n        this._debug('received broadcast notification from other tab or client', event)\n\n        await this._notifyAllSubscribers(event.data.event, event.data.session, false) // broadcast = false so we don't get an endless loop of messages\n      })\n    }\n\n    this.initialize()\n  }\n\n  private _debug(...args: any[]): GoTrueClient {\n    if (this.logDebugMessages) {\n      this.logger(\n        `GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`,\n        ...args\n      )\n    }\n\n    return this\n  }\n\n  /**\n   * Initializes the client session either from the url or from storage.\n   * This method is automatically called when instantiating the client, but should also be called\n   * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n   */\n  async initialize(): Promise<InitializeResult> {\n    if (this.initializePromise) {\n      return await this.initializePromise\n    }\n\n    this.initializePromise = (async () => {\n      return await this._acquireLock(-1, async () => {\n        return await this._initialize()\n      })\n    })()\n\n    return await this.initializePromise\n  }\n\n  /**\n   * IMPORTANT:\n   * 1. Never throw in this method, as it is called from the constructor\n   * 2. Never return a session from this method as it would be cached over\n   *    the whole lifetime of the client\n   */\n  private async _initialize(): Promise<InitializeResult> {\n    try {\n      const params = parseParametersFromURL(window.location.href)\n      let callbackUrlType = 'none'\n      if (this._isImplicitGrantCallback(params)) {\n        callbackUrlType = 'implicit'\n      } else if (await this._isPKCECallback(params)) {\n        callbackUrlType = 'pkce'\n      }\n\n      /**\n       * Attempt to get the session from the URL only if these conditions are fulfilled\n       *\n       * Note: If the URL isn't one of the callback url types (implicit or pkce),\n       * then there could be an existing session so we don't want to prematurely remove it\n       */\n      if (isBrowser() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n        const { data, error } = await this._getSessionFromURL(params, callbackUrlType)\n        if (error) {\n          this._debug('#_initialize()', 'error detecting session from URL', error)\n\n          if (isAuthImplicitGrantRedirectError(error)) {\n            const errorCode = error.details?.code\n            if (\n              errorCode === 'identity_already_exists' ||\n              errorCode === 'identity_not_found' ||\n              errorCode === 'single_identity_not_deletable'\n            ) {\n              return { error }\n            }\n          }\n\n          // failed login attempt via url,\n          // remove old session as in verifyOtp, signUp and signInWith*\n          await this._removeSession()\n\n          return { error }\n        }\n\n        const { session, redirectType } = data\n\n        this._debug(\n          '#_initialize()',\n          'detected session in URL',\n          session,\n          'redirect type',\n          redirectType\n        )\n\n        await this._saveSession(session)\n\n        setTimeout(async () => {\n          if (redirectType === 'recovery') {\n            await this._notifyAllSubscribers('PASSWORD_RECOVERY', session)\n          } else {\n            await this._notifyAllSubscribers('SIGNED_IN', session)\n          }\n        }, 0)\n\n        return { error: null }\n      }\n      // no login attempt via callback url try to recover session from storage\n      await this._recoverAndRefresh()\n      return { error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { error }\n      }\n\n      return {\n        error: new AuthUnknownError('Unexpected error during initialization', error),\n      }\n    } finally {\n      await this._handleVisibilityChange()\n      this._debug('#_initialize()', 'end')\n    }\n  }\n\n  /**\n   * Creates a new anonymous user.\n   *\n   * @returns A session where the is_anonymous claim in the access token JWT set to true\n   */\n  async signInAnonymously(credentials?: SignInAnonymouslyCredentials): Promise<AuthResponse> {\n    try {\n      const res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n        headers: this.headers,\n        body: {\n          data: credentials?.options?.data ?? {},\n          gotrue_meta_security: { captcha_token: credentials?.options?.captchaToken },\n        },\n        xform: _sessionResponse,\n      })\n      const { data, error } = res\n\n      if (error || !data) {\n        return { data: { user: null, session: null }, error: error }\n      }\n      const session: Session | null = data.session\n      const user: User | null = data.user\n\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a new user.\n   *\n   * Be aware that if a user account exists in the system you may get back an\n   * error message that attempts to hide this information from the user.\n   * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n   *\n   * @returns A logged-in session if the server has \"autoconfirm\" ON\n   * @returns A user if the server has \"autoconfirm\" OFF\n   */\n  async signUp(credentials: SignUpWithPasswordCredentials): Promise<AuthResponse> {\n    try {\n      let res: AuthResponse\n      if ('email' in credentials) {\n        const { email, password, options } = credentials\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce') {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          redirectTo: options?.emailRedirectTo,\n          body: {\n            email,\n            password,\n            data: options?.data ?? {},\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          xform: _sessionResponse,\n        })\n      } else if ('phone' in credentials) {\n        const { phone, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            data: options?.data ?? {},\n            channel: options?.channel ?? 'sms',\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponse,\n        })\n      } else {\n        throw new AuthInvalidCredentialsError(\n          'You must provide either an email or phone number and a password'\n        )\n      }\n\n      const { data, error } = res\n\n      if (error || !data) {\n        return { data: { user: null, session: null }, error: error }\n      }\n\n      const session: Session | null = data.session\n      const user: User | null = data.user\n\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Log in an existing user with an email and password or phone and password.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or that the\n   * email/phone and password combination is wrong or that the account can only\n   * be accessed via social login.\n   */\n  async signInWithPassword(\n    credentials: SignInWithPasswordCredentials\n  ): Promise<AuthTokenResponsePassword> {\n    try {\n      let res: AuthResponsePassword\n      if ('email' in credentials) {\n        const { email, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            email,\n            password,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponsePassword,\n        })\n      } else if ('phone' in credentials) {\n        const { phone, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponsePassword,\n        })\n      } else {\n        throw new AuthInvalidCredentialsError(\n          'You must provide either an email or phone number and a password'\n        )\n      }\n      const { data, error } = res\n\n      if (error) {\n        return { data: { user: null, session: null }, error }\n      } else if (!data || !data.session || !data.user) {\n        return { data: { user: null, session: null }, error: new AuthInvalidTokenResponseError() }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return {\n        data: {\n          user: data.user,\n          session: data.session,\n          ...(data.weak_password ? { weakPassword: data.weak_password } : null),\n        },\n        error,\n      }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Log in an existing user via a third-party provider.\n   * This method supports the PKCE flow.\n   */\n  async signInWithOAuth(credentials: SignInWithOAuthCredentials): Promise<OAuthResponse> {\n    return await this._handleProviderSignIn(credentials.provider, {\n      redirectTo: credentials.options?.redirectTo,\n      scopes: credentials.options?.scopes,\n      queryParams: credentials.options?.queryParams,\n      skipBrowserRedirect: credentials.options?.skipBrowserRedirect,\n    })\n  }\n\n  /**\n   * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n   */\n  async exchangeCodeForSession(authCode: string): Promise<AuthTokenResponse> {\n    await this.initializePromise\n\n    return this._acquireLock(-1, async () => {\n      return this._exchangeCodeForSession(authCode)\n    })\n  }\n\n  private async _exchangeCodeForSession(authCode: string): Promise<\n    | {\n        data: { session: Session; user: User; redirectType: string | null }\n        error: null\n      }\n    | { data: { session: null; user: null; redirectType: null }; error: AuthError }\n  > {\n    const storageItem = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n    const [codeVerifier, redirectType] = ((storageItem ?? '') as string).split('/')\n\n    try {\n      const { data, error } = await _request(\n        this.fetch,\n        'POST',\n        `${this.url}/token?grant_type=pkce`,\n        {\n          headers: this.headers,\n          body: {\n            auth_code: authCode,\n            code_verifier: codeVerifier,\n          },\n          xform: _sessionResponse,\n        }\n      )\n      await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n      if (error) {\n        throw error\n      }\n      if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null, redirectType: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data: { ...data, redirectType: redirectType ?? null }, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null, redirectType: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Allows signing in with an OIDC ID token. The authentication provider used\n   * should be enabled and configured.\n   */\n  async signInWithIdToken(credentials: SignInWithIdTokenCredentials): Promise<AuthTokenResponse> {\n    try {\n      const { options, provider, token, access_token, nonce } = credentials\n\n      const res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n        headers: this.headers,\n        body: {\n          provider,\n          id_token: token,\n          access_token,\n          nonce,\n          gotrue_meta_security: { captcha_token: options?.captchaToken },\n        },\n        xform: _sessionResponse,\n      })\n\n      const { data, error } = res\n      if (error) {\n        return { data: { user: null, session: null }, error }\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Log in a user using magiclink or a one-time password (OTP).\n   *\n   * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n   * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n   * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or, that the account\n   * can only be accessed via social login.\n   *\n   * Do note that you will need to configure a Whatsapp sender on Twilio\n   * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n   * channel is not supported on other providers\n   * at this time.\n   * This method supports PKCE when an email is passed.\n   */\n  async signInWithOtp(credentials: SignInWithPasswordlessCredentials): Promise<AuthOtpResponse> {\n    try {\n      if ('email' in credentials) {\n        const { email, options } = credentials\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce') {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n        const { error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            email,\n            data: options?.data ?? {},\n            create_user: options?.shouldCreateUser ?? true,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          redirectTo: options?.emailRedirectTo,\n        })\n        return { data: { user: null, session: null }, error }\n      }\n      if ('phone' in credentials) {\n        const { phone, options } = credentials\n        const { data, error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            phone,\n            data: options?.data ?? {},\n            create_user: options?.shouldCreateUser ?? true,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            channel: options?.channel ?? 'sms',\n          },\n        })\n        return { data: { user: null, session: null, messageId: data?.message_id }, error }\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number.')\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n   */\n  async verifyOtp(params: VerifyOtpParams): Promise<AuthResponse> {\n    try {\n      let redirectTo: string | undefined = undefined\n      let captchaToken: string | undefined = undefined\n      if ('options' in params) {\n        redirectTo = params.options?.redirectTo\n        captchaToken = params.options?.captchaToken\n      }\n      const { data, error } = await _request(this.fetch, 'POST', `${this.url}/verify`, {\n        headers: this.headers,\n        body: {\n          ...params,\n          gotrue_meta_security: { captcha_token: captchaToken },\n        },\n        redirectTo,\n        xform: _sessionResponse,\n      })\n\n      if (error) {\n        throw error\n      }\n\n      if (!data) {\n        throw new Error('An error occurred on token verification.')\n      }\n\n      const session: Session | null = data.session\n      const user: User = data.user\n\n      if (session?.access_token) {\n        await this._saveSession(session as Session)\n        await this._notifyAllSubscribers(\n          params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN',\n          session\n        )\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Attempts a single-sign on using an enterprise Identity Provider. A\n   * successful SSO attempt will redirect the current page to the identity\n   * provider authorization page. The redirect URL is implementation and SSO\n   * protocol specific.\n   *\n   * You can use it by providing a SSO domain. Typically you can extract this\n   * domain by asking users for their email address. If this domain is\n   * registered on the Auth instance the redirect will use that organization's\n   * currently active SSO Identity Provider for the login.\n   *\n   * If you have built an organization-specific login page, you can use the\n   * organization's SSO Identity Provider UUID directly instead.\n   */\n  async signInWithSSO(params: SignInWithSSO): Promise<SSOResponse> {\n    try {\n      let codeChallenge: string | null = null\n      let codeChallengeMethod: string | null = null\n      if (this.flowType === 'pkce') {\n        ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n          this.storage,\n          this.storageKey\n        )\n      }\n\n      return await _request(this.fetch, 'POST', `${this.url}/sso`, {\n        body: {\n          ...('providerId' in params ? { provider_id: params.providerId } : null),\n          ...('domain' in params ? { domain: params.domain } : null),\n          redirect_to: params.options?.redirectTo ?? undefined,\n          ...(params?.options?.captchaToken\n            ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n            : null),\n          skip_http_redirect: true, // fetch does not handle redirects\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n        },\n        headers: this.headers,\n        xform: _ssoResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Sends a reauthentication OTP to the user's email or phone number.\n   * Requires the user to be signed-in.\n   */\n  async reauthenticate(): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._reauthenticate()\n    })\n  }\n\n  private async _reauthenticate(): Promise<AuthResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const {\n          data: { session },\n          error: sessionError,\n        } = result\n        if (sessionError) throw sessionError\n        if (!session) throw new AuthSessionMissingError()\n\n        const { error } = await _request(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n          headers: this.headers,\n          jwt: session.access_token,\n        })\n        return { data: { user: null, session: null }, error }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n   */\n  async resend(credentials: ResendParams): Promise<AuthOtpResponse> {\n    try {\n      const endpoint = `${this.url}/resend`\n      if ('email' in credentials) {\n        const { email, type, options } = credentials\n        const { error } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            email,\n            type,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          redirectTo: options?.emailRedirectTo,\n        })\n        return { data: { user: null, session: null }, error }\n      } else if ('phone' in credentials) {\n        const { phone, type, options } = credentials\n        const { data, error } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            phone,\n            type,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n        })\n        return { data: { user: null, session: null, messageId: data?.message_id }, error }\n      }\n      throw new AuthInvalidCredentialsError(\n        'You must provide either an email or phone number and a type'\n      )\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Returns the session, refreshing it if necessary.\n   *\n   * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n   *\n   * **IMPORTANT:** This method loads values directly from the storage attached\n   * to the client. If that storage is based on request cookies for example,\n   * the values in it may not be authentic and therefore it's strongly advised\n   * against using this method and its results in such circumstances. A warning\n   * will be emitted if this is detected. Use {@link #getUser()} instead.\n   */\n  async getSession() {\n    await this.initializePromise\n\n    const result = await this._acquireLock(-1, async () => {\n      return this._useSession(async (result) => {\n        return result\n      })\n    })\n\n    return result\n  }\n\n  /**\n   * Acquires a global lock based on the storage key.\n   */\n  private async _acquireLock<R>(acquireTimeout: number, fn: () => Promise<R>): Promise<R> {\n    this._debug('#_acquireLock', 'begin', acquireTimeout)\n\n    try {\n      if (this.lockAcquired) {\n        const last = this.pendingInLock.length\n          ? this.pendingInLock[this.pendingInLock.length - 1]\n          : Promise.resolve()\n\n        const result = (async () => {\n          await last\n          return await fn()\n        })()\n\n        this.pendingInLock.push(\n          (async () => {\n            try {\n              await result\n            } catch (e: any) {\n              // we just care if it finished\n            }\n          })()\n        )\n\n        return result\n      }\n\n      return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n        this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey)\n\n        try {\n          this.lockAcquired = true\n\n          const result = fn()\n\n          this.pendingInLock.push(\n            (async () => {\n              try {\n                await result\n              } catch (e: any) {\n                // we just care if it finished\n              }\n            })()\n          )\n\n          await result\n\n          // keep draining the queue until there's nothing to wait on\n          while (this.pendingInLock.length) {\n            const waitOn = [...this.pendingInLock]\n\n            await Promise.all(waitOn)\n\n            this.pendingInLock.splice(0, waitOn.length)\n          }\n\n          return await result\n        } finally {\n          this._debug('#_acquireLock', 'lock released for storage key', this.storageKey)\n\n          this.lockAcquired = false\n        }\n      })\n    } finally {\n      this._debug('#_acquireLock', 'end')\n    }\n  }\n\n  /**\n   * Use instead of {@link #getSession} inside the library. It is\n   * semantically usually what you want, as getting a session involves some\n   * processing afterwards that requires only one client operating on the\n   * session at once across multiple tabs or processes.\n   */\n  private async _useSession<R>(\n    fn: (\n      result:\n        | {\n            data: {\n              session: Session\n            }\n            error: null\n          }\n        | {\n            data: {\n              session: null\n            }\n            error: AuthError\n          }\n        | {\n            data: {\n              session: null\n            }\n            error: null\n          }\n    ) => Promise<R>\n  ): Promise<R> {\n    this._debug('#_useSession', 'begin')\n\n    try {\n      // the use of __loadSession here is the only correct use of the function!\n      const result = await this.__loadSession()\n\n      return await fn(result)\n    } finally {\n      this._debug('#_useSession', 'end')\n    }\n  }\n\n  /**\n   * NEVER USE DIRECTLY!\n   *\n   * Always use {@link #_useSession}.\n   */\n  private async __loadSession(): Promise<\n    | {\n        data: {\n          session: Session\n        }\n        error: null\n      }\n    | {\n        data: {\n          session: null\n        }\n        error: AuthError\n      }\n    | {\n        data: {\n          session: null\n        }\n        error: null\n      }\n  > {\n    this._debug('#__loadSession()', 'begin')\n\n    if (!this.lockAcquired) {\n      this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack)\n    }\n\n    try {\n      let currentSession: Session | null = null\n\n      const maybeSession = await getItemAsync(this.storage, this.storageKey)\n\n      this._debug('#getSession()', 'session from storage', maybeSession)\n\n      if (maybeSession !== null) {\n        if (this._isValidSession(maybeSession)) {\n          currentSession = maybeSession\n        } else {\n          this._debug('#getSession()', 'session from storage is not valid')\n          await this._removeSession()\n        }\n      }\n\n      if (!currentSession) {\n        return { data: { session: null }, error: null }\n      }\n\n      // A session is considered expired before the access token _actually_\n      // expires. When the autoRefreshToken option is off (or when the tab is\n      // in the background), very eager users of getSession() -- like\n      // realtime-js -- might send a valid JWT which will expire by the time it\n      // reaches the server.\n      const hasExpired = currentSession.expires_at\n        ? currentSession.expires_at * 1000 - Date.now() < EXPIRY_MARGIN_MS\n        : false\n\n      this._debug(\n        '#__loadSession()',\n        `session has${hasExpired ? '' : ' not'} expired`,\n        'expires_at',\n        currentSession.expires_at\n      )\n\n      if (!hasExpired) {\n        if (this.storage.isServer) {\n          let suppressWarning = this.suppressGetSessionWarning\n          const proxySession: Session = new Proxy(currentSession, {\n            get: (target: any, prop: string, receiver: any) => {\n              if (!suppressWarning && prop === 'user') {\n                // only show warning when the user object is being accessed from the server\n                console.warn(\n                  'Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.'\n                )\n                suppressWarning = true // keeps this proxy instance from logging additional warnings\n                this.suppressGetSessionWarning = true // keeps this client's future proxy instances from warning\n              }\n              return Reflect.get(target, prop, receiver)\n            },\n          })\n          currentSession = proxySession\n        }\n\n        return { data: { session: currentSession }, error: null }\n      }\n\n      const { session, error } = await this._callRefreshToken(currentSession.refresh_token)\n      if (error) {\n        return { data: { session: null }, error }\n      }\n\n      return { data: { session }, error: null }\n    } finally {\n      this._debug('#__loadSession()', 'end')\n    }\n  }\n\n  /**\n   * Gets the current user details if there is an existing session. This method\n   * performs a network request to the Supabase Auth server, so the returned\n   * value is authentic and can be used to base authorization rules on.\n   *\n   * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n   */\n  async getUser(jwt?: string): Promise<UserResponse> {\n    if (jwt) {\n      return await this._getUser(jwt)\n    }\n\n    await this.initializePromise\n\n    const result = await this._acquireLock(-1, async () => {\n      return await this._getUser()\n    })\n\n    return result\n  }\n\n  private async _getUser(jwt?: string): Promise<UserResponse> {\n    try {\n      if (jwt) {\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: jwt,\n          xform: _userResponse,\n        })\n      }\n\n      return await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) {\n          throw error\n        }\n\n        // returns an error if there is no access_token or custom authorization header\n        if (!data.session?.access_token && !this.hasCustomAuthorizationHeader) {\n          return { data: { user: null }, error: new AuthSessionMissingError() }\n        }\n\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: data.session?.access_token ?? undefined,\n          xform: _userResponse,\n        })\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        if (isAuthSessionMissingError(error)) {\n          // JWT contains a `session_id` which does not correspond to an active\n          // session in the database, indicating the user is signed out.\n\n          await this._removeSession()\n          await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n        }\n\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Updates user data for a logged in user.\n   */\n  async updateUser(\n    attributes: UserAttributes,\n    options: {\n      emailRedirectTo?: string | undefined\n    } = {}\n  ): Promise<UserResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._updateUser(attributes, options)\n    })\n  }\n\n  protected async _updateUser(\n    attributes: UserAttributes,\n    options: {\n      emailRedirectTo?: string | undefined\n    } = {}\n  ): Promise<UserResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          throw sessionError\n        }\n        if (!sessionData.session) {\n          throw new AuthSessionMissingError()\n        }\n        const session: Session = sessionData.session\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce' && attributes.email != null) {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n\n        const { data, error: userError } = await _request(this.fetch, 'PUT', `${this.url}/user`, {\n          headers: this.headers,\n          redirectTo: options?.emailRedirectTo,\n          body: {\n            ...attributes,\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          jwt: session.access_token,\n          xform: _userResponse,\n        })\n        if (userError) throw userError\n        session.user = data.user as User\n        await this._saveSession(session)\n        await this._notifyAllSubscribers('USER_UPDATED', session)\n        return { data: { user: session.user }, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n   * If the refresh token or access token in the current session is invalid, an error will be thrown.\n   * @param currentSession The current session that minimally contains an access token and refresh token.\n   */\n  async setSession(currentSession: {\n    access_token: string\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._setSession(currentSession)\n    })\n  }\n\n  protected async _setSession(currentSession: {\n    access_token: string\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    try {\n      if (!currentSession.access_token || !currentSession.refresh_token) {\n        throw new AuthSessionMissingError()\n      }\n\n      const timeNow = Date.now() / 1000\n      let expiresAt = timeNow\n      let hasExpired = true\n      let session: Session | null = null\n      const { payload } = decodeJWT(currentSession.access_token)\n      if (payload.exp) {\n        expiresAt = payload.exp\n        hasExpired = expiresAt <= timeNow\n      }\n\n      if (hasExpired) {\n        const { session: refreshedSession, error } = await this._callRefreshToken(\n          currentSession.refresh_token\n        )\n        if (error) {\n          return { data: { user: null, session: null }, error: error }\n        }\n\n        if (!refreshedSession) {\n          return { data: { user: null, session: null }, error: null }\n        }\n        session = refreshedSession\n      } else {\n        const { data, error } = await this._getUser(currentSession.access_token)\n        if (error) {\n          throw error\n        }\n        session = {\n          access_token: currentSession.access_token,\n          refresh_token: currentSession.refresh_token,\n          user: data.user,\n          token_type: 'bearer',\n          expires_in: expiresAt - timeNow,\n          expires_at: expiresAt,\n        }\n        await this._saveSession(session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user: session.user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { session: null, user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Returns a new session, regardless of expiry status.\n   * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n   * If the current session's refresh token is invalid, an error will be thrown.\n   * @param currentSession The current session. If passed in, it must contain a refresh token.\n   */\n  async refreshSession(currentSession?: { refresh_token: string }): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._refreshSession(currentSession)\n    })\n  }\n\n  protected async _refreshSession(currentSession?: {\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        if (!currentSession) {\n          const { data, error } = result\n          if (error) {\n            throw error\n          }\n\n          currentSession = data.session ?? undefined\n        }\n\n        if (!currentSession?.refresh_token) {\n          throw new AuthSessionMissingError()\n        }\n\n        const { session, error } = await this._callRefreshToken(currentSession.refresh_token)\n        if (error) {\n          return { data: { user: null, session: null }, error: error }\n        }\n\n        if (!session) {\n          return { data: { user: null, session: null }, error: null }\n        }\n\n        return { data: { user: session.user, session }, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Gets the session data from a URL string\n   */\n  private async _getSessionFromURL(\n    params: { [parameter: string]: string },\n    callbackUrlType: string\n  ): Promise<\n    | {\n        data: { session: Session; redirectType: string | null }\n        error: null\n      }\n    | { data: { session: null; redirectType: null }; error: AuthError }\n  > {\n    try {\n      if (!isBrowser()) throw new AuthImplicitGrantRedirectError('No browser detected.')\n\n      // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n      if (params.error || params.error_description || params.error_code) {\n        // The error class returned implies that the redirect is from an implicit grant flow\n        // but it could also be from a redirect error from a PKCE flow.\n        throw new AuthImplicitGrantRedirectError(\n          params.error_description || 'Error in URL with unspecified error_description',\n          {\n            error: params.error || 'unspecified_error',\n            code: params.error_code || 'unspecified_code',\n          }\n        )\n      }\n\n      // Checks for mismatches between the flowType initialised in the client and the URL parameters\n      switch (callbackUrlType) {\n        case 'implicit':\n          if (this.flowType === 'pkce') {\n            throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.')\n          }\n          break\n        case 'pkce':\n          if (this.flowType === 'implicit') {\n            throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.')\n          }\n          break\n        default:\n        // there's no mismatch so we continue\n      }\n\n      // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n      if (callbackUrlType === 'pkce') {\n        this._debug('#_initialize()', 'begin', 'is PKCE flow', true)\n        if (!params.code) throw new AuthPKCEGrantCodeExchangeError('No code detected.')\n        const { data, error } = await this._exchangeCodeForSession(params.code)\n        if (error) throw error\n\n        const url = new URL(window.location.href)\n        url.searchParams.delete('code')\n\n        window.history.replaceState(window.history.state, '', url.toString())\n\n        return { data: { session: data.session, redirectType: null }, error: null }\n      }\n\n      const {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        refresh_token,\n        expires_in,\n        expires_at,\n        token_type,\n      } = params\n\n      if (!access_token || !expires_in || !refresh_token || !token_type) {\n        throw new AuthImplicitGrantRedirectError('No session defined in URL')\n      }\n\n      const timeNow = Math.round(Date.now() / 1000)\n      const expiresIn = parseInt(expires_in)\n      let expiresAt = timeNow + expiresIn\n\n      if (expires_at) {\n        expiresAt = parseInt(expires_at)\n      }\n\n      const actuallyExpiresIn = expiresAt - timeNow\n      if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION_MS) {\n        console.warn(\n          `@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`\n        )\n      }\n\n      const issuedAt = expiresAt - expiresIn\n      if (timeNow - issuedAt >= 120) {\n        console.warn(\n          '@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale',\n          issuedAt,\n          expiresAt,\n          timeNow\n        )\n      } else if (timeNow - issuedAt < 0) {\n        console.warn(\n          '@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew',\n          issuedAt,\n          expiresAt,\n          timeNow\n        )\n      }\n\n      const { data, error } = await this._getUser(access_token)\n      if (error) throw error\n\n      const session: Session = {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        expires_in: expiresIn,\n        expires_at: expiresAt,\n        refresh_token,\n        token_type,\n        user: data.user,\n      }\n\n      // Remove tokens from URL\n      window.location.hash = ''\n      this._debug('#_getSessionFromURL()', 'clearing window.location.hash')\n\n      return { data: { session, redirectType: params.type }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { session: null, redirectType: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n   */\n  private _isImplicitGrantCallback(params: { [parameter: string]: string }): boolean {\n    return Boolean(params.access_token || params.error_description)\n  }\n\n  /**\n   * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n   */\n  private async _isPKCECallback(params: { [parameter: string]: string }): Promise<boolean> {\n    const currentStorageContent = await getItemAsync(\n      this.storage,\n      `${this.storageKey}-code-verifier`\n    )\n\n    return !!(params.code && currentStorageContent)\n  }\n\n  /**\n   * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n   *\n   * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n   * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n   *\n   * If using `others` scope, no `SIGNED_OUT` event is fired!\n   */\n  async signOut(options: SignOut = { scope: 'global' }): Promise<{ error: AuthError | null }> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._signOut(options)\n    })\n  }\n\n  protected async _signOut(\n    { scope }: SignOut = { scope: 'global' }\n  ): Promise<{ error: AuthError | null }> {\n    return await this._useSession(async (result) => {\n      const { data, error: sessionError } = result\n      if (sessionError) {\n        return { error: sessionError }\n      }\n      const accessToken = data.session?.access_token\n      if (accessToken) {\n        const { error } = await this.admin.signOut(accessToken, scope)\n        if (error) {\n          // ignore 404s since user might not exist anymore\n          // ignore 401s since an invalid or expired JWT should sign out the current session\n          if (\n            !(\n              isAuthApiError(error) &&\n              (error.status === 404 || error.status === 401 || error.status === 403)\n            )\n          ) {\n            return { error }\n          }\n        }\n      }\n      if (scope !== 'others') {\n        await this._removeSession()\n        await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n      }\n      return { error: null }\n    })\n  }\n\n  /**\n   * Receive a notification every time an auth event happens.\n   * @param callback A callback function to be invoked when an auth event happens.\n   */\n  onAuthStateChange(\n    callback: (event: AuthChangeEvent, session: Session | null) => void | Promise<void>\n  ): {\n    data: { subscription: Subscription }\n  } {\n    const id: string = uuid()\n    const subscription: Subscription = {\n      id,\n      callback,\n      unsubscribe: () => {\n        this._debug('#unsubscribe()', 'state change callback with id removed', id)\n\n        this.stateChangeEmitters.delete(id)\n      },\n    }\n\n    this._debug('#onAuthStateChange()', 'registered callback with id', id)\n\n    this.stateChangeEmitters.set(id, subscription)\n    ;(async () => {\n      await this.initializePromise\n\n      await this._acquireLock(-1, async () => {\n        this._emitInitialSession(id)\n      })\n    })()\n\n    return { data: { subscription } }\n  }\n\n  private async _emitInitialSession(id: string): Promise<void> {\n    return await this._useSession(async (result) => {\n      try {\n        const {\n          data: { session },\n          error,\n        } = result\n        if (error) throw error\n\n        await this.stateChangeEmitters.get(id)?.callback('INITIAL_SESSION', session)\n        this._debug('INITIAL_SESSION', 'callback id', id, 'session', session)\n      } catch (err) {\n        await this.stateChangeEmitters.get(id)?.callback('INITIAL_SESSION', null)\n        this._debug('INITIAL_SESSION', 'callback id', id, 'error', err)\n        console.error(err)\n      }\n    })\n  }\n\n  /**\n   * Sends a password reset request to an email address. This method supports the PKCE flow.\n   *\n   * @param email The email address of the user.\n   * @param options.redirectTo The URL to send the user to after they click the password reset link.\n   * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n   */\n  async resetPasswordForEmail(\n    email: string,\n    options: {\n      redirectTo?: string\n      captchaToken?: string\n    } = {}\n  ): Promise<\n    | {\n        data: {}\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    let codeChallenge: string | null = null\n    let codeChallengeMethod: string | null = null\n\n    if (this.flowType === 'pkce') {\n      ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n        this.storage,\n        this.storageKey,\n        true // isPasswordRecovery\n      )\n    }\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/recover`, {\n        body: {\n          email,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n          gotrue_meta_security: { captcha_token: options.captchaToken },\n        },\n        headers: this.headers,\n        redirectTo: options.redirectTo,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Gets all the identities linked to a user.\n   */\n  async getUserIdentities(): Promise<\n    | {\n        data: {\n          identities: UserIdentity[]\n        }\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    try {\n      const { data, error } = await this.getUser()\n      if (error) throw error\n      return { data: { identities: data.user.identities ?? [] }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n  /**\n   * Links an oauth identity to an existing user.\n   * This method supports the PKCE flow.\n   */\n  async linkIdentity(credentials: SignInWithOAuthCredentials): Promise<OAuthResponse> {\n    try {\n      const { data, error } = await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) throw error\n        const url: string = await this._getUrlForProvider(\n          `${this.url}/user/identities/authorize`,\n          credentials.provider,\n          {\n            redirectTo: credentials.options?.redirectTo,\n            scopes: credentials.options?.scopes,\n            queryParams: credentials.options?.queryParams,\n            skipBrowserRedirect: true,\n          }\n        )\n        return await _request(this.fetch, 'GET', url, {\n          headers: this.headers,\n          jwt: data.session?.access_token ?? undefined,\n        })\n      })\n      if (error) throw error\n      if (isBrowser() && !credentials.options?.skipBrowserRedirect) {\n        window.location.assign(data?.url)\n      }\n      return { data: { provider: credentials.provider, url: data?.url }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { provider: credentials.provider, url: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n   */\n  async unlinkIdentity(identity: UserIdentity): Promise<\n    | {\n        data: {}\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    try {\n      return await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) {\n          throw error\n        }\n        return await _request(\n          this.fetch,\n          'DELETE',\n          `${this.url}/user/identities/${identity.identity_id}`,\n          {\n            headers: this.headers,\n            jwt: data.session?.access_token ?? undefined,\n          }\n        )\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Generates a new JWT.\n   * @param refreshToken A valid refresh token that was returned on login.\n   */\n  private async _refreshAccessToken(refreshToken: string): Promise<AuthResponse> {\n    const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`\n    this._debug(debugName, 'begin')\n\n    try {\n      const startedAt = Date.now()\n\n      // will attempt to refresh the token with exponential backoff\n      return await retryable(\n        async (attempt) => {\n          if (attempt > 0) {\n            await sleep(200 * Math.pow(2, attempt - 1)) // 200, 400, 800, ...\n          }\n\n          this._debug(debugName, 'refreshing attempt', attempt)\n\n          return await _request(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n            body: { refresh_token: refreshToken },\n            headers: this.headers,\n            xform: _sessionResponse,\n          })\n        },\n        (attempt, error) => {\n          const nextBackOffInterval = 200 * Math.pow(2, attempt)\n          return (\n            error &&\n            isAuthRetryableFetchError(error) &&\n            // retryable only if the request can be sent before the backoff overflows the tick duration\n            Date.now() + nextBackOffInterval - startedAt < AUTO_REFRESH_TICK_DURATION_MS\n          )\n        }\n      )\n    } catch (error) {\n      this._debug(debugName, 'error', error)\n\n      if (isAuthError(error)) {\n        return { data: { session: null, user: null }, error }\n      }\n      throw error\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private _isValidSession(maybeSession: unknown): maybeSession is Session {\n    const isValidSession =\n      typeof maybeSession === 'object' &&\n      maybeSession !== null &&\n      'access_token' in maybeSession &&\n      'refresh_token' in maybeSession &&\n      'expires_at' in maybeSession\n\n    return isValidSession\n  }\n\n  private async _handleProviderSignIn(\n    provider: Provider,\n    options: {\n      redirectTo?: string\n      scopes?: string\n      queryParams?: { [key: string]: string }\n      skipBrowserRedirect?: boolean\n    }\n  ) {\n    const url: string = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n      redirectTo: options.redirectTo,\n      scopes: options.scopes,\n      queryParams: options.queryParams,\n    })\n\n    this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url)\n\n    // try to open on the browser\n    if (isBrowser() && !options.skipBrowserRedirect) {\n      window.location.assign(url)\n    }\n\n    return { data: { provider, url }, error: null }\n  }\n\n  /**\n   * Recovers the session from LocalStorage and refreshes the token\n   * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n   */\n  private async _recoverAndRefresh() {\n    const debugName = '#_recoverAndRefresh()'\n    this._debug(debugName, 'begin')\n\n    try {\n      const currentSession = await getItemAsync(this.storage, this.storageKey)\n      this._debug(debugName, 'session from storage', currentSession)\n\n      if (!this._isValidSession(currentSession)) {\n        this._debug(debugName, 'session is not valid')\n        if (currentSession !== null) {\n          await this._removeSession()\n        }\n\n        return\n      }\n\n      const expiresWithMargin =\n        (currentSession.expires_at ?? Infinity) * 1000 - Date.now() < EXPIRY_MARGIN_MS\n\n      this._debug(\n        debugName,\n        `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN_MS}s`\n      )\n\n      if (expiresWithMargin) {\n        if (this.autoRefreshToken && currentSession.refresh_token) {\n          const { error } = await this._callRefreshToken(currentSession.refresh_token)\n\n          if (error) {\n            console.error(error)\n\n            if (!isAuthRetryableFetchError(error)) {\n              this._debug(\n                debugName,\n                'refresh failed with a non-retryable error, removing the session',\n                error\n              )\n              await this._removeSession()\n            }\n          }\n        }\n      } else {\n        // no need to persist currentSession again, as we just loaded it from\n        // local storage; persisting it again may overwrite a value saved by\n        // another client with access to the same local storage\n        await this._notifyAllSubscribers('SIGNED_IN', currentSession)\n      }\n    } catch (err) {\n      this._debug(debugName, 'error', err)\n\n      console.error(err)\n      return\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private async _callRefreshToken(refreshToken: string): Promise<CallRefreshTokenResult> {\n    if (!refreshToken) {\n      throw new AuthSessionMissingError()\n    }\n\n    // refreshing is already in progress\n    if (this.refreshingDeferred) {\n      return this.refreshingDeferred.promise\n    }\n\n    const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`\n\n    this._debug(debugName, 'begin')\n\n    try {\n      this.refreshingDeferred = new Deferred<CallRefreshTokenResult>()\n\n      const { data, error } = await this._refreshAccessToken(refreshToken)\n      if (error) throw error\n      if (!data.session) throw new AuthSessionMissingError()\n\n      await this._saveSession(data.session)\n      await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session)\n\n      const result = { session: data.session, error: null }\n\n      this.refreshingDeferred.resolve(result)\n\n      return result\n    } catch (error) {\n      this._debug(debugName, 'error', error)\n\n      if (isAuthError(error)) {\n        const result = { session: null, error }\n\n        if (!isAuthRetryableFetchError(error)) {\n          await this._removeSession()\n        }\n\n        this.refreshingDeferred?.resolve(result)\n\n        return result\n      }\n\n      this.refreshingDeferred?.reject(error)\n      throw error\n    } finally {\n      this.refreshingDeferred = null\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private async _notifyAllSubscribers(\n    event: AuthChangeEvent,\n    session: Session | null,\n    broadcast = true\n  ) {\n    const debugName = `#_notifyAllSubscribers(${event})`\n    this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`)\n\n    try {\n      if (this.broadcastChannel && broadcast) {\n        this.broadcastChannel.postMessage({ event, session })\n      }\n\n      const errors: any[] = []\n      const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n        try {\n          await x.callback(event, session)\n        } catch (e: any) {\n          errors.push(e)\n        }\n      })\n\n      await Promise.all(promises)\n\n      if (errors.length > 0) {\n        for (let i = 0; i < errors.length; i += 1) {\n          console.error(errors[i])\n        }\n\n        throw errors[0]\n      }\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  /**\n   * set currentSession and currentUser\n   * process to _startAutoRefreshToken if possible\n   */\n  private async _saveSession(session: Session) {\n    this._debug('#_saveSession()', session)\n    // _saveSession is always called whenever a new session has been acquired\n    // so we can safely suppress the warning returned by future getSession calls\n    this.suppressGetSessionWarning = true\n    await setItemAsync(this.storage, this.storageKey, session)\n  }\n\n  private async _removeSession() {\n    this._debug('#_removeSession()')\n\n    await removeItemAsync(this.storage, this.storageKey)\n    await this._notifyAllSubscribers('SIGNED_OUT', null)\n  }\n\n  /**\n   * Removes any registered visibilitychange callback.\n   *\n   * {@see #startAutoRefresh}\n   * {@see #stopAutoRefresh}\n   */\n  private _removeVisibilityChangedCallback() {\n    this._debug('#_removeVisibilityChangedCallback()')\n\n    const callback = this.visibilityChangedCallback\n    this.visibilityChangedCallback = null\n\n    try {\n      if (callback && isBrowser() && window?.removeEventListener) {\n        window.removeEventListener('visibilitychange', callback)\n      }\n    } catch (e) {\n      console.error('removing visibilitychange callback failed', e)\n    }\n  }\n\n  /**\n   * This is the private implementation of {@link #startAutoRefresh}. Use this\n   * within the library.\n   */\n  private async _startAutoRefresh() {\n    await this._stopAutoRefresh()\n\n    this._debug('#_startAutoRefresh()')\n\n    const ticker = setInterval(() => this._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION_MS)\n    this.autoRefreshTicker = ticker\n\n    if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n      // ticker is a NodeJS Timeout object that has an `unref` method\n      // https://nodejs.org/api/timers.html#timeoutunref\n      // When auto refresh is used in NodeJS (like for testing) the\n      // `setInterval` is preventing the process from being marked as\n      // finished and tests run endlessly. This can be prevented by calling\n      // `unref()` on the returned object.\n      ticker.unref()\n      // @ts-expect-error TS has no context of Deno\n    } else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n      // similar like for NodeJS, but with the Deno API\n      // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n      // @ts-expect-error TS has no context of Deno\n      Deno.unrefTimer(ticker)\n    }\n\n    // run the tick immediately, but in the next pass of the event loop so that\n    // #_initialize can be allowed to complete without recursively waiting on\n    // itself\n    setTimeout(async () => {\n      await this.initializePromise\n      await this._autoRefreshTokenTick()\n    }, 0)\n  }\n\n  /**\n   * This is the private implementation of {@link #stopAutoRefresh}. Use this\n   * within the library.\n   */\n  private async _stopAutoRefresh() {\n    this._debug('#_stopAutoRefresh()')\n\n    const ticker = this.autoRefreshTicker\n    this.autoRefreshTicker = null\n\n    if (ticker) {\n      clearInterval(ticker)\n    }\n  }\n\n  /**\n   * Starts an auto-refresh process in the background. The session is checked\n   * every few seconds. Close to the time of expiration a process is started to\n   * refresh the session. If refreshing fails it will be retried for as long as\n   * necessary.\n   *\n   * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n   * to call this function, it will be called for you.\n   *\n   * On browsers the refresh process works only when the tab/window is in the\n   * foreground to conserve resources as well as prevent race conditions and\n   * flooding auth with requests. If you call this method any managed\n   * visibility change callback will be removed and you must manage visibility\n   * changes on your own.\n   *\n   * On non-browser platforms the refresh process works *continuously* in the\n   * background, which may not be desirable. You should hook into your\n   * platform's foreground indication mechanism and call these methods\n   * appropriately to conserve resources.\n   *\n   * {@see #stopAutoRefresh}\n   */\n  async startAutoRefresh() {\n    this._removeVisibilityChangedCallback()\n    await this._startAutoRefresh()\n  }\n\n  /**\n   * Stops an active auto refresh process running in the background (if any).\n   *\n   * If you call this method any managed visibility change callback will be\n   * removed and you must manage visibility changes on your own.\n   *\n   * See {@link #startAutoRefresh} for more details.\n   */\n  async stopAutoRefresh() {\n    this._removeVisibilityChangedCallback()\n    await this._stopAutoRefresh()\n  }\n\n  /**\n   * Runs the auto refresh token tick.\n   */\n  private async _autoRefreshTokenTick() {\n    this._debug('#_autoRefreshTokenTick()', 'begin')\n\n    try {\n      await this._acquireLock(0, async () => {\n        try {\n          const now = Date.now()\n\n          try {\n            return await this._useSession(async (result) => {\n              const {\n                data: { session },\n              } = result\n\n              if (!session || !session.refresh_token || !session.expires_at) {\n                this._debug('#_autoRefreshTokenTick()', 'no session')\n                return\n              }\n\n              // session will expire in this many ticks (or has already expired if <= 0)\n              const expiresInTicks = Math.floor(\n                (session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION_MS\n              )\n\n              this._debug(\n                '#_autoRefreshTokenTick()',\n                `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`\n              )\n\n              if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                await this._callRefreshToken(session.refresh_token)\n              }\n            })\n          } catch (e: any) {\n            console.error(\n              'Auto refresh tick failed with error. This is likely a transient error.',\n              e\n            )\n          }\n        } finally {\n          this._debug('#_autoRefreshTokenTick()', 'end')\n        }\n      })\n    } catch (e: any) {\n      if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n        this._debug('auto refresh token tick lock not available')\n      } else {\n        throw e\n      }\n    }\n  }\n\n  /**\n   * Registers callbacks on the browser / platform, which in-turn run\n   * algorithms when the browser window/tab are in foreground. On non-browser\n   * platforms it assumes always foreground.\n   */\n  private async _handleVisibilityChange() {\n    this._debug('#_handleVisibilityChange()')\n\n    if (!isBrowser() || !window?.addEventListener) {\n      if (this.autoRefreshToken) {\n        // in non-browser environments the refresh token ticker runs always\n        this.startAutoRefresh()\n      }\n\n      return false\n    }\n\n    try {\n      this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false)\n\n      window?.addEventListener('visibilitychange', this.visibilityChangedCallback)\n\n      // now immediately call the visbility changed callback to setup with the\n      // current visbility state\n      await this._onVisibilityChanged(true) // initial call\n    } catch (error) {\n      console.error('_handleVisibilityChange', error)\n    }\n  }\n\n  /**\n   * Callback registered with `window.addEventListener('visibilitychange')`.\n   */\n  private async _onVisibilityChanged(calledFromInitialize: boolean) {\n    const methodName = `#_onVisibilityChanged(${calledFromInitialize})`\n    this._debug(methodName, 'visibilityState', document.visibilityState)\n\n    if (document.visibilityState === 'visible') {\n      if (this.autoRefreshToken) {\n        // in browser environments the refresh token ticker runs only on focused tabs\n        // which prevents race conditions\n        this._startAutoRefresh()\n      }\n\n      if (!calledFromInitialize) {\n        // called when the visibility has changed, i.e. the browser\n        // transitioned from hidden -> visible so we need to see if the session\n        // should be recovered immediately... but to do that we need to acquire\n        // the lock first asynchronously\n        await this.initializePromise\n\n        await this._acquireLock(-1, async () => {\n          if (document.visibilityState !== 'visible') {\n            this._debug(\n              methodName,\n              'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting'\n            )\n\n            // visibility has changed while waiting for the lock, abort\n            return\n          }\n\n          // recover the session\n          await this._recoverAndRefresh()\n        })\n      }\n    } else if (document.visibilityState === 'hidden') {\n      if (this.autoRefreshToken) {\n        this._stopAutoRefresh()\n      }\n    }\n  }\n\n  /**\n   * Generates the relevant login URL for a third-party provider.\n   * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n   * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n   * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n   */\n  private async _getUrlForProvider(\n    url: string,\n    provider: Provider,\n    options: {\n      redirectTo?: string\n      scopes?: string\n      queryParams?: { [key: string]: string }\n      skipBrowserRedirect?: boolean\n    }\n  ) {\n    const urlParams: string[] = [`provider=${encodeURIComponent(provider)}`]\n    if (options?.redirectTo) {\n      urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`)\n    }\n    if (options?.scopes) {\n      urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`)\n    }\n    if (this.flowType === 'pkce') {\n      const [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n        this.storage,\n        this.storageKey\n      )\n\n      const flowParams = new URLSearchParams({\n        code_challenge: `${encodeURIComponent(codeChallenge)}`,\n        code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n      })\n      urlParams.push(flowParams.toString())\n    }\n    if (options?.queryParams) {\n      const query = new URLSearchParams(options.queryParams)\n      urlParams.push(query.toString())\n    }\n    if (options?.skipBrowserRedirect) {\n      urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`)\n    }\n\n    return `${url}?${urlParams.join('&')}`\n  }\n\n  private async _unenroll(params: MFAUnenrollParams): Promise<AuthMFAUnenrollResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n\n        return await _request(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n          headers: this.headers,\n          jwt: sessionData?.session?.access_token,\n        })\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#enroll}\n   */\n  private async _enroll(params: MFAEnrollTOTPParams): Promise<AuthMFAEnrollTOTPResponse>\n  private async _enroll(params: MFAEnrollPhoneParams): Promise<AuthMFAEnrollPhoneResponse>\n  private async _enroll(params: MFAEnrollParams): Promise<AuthMFAEnrollResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n\n        const body = {\n          friendly_name: params.friendlyName,\n          factor_type: params.factorType,\n          ...(params.factorType === 'phone' ? { phone: params.phone } : { issuer: params.issuer }),\n        }\n\n        const { data, error } = await _request(this.fetch, 'POST', `${this.url}/factors`, {\n          body,\n          headers: this.headers,\n          jwt: sessionData?.session?.access_token,\n        })\n\n        if (error) {\n          return { data: null, error }\n        }\n\n        if (params.factorType === 'totp' && data?.totp?.qr_code) {\n          data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`\n        }\n\n        return { data, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#verify}\n   */\n  private async _verify(params: MFAVerifyParams): Promise<AuthMFAVerifyResponse> {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async (result) => {\n          const { data: sessionData, error: sessionError } = result\n          if (sessionError) {\n            return { data: null, error: sessionError }\n          }\n\n          const { data, error } = await _request(\n            this.fetch,\n            'POST',\n            `${this.url}/factors/${params.factorId}/verify`,\n            {\n              body: { code: params.code, challenge_id: params.challengeId },\n              headers: this.headers,\n              jwt: sessionData?.session?.access_token,\n            }\n          )\n          if (error) {\n            return { data: null, error }\n          }\n\n          await this._saveSession({\n            expires_at: Math.round(Date.now() / 1000) + data.expires_in,\n            ...data,\n          })\n          await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data)\n\n          return { data, error }\n        })\n      } catch (error) {\n        if (isAuthError(error)) {\n          return { data: null, error }\n        }\n        throw error\n      }\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#challenge}\n   */\n  private async _challenge(params: MFAChallengeParams): Promise<AuthMFAChallengeResponse> {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async (result) => {\n          const { data: sessionData, error: sessionError } = result\n          if (sessionError) {\n            return { data: null, error: sessionError }\n          }\n\n          return await _request(\n            this.fetch,\n            'POST',\n            `${this.url}/factors/${params.factorId}/challenge`,\n            {\n              body: { channel: params.channel },\n              headers: this.headers,\n              jwt: sessionData?.session?.access_token,\n            }\n          )\n        })\n      } catch (error) {\n        if (isAuthError(error)) {\n          return { data: null, error }\n        }\n        throw error\n      }\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#challengeAndVerify}\n   */\n  private async _challengeAndVerify(\n    params: MFAChallengeAndVerifyParams\n  ): Promise<AuthMFAVerifyResponse> {\n    // both _challenge and _verify independently acquire the lock, so no need\n    // to acquire it here\n\n    const { data: challengeData, error: challengeError } = await this._challenge({\n      factorId: params.factorId,\n    })\n    if (challengeError) {\n      return { data: null, error: challengeError }\n    }\n\n    return await this._verify({\n      factorId: params.factorId,\n      challengeId: challengeData.id,\n      code: params.code,\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#listFactors}\n   */\n  private async _listFactors(): Promise<AuthMFAListFactorsResponse> {\n    // use #getUser instead of #_getUser as the former acquires a lock\n    const {\n      data: { user },\n      error: userError,\n    } = await this.getUser()\n    if (userError) {\n      return { data: null, error: userError }\n    }\n\n    const factors = user?.factors || []\n    const totp = factors.filter(\n      (factor) => factor.factor_type === 'totp' && factor.status === 'verified'\n    )\n    const phone = factors.filter(\n      (factor) => factor.factor_type === 'phone' && factor.status === 'verified'\n    )\n\n    return {\n      data: {\n        all: factors,\n        totp,\n        phone,\n      },\n      error: null,\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   */\n  private async _getAuthenticatorAssuranceLevel(): Promise<AuthMFAGetAuthenticatorAssuranceLevelResponse> {\n    return this._acquireLock(-1, async () => {\n      return await this._useSession(async (result) => {\n        const {\n          data: { session },\n          error: sessionError,\n        } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n        if (!session) {\n          return {\n            data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n            error: null,\n          }\n        }\n\n        const { payload } = decodeJWT(session.access_token)\n\n        let currentLevel: AuthenticatorAssuranceLevels | null = null\n\n        if (payload.aal) {\n          currentLevel = payload.aal\n        }\n\n        let nextLevel: AuthenticatorAssuranceLevels | null = currentLevel\n\n        const verifiedFactors =\n          session.user.factors?.filter((factor: Factor) => factor.status === 'verified') ?? []\n\n        if (verifiedFactors.length > 0) {\n          nextLevel = 'aal2'\n        }\n\n        const currentAuthenticationMethods = payload.amr || []\n\n        return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null }\n      })\n    })\n  }\n\n  private async fetchJwk(kid: string, jwks: { keys: JWK[] } = { keys: [] }): Promise<JWK> {\n    // try fetching from the supplied jwks\n    let jwk = jwks.keys.find((key) => key.kid === kid)\n    if (jwk) {\n      return jwk\n    }\n\n    // try fetching from cache\n    jwk = this.jwks.keys.find((key) => key.kid === kid)\n\n    // jwk exists and jwks isn't stale\n    if (jwk && this.jwks_cached_at + JWKS_TTL > Date.now()) {\n      return jwk\n    }\n    // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n    const { data, error } = await _request(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n      headers: this.headers,\n    })\n    if (error) {\n      throw error\n    }\n    if (!data.keys || data.keys.length === 0) {\n      throw new AuthInvalidJwtError('JWKS is empty')\n    }\n    this.jwks = data\n    this.jwks_cached_at = Date.now()\n    // Find the signing key\n    jwk = data.keys.find((key: any) => key.kid === kid)\n    if (!jwk) {\n      throw new AuthInvalidJwtError('No matching signing key found in JWKS')\n    }\n    return jwk\n  }\n\n  /**\n   * @experimental This method may change in future versions.\n   * @description Gets the claims from a JWT. If the JWT is symmetric JWTs, it will call getUser() to verify against the server. If the JWT is asymmetric, it will be verified against the JWKS using the WebCrypto API.\n   */\n  async getClaims(\n    jwt?: string,\n    jwks: { keys: JWK[] } = { keys: [] }\n  ): Promise<\n    | {\n        data: { claims: JwtPayload; header: JwtHeader; signature: Uint8Array }\n        error: null\n      }\n    | { data: null; error: AuthError }\n    | { data: null; error: null }\n  > {\n    try {\n      let token = jwt\n      if (!token) {\n        const { data, error } = await this.getSession()\n        if (error || !data.session) {\n          return { data: null, error }\n        }\n        token = data.session.access_token\n      }\n\n      const {\n        header,\n        payload,\n        signature,\n        raw: { header: rawHeader, payload: rawPayload },\n      } = decodeJWT(token)\n\n      // Reject expired JWTs\n      validateExp(payload.exp)\n\n      // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n      if (\n        !header.kid ||\n        header.alg === 'HS256' ||\n        !('crypto' in globalThis && 'subtle' in globalThis.crypto)\n      ) {\n        const { error } = await this.getUser(token)\n        if (error) {\n          throw error\n        }\n        // getUser succeeds so the claims in the JWT can be trusted\n        return {\n          data: {\n            claims: payload,\n            header,\n            signature,\n          },\n          error: null,\n        }\n      }\n\n      const algorithm = getAlgorithm(header.alg)\n      const signingKey = await this.fetchJwk(header.kid, jwks)\n\n      // Convert JWK to CryptoKey\n      const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, [\n        'verify',\n      ])\n\n      // Verify the signature\n      const isValid = await crypto.subtle.verify(\n        algorithm,\n        publicKey,\n        signature,\n        stringToUint8Array(`${rawHeader}.${rawPayload}`)\n      )\n\n      if (!isValid) {\n        throw new AuthInvalidJwtError('Invalid JWT signature')\n      }\n\n      // If verification succeeds, decode and return claims\n      return {\n        data: {\n          claims: payload,\n          header,\n          signature,\n        },\n        error: null,\n      }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,SACEC,eAAe,EACfC,gBAAgB,EAChBC,6BAA6B,EAC7BC,2BAA2B,EAC3BC,UAAU,EACVC,WAAW,EACXC,QAAQ,QACH,iBAAiB;AACxB,SAEEC,8BAA8B,EAC9BC,8BAA8B,EAC9BC,2BAA2B,EAC3BC,uBAAuB,EACvBC,6BAA6B,EAC7BC,gBAAgB,EAChBC,cAAc,EACdC,WAAW,EACXC,yBAAyB,EACzBC,yBAAyB,EACzBC,gCAAgC,EAChCC,mBAAmB,QACd,cAAc;AACrB,SAEEC,QAAQ,EACRC,gBAAgB,EAChBC,wBAAwB,EACxBC,aAAa,EACbC,YAAY,QACP,aAAa;AACpB,SACEC,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,oBAAoB,EACpBC,sBAAsB,EACtBC,yBAAyB,EACzBC,YAAY,EACZC,WAAW,EACXC,SAAS,QACJ,eAAe;AACtB,SAASC,mBAAmB,EAAEC,yBAAyB,QAAQ,qBAAqB;AACpF,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,uBAAuB,EAAEC,aAAa,QAAQ,aAAa;AAwDpE,SAASC,kBAAkB,QAAQ,iBAAiB;AAEpDJ,kBAAkB,EAAE,EAAC;AAErB,MAAMK,eAAe,GAAsE;EACzFC,GAAG,EAAE3C,UAAU;EACf4C,UAAU,EAAE3C,WAAW;EACvB4C,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,kBAAkB,EAAE,IAAI;EACxBC,OAAO,EAAEpD,eAAe;EACxBqD,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,KAAK;EACZC,4BAA4B,EAAE;CAC/B;AAED,eAAeC,QAAQA,CAAIC,IAAY,EAAEC,cAAsB,EAAEC,EAAoB;EACnF,OAAO,MAAMA,EAAE,EAAE;AACnB;AAEA,eAAc,MAAOC,YAAY;EA4D/B;;;EAGAC,YAAYC,OAA4B;;IAnC9B,KAAAC,aAAa,GAAqC,IAAI;IACtD,KAAAC,mBAAmB,GAA8B,IAAIC,GAAG,EAAE;IAC1D,KAAAC,iBAAiB,GAA0C,IAAI;IAC/D,KAAAC,yBAAyB,GAAgC,IAAI;IAC7D,KAAAC,kBAAkB,GAA4C,IAAI;IAC5E;;;;;;IAMU,KAAAC,iBAAiB,GAAqC,IAAI;IAC1D,KAAAlB,kBAAkB,GAAG,IAAI;IAKzB,KAAAI,4BAA4B,GAAG,KAAK;IACpC,KAAAe,yBAAyB,GAAG,KAAK;IAGjC,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAmB,EAAE;IAE5C;;;IAGU,KAAAC,gBAAgB,GAA4B,IAAI;IAGhD,KAAAC,MAAM,GAA8CC,OAAO,CAACC,GAAG;IAMvE,IAAI,CAACC,UAAU,GAAGjB,YAAY,CAACkB,cAAc;IAC7ClB,YAAY,CAACkB,cAAc,IAAI,CAAC;IAEhC,IAAI,IAAI,CAACD,UAAU,GAAG,CAAC,IAAInD,SAAS,EAAE,EAAE;MACtCiD,OAAO,CAACI,IAAI,CACV,8MAA8M,CAC/M;;IAGH,MAAMC,QAAQ,GAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQpC,eAAe,GAAKgB,OAAO,CAAE;IAEnD,IAAI,CAACqB,gBAAgB,GAAG,CAAC,CAACH,QAAQ,CAAC1B,KAAK;IACxC,IAAI,OAAO0B,QAAQ,CAAC1B,KAAK,KAAK,UAAU,EAAE;MACxC,IAAI,CAACoB,MAAM,GAAGM,QAAQ,CAAC1B,KAAK;;IAG9B,IAAI,CAACJ,cAAc,GAAG8B,QAAQ,CAAC9B,cAAc;IAC7C,IAAI,CAACF,UAAU,GAAGgC,QAAQ,CAAChC,UAAU;IACrC,IAAI,CAACC,gBAAgB,GAAG+B,QAAQ,CAAC/B,gBAAgB;IACjD,IAAI,CAACmC,KAAK,GAAG,IAAIrF,cAAc,CAAC;MAC9BgD,GAAG,EAAEiC,QAAQ,CAACjC,GAAG;MACjBK,OAAO,EAAE4B,QAAQ,CAAC5B,OAAO;MACzBiC,KAAK,EAAEL,QAAQ,CAACK;KACjB,CAAC;IAEF,IAAI,CAACtC,GAAG,GAAGiC,QAAQ,CAACjC,GAAG;IACvB,IAAI,CAACK,OAAO,GAAG4B,QAAQ,CAAC5B,OAAO;IAC/B,IAAI,CAACiC,KAAK,GAAGzD,YAAY,CAACoD,QAAQ,CAACK,KAAK,CAAC;IACzC,IAAI,CAACC,IAAI,GAAGN,QAAQ,CAACM,IAAI,IAAI9B,QAAQ;IACrC,IAAI,CAACL,kBAAkB,GAAG6B,QAAQ,CAAC7B,kBAAkB;IACrD,IAAI,CAACE,QAAQ,GAAG2B,QAAQ,CAAC3B,QAAQ;IACjC,IAAI,CAACE,4BAA4B,GAAGyB,QAAQ,CAACzB,4BAA4B;IAEzE,IAAIyB,QAAQ,CAACM,IAAI,EAAE;MACjB,IAAI,CAACA,IAAI,GAAGN,QAAQ,CAACM,IAAI;KAC1B,MAAM,IAAI5D,SAAS,EAAE,KAAI,CAAA6D,EAAA,GAAAC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,SAAS,cAAAF,EAAA,uBAAAA,EAAA,CAAEG,KAAK,GAAE;MACtD,IAAI,CAACJ,IAAI,GAAG1C,aAAa;KAC1B,MAAM;MACL,IAAI,CAAC0C,IAAI,GAAG9B,QAAQ;;IAEtB,IAAI,CAACmC,IAAI,GAAG;MAAEC,IAAI,EAAE;IAAE,CAAE;IACxB,IAAI,CAACC,cAAc,GAAGC,MAAM,CAACC,gBAAgB;IAC7C,IAAI,CAACC,GAAG,GAAG;MACTC,MAAM,EAAE,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;MAC/BC,MAAM,EAAE,IAAI,CAACC,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC;MAC/BG,QAAQ,EAAE,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,IAAI,CAAC;MACnCK,SAAS,EAAE,IAAI,CAACC,UAAU,CAACN,IAAI,CAAC,IAAI,CAAC;MACrCO,WAAW,EAAE,IAAI,CAACC,YAAY,CAACR,IAAI,CAAC,IAAI,CAAC;MACzCS,kBAAkB,EAAE,IAAI,CAACC,mBAAmB,CAACV,IAAI,CAAC,IAAI,CAAC;MACvDW,8BAA8B,EAAE,IAAI,CAACC,+BAA+B,CAACZ,IAAI,CAAC,IAAI;KAC/E;IAED,IAAI,IAAI,CAACjD,cAAc,EAAE;MACvB,IAAI8B,QAAQ,CAACgC,OAAO,EAAE;QACpB,IAAI,CAACA,OAAO,GAAGhC,QAAQ,CAACgC,OAAO;OAChC,MAAM;QACL,IAAI/E,oBAAoB,EAAE,EAAE;UAC1B,IAAI,CAAC+E,OAAO,GAAGzE,mBAAmB;SACnC,MAAM;UACL,IAAI,CAACwB,aAAa,GAAG,EAAE;UACvB,IAAI,CAACiD,OAAO,GAAGxE,yBAAyB,CAAC,IAAI,CAACuB,aAAa,CAAC;;;KAGjE,MAAM;MACL,IAAI,CAACA,aAAa,GAAG,EAAE;MACvB,IAAI,CAACiD,OAAO,GAAGxE,yBAAyB,CAAC,IAAI,CAACuB,aAAa,CAAC;;IAG9D,IAAIrC,SAAS,EAAE,IAAI8D,UAAU,CAACyB,gBAAgB,IAAI,IAAI,CAAC/D,cAAc,IAAI,IAAI,CAACF,UAAU,EAAE;MACxF,IAAI;QACF,IAAI,CAACyB,gBAAgB,GAAG,IAAIe,UAAU,CAACyB,gBAAgB,CAAC,IAAI,CAACjE,UAAU,CAAC;OACzE,CAAC,OAAOkE,CAAM,EAAE;QACfvC,OAAO,CAACwC,KAAK,CACX,wFAAwF,EACxFD,CAAC,CACF;;MAGH,CAAAE,EAAA,OAAI,CAAC3C,gBAAgB,cAAA2C,EAAA,uBAAAA,EAAA,CAAEC,gBAAgB,CAAC,SAAS,EAAE,MAAOC,KAAK,IAAI;QACjE,IAAI,CAACC,MAAM,CAAC,0DAA0D,EAAED,KAAK,CAAC;QAE9E,MAAM,IAAI,CAACE,qBAAqB,CAACF,KAAK,CAACG,IAAI,CAACH,KAAK,EAAEA,KAAK,CAACG,IAAI,CAACC,OAAO,EAAE,KAAK,CAAC,EAAC;MAChF,CAAC,CAAC;;IAGJ,IAAI,CAACC,UAAU,EAAE;EACnB;EAEQJ,MAAMA,CAAC,GAAGK,IAAW;IAC3B,IAAI,IAAI,CAACzC,gBAAgB,EAAE;MACzB,IAAI,CAACT,MAAM,CACT,gBAAgB,IAAI,CAACG,UAAU,KAAKnC,OAAO,KAAK,IAAImF,IAAI,EAAE,CAACC,WAAW,EAAE,EAAE,EAC1E,GAAGF,IAAI,CACR;;IAGH,OAAO,IAAI;EACb;EAEA;;;;;EAKA,MAAMD,UAAUA,CAAA;IACd,IAAI,IAAI,CAACtD,iBAAiB,EAAE;MAC1B,OAAO,MAAM,IAAI,CAACA,iBAAiB;;IAGrC,IAAI,CAACA,iBAAiB,GAAG,CAAC,YAAW;MACnC,OAAO,MAAM,IAAI,CAAC0D,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;QAC5C,OAAO,MAAM,IAAI,CAACC,WAAW,EAAE;MACjC,CAAC,CAAC;IACJ,CAAC,EAAC,CAAE;IAEJ,OAAO,MAAM,IAAI,CAAC3D,iBAAiB;EACrC;EAEA;;;;;;EAMQ,MAAM2D,WAAWA,CAAA;;IACvB,IAAI;MACF,MAAMC,MAAM,GAAG/F,sBAAsB,CAACgG,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3D,IAAIC,eAAe,GAAG,MAAM;MAC5B,IAAI,IAAI,CAACC,wBAAwB,CAACL,MAAM,CAAC,EAAE;QACzCI,eAAe,GAAG,UAAU;OAC7B,MAAM,IAAI,MAAM,IAAI,CAACE,eAAe,CAACN,MAAM,CAAC,EAAE;QAC7CI,eAAe,GAAG,MAAM;;MAG1B;;;;;;MAMA,IAAI3G,SAAS,EAAE,IAAI,IAAI,CAACyB,kBAAkB,IAAIkF,eAAe,KAAK,MAAM,EAAE;QACxE,MAAM;UAAEZ,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAM,IAAI,CAACqB,kBAAkB,CAACP,MAAM,EAAEI,eAAe,CAAC;QAC9E,IAAIlB,KAAK,EAAE;UACT,IAAI,CAACI,MAAM,CAAC,gBAAgB,EAAE,kCAAkC,EAAEJ,KAAK,CAAC;UAExE,IAAIlG,gCAAgC,CAACkG,KAAK,CAAC,EAAE;YAC3C,MAAMsB,SAAS,GAAG,CAAAlD,EAAA,GAAA4B,KAAK,CAACuB,OAAO,cAAAnD,EAAA,uBAAAA,EAAA,CAAEoD,IAAI;YACrC,IACEF,SAAS,KAAK,yBAAyB,IACvCA,SAAS,KAAK,oBAAoB,IAClCA,SAAS,KAAK,+BAA+B,EAC7C;cACA,OAAO;gBAAEtB;cAAK,CAAE;;;UAIpB;UACA;UACA,MAAM,IAAI,CAACyB,cAAc,EAAE;UAE3B,OAAO;YAAEzB;UAAK,CAAE;;QAGlB,MAAM;UAAEO,OAAO;UAAEmB;QAAY,CAAE,GAAGpB,IAAI;QAEtC,IAAI,CAACF,MAAM,CACT,gBAAgB,EAChB,yBAAyB,EACzBG,OAAO,EACP,eAAe,EACfmB,YAAY,CACb;QAED,MAAM,IAAI,CAACC,YAAY,CAACpB,OAAO,CAAC;QAEhCqB,UAAU,CAAC,YAAW;UACpB,IAAIF,YAAY,KAAK,UAAU,EAAE;YAC/B,MAAM,IAAI,CAACrB,qBAAqB,CAAC,mBAAmB,EAAEE,OAAO,CAAC;WAC/D,MAAM;YACL,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;;QAE1D,CAAC,EAAE,CAAC,CAAC;QAEL,OAAO;UAAEP,KAAK,EAAE;QAAI,CAAE;;MAExB;MACA,MAAM,IAAI,CAAC6B,kBAAkB,EAAE;MAC/B,OAAO;QAAE7B,KAAK,EAAE;MAAI,CAAE;KACvB,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEA;QAAK,CAAE;;MAGlB,OAAO;QACLA,KAAK,EAAE,IAAIvG,gBAAgB,CAAC,wCAAwC,EAAEuG,KAAK;OAC5E;KACF,SAAS;MACR,MAAM,IAAI,CAAC8B,uBAAuB,EAAE;MACpC,IAAI,CAAC1B,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC;;EAExC;EAEA;;;;;EAKA,MAAM2B,iBAAiBA,CAACC,WAA0C;;IAChE,IAAI;MACF,MAAMC,GAAG,GAAG,MAAMjI,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,SAAS,EAAE;QACnEK,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBiG,IAAI,EAAE;UACJ5B,IAAI,EAAE,CAAAL,EAAA,IAAA7B,EAAA,GAAA4D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAErF,OAAO,cAAAyB,EAAA,uBAAAA,EAAA,CAAEkC,IAAI,cAAAL,EAAA,cAAAA,EAAA,GAAI,EAAE;UACtCkC,oBAAoB,EAAE;YAAEC,aAAa,EAAE,CAAAC,EAAA,GAAAL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAErF,OAAO,cAAA0F,EAAA,uBAAAA,EAAA,CAAEC;UAAY;SAC1E;QACDC,KAAK,EAAEtI;OACR,CAAC;MACF,MAAM;QAAEqG,IAAI;QAAEN;MAAK,CAAE,GAAGiC,GAAG;MAE3B,IAAIjC,KAAK,IAAI,CAACM,IAAI,EAAE;QAClB,OAAO;UAAEA,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP,KAAK,EAAEA;QAAK,CAAE;;MAE9D,MAAMO,OAAO,GAAmBD,IAAI,CAACC,OAAO;MAC5C,MAAMiC,IAAI,GAAgBlC,IAAI,CAACkC,IAAI;MAEnC,IAAIlC,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAACoB,YAAY,CAACrB,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;;MAGxD,OAAO;QAAED,IAAI,EAAE;UAAEkC,IAAI;UAAEjC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAChD,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;;;EAUA,MAAMyC,MAAMA,CAACT,WAA0C;;IACrD,IAAI;MACF,IAAIC,GAAiB;MACrB,IAAI,OAAO,IAAID,WAAW,EAAE;QAC1B,MAAM;UAAEU,KAAK;UAAEC,QAAQ;UAAEhG;QAAO,CAAE,GAAGqF,WAAW;QAChD,IAAIY,aAAa,GAAkB,IAAI;QACvC,IAAIC,mBAAmB,GAAkB,IAAI;QAC7C,IAAI,IAAI,CAAC3G,QAAQ,KAAK,MAAM,EAAE;UAC5B;UAAC,CAAC0G,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAM7H,yBAAyB,CACrE,IAAI,CAAC6E,OAAO,EACZ,IAAI,CAAChE,UAAU,CAChB;;QAEHoG,GAAG,GAAG,MAAMjI,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,SAAS,EAAE;UAC7DK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB6G,UAAU,EAAEnG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG,eAAe;UACpCb,IAAI,EAAE;YACJQ,KAAK;YACLC,QAAQ;YACRrC,IAAI,EAAE,CAAAlC,EAAA,GAAAzB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2D,IAAI,cAAAlC,EAAA,cAAAA,EAAA,GAAI,EAAE;YACzB+D,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2F;YAAY,CAAE;YAC9DU,cAAc,EAAEJ,aAAa;YAC7BK,qBAAqB,EAAEJ;WACxB;UACDN,KAAK,EAAEtI;SACR,CAAC;OACH,MAAM,IAAI,OAAO,IAAI+H,WAAW,EAAE;QACjC,MAAM;UAAEkB,KAAK;UAAEP,QAAQ;UAAEhG;QAAO,CAAE,GAAGqF,WAAW;QAChDC,GAAG,GAAG,MAAMjI,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,SAAS,EAAE;UAC7DK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBiG,IAAI,EAAE;YACJgB,KAAK;YACLP,QAAQ;YACRrC,IAAI,EAAE,CAAAL,EAAA,GAAAtD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2D,IAAI,cAAAL,EAAA,cAAAA,EAAA,GAAI,EAAE;YACzBkD,OAAO,EAAE,CAAAd,EAAA,GAAA1F,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwG,OAAO,cAAAd,EAAA,cAAAA,EAAA,GAAI,KAAK;YAClCF,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2F;YAAY;WAC7D;UACDC,KAAK,EAAEtI;SACR,CAAC;OACH,MAAM;QACL,MAAM,IAAIX,2BAA2B,CACnC,iEAAiE,CAClE;;MAGH,MAAM;QAAEgH,IAAI;QAAEN;MAAK,CAAE,GAAGiC,GAAG;MAE3B,IAAIjC,KAAK,IAAI,CAACM,IAAI,EAAE;QAClB,OAAO;UAAEA,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP,KAAK,EAAEA;QAAK,CAAE;;MAG9D,MAAMO,OAAO,GAAmBD,IAAI,CAACC,OAAO;MAC5C,MAAMiC,IAAI,GAAgBlC,IAAI,CAACkC,IAAI;MAEnC,IAAIlC,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAACoB,YAAY,CAACrB,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;;MAGxD,OAAO;QAAED,IAAI,EAAE;UAAEkC,IAAI;UAAEjC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAChD,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;EAQA,MAAMoD,kBAAkBA,CACtBpB,WAA0C;IAE1C,IAAI;MACF,IAAIC,GAAyB;MAC7B,IAAI,OAAO,IAAID,WAAW,EAAE;QAC1B,MAAM;UAAEU,KAAK;UAAEC,QAAQ;UAAEhG;QAAO,CAAE,GAAGqF,WAAW;QAChDC,GAAG,GAAG,MAAMjI,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,4BAA4B,EAAE;UAChFK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBiG,IAAI,EAAE;YACJQ,KAAK;YACLC,QAAQ;YACRR,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2F;YAAY;WAC7D;UACDC,KAAK,EAAErI;SACR,CAAC;OACH,MAAM,IAAI,OAAO,IAAI8H,WAAW,EAAE;QACjC,MAAM;UAAEkB,KAAK;UAAEP,QAAQ;UAAEhG;QAAO,CAAE,GAAGqF,WAAW;QAChDC,GAAG,GAAG,MAAMjI,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,4BAA4B,EAAE;UAChFK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBiG,IAAI,EAAE;YACJgB,KAAK;YACLP,QAAQ;YACRR,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2F;YAAY;WAC7D;UACDC,KAAK,EAAErI;SACR,CAAC;OACH,MAAM;QACL,MAAM,IAAIZ,2BAA2B,CACnC,iEAAiE,CAClE;;MAEH,MAAM;QAAEgH,IAAI;QAAEN;MAAK,CAAE,GAAGiC,GAAG;MAE3B,IAAIjC,KAAK,EAAE;QACT,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;OACtD,MAAM,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAACkC,IAAI,EAAE;QAC/C,OAAO;UAAElC,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP,KAAK,EAAE,IAAIxG,6BAA6B;QAAE,CAAE;;MAE5F,IAAI8G,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAACoB,YAAY,CAACrB,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;;MAE7D,OAAO;QACLD,IAAI,EAAAxC,MAAA,CAAAC,MAAA;UACFyE,IAAI,EAAElC,IAAI,CAACkC,IAAI;UACfjC,OAAO,EAAED,IAAI,CAACC;QAAO,GACjBD,IAAI,CAAC+C,aAAa,GAAG;UAAEC,YAAY,EAAEhD,IAAI,CAAC+C;QAAa,CAAE,GAAG,IAAK,CACtE;QACDrD;OACD;KACF,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,MAAMA,KAAK;;EAEf;EAEA;;;;EAIA,MAAMuD,eAAeA,CAACvB,WAAuC;;IAC3D,OAAO,MAAM,IAAI,CAACwB,qBAAqB,CAACxB,WAAW,CAACyB,QAAQ,EAAE;MAC5DX,UAAU,EAAE,CAAA1E,EAAA,GAAA4D,WAAW,CAACrF,OAAO,cAAAyB,EAAA,uBAAAA,EAAA,CAAE0E,UAAU;MAC3CY,MAAM,EAAE,CAAAzD,EAAA,GAAA+B,WAAW,CAACrF,OAAO,cAAAsD,EAAA,uBAAAA,EAAA,CAAEyD,MAAM;MACnCC,WAAW,EAAE,CAAAtB,EAAA,GAAAL,WAAW,CAACrF,OAAO,cAAA0F,EAAA,uBAAAA,EAAA,CAAEsB,WAAW;MAC7CC,mBAAmB,EAAE,CAAAC,EAAA,GAAA7B,WAAW,CAACrF,OAAO,cAAAkH,EAAA,uBAAAA,EAAA,CAAED;KAC3C,CAAC;EACJ;EAEA;;;EAGA,MAAME,sBAAsBA,CAACC,QAAgB;IAC3C,MAAM,IAAI,CAAC7G,iBAAiB;IAE5B,OAAO,IAAI,CAAC0D,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACtC,OAAO,IAAI,CAACoD,uBAAuB,CAACD,QAAQ,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEQ,MAAMC,uBAAuBA,CAACD,QAAgB;IAOpD,MAAME,WAAW,GAAG,MAAM3J,YAAY,CAAC,IAAI,CAACuF,OAAO,EAAE,GAAG,IAAI,CAAChE,UAAU,gBAAgB,CAAC;IACxF,MAAM,CAACqI,YAAY,EAAExC,YAAY,CAAC,GAAI,CAACuC,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,EAAE,EAAaE,KAAK,CAAC,GAAG,CAAC;IAE/E,IAAI;MACF,MAAM;QAAE7D,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAMhG,QAAQ,CACpC,IAAI,CAACkE,KAAK,EACV,MAAM,EACN,GAAG,IAAI,CAACtC,GAAG,wBAAwB,EACnC;QACEK,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBiG,IAAI,EAAE;UACJkC,SAAS,EAAEL,QAAQ;UACnBM,aAAa,EAAEH;SAChB;QACD3B,KAAK,EAAEtI;OACR,CACF;MACD,MAAMO,eAAe,CAAC,IAAI,CAACqF,OAAO,EAAE,GAAG,IAAI,CAAChE,UAAU,gBAAgB,CAAC;MACvE,IAAImE,KAAK,EAAE;QACT,MAAMA,KAAK;;MAEb,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAACkC,IAAI,EAAE;QACxC,OAAO;UACLlC,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE,IAAI;YAAEmB,YAAY,EAAE;UAAI,CAAE;UACvD1B,KAAK,EAAE,IAAIxG,6BAA6B;SACzC;;MAEH,IAAI8G,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAACoB,YAAY,CAACrB,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;;MAE7D,OAAO;QAAED,IAAI,EAAAxC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAOuC,IAAI;UAAEoB,YAAY,EAAEA,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAI;QAAI,EAAE;QAAE1B;MAAK,CAAE;KACxE,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE,IAAI;YAAEmB,YAAY,EAAE;UAAI,CAAE;UAAE1B;QAAK,CAAE;;MAG3E,MAAMA,KAAK;;EAEf;EAEA;;;;EAIA,MAAMsE,iBAAiBA,CAACtC,WAAyC;IAC/D,IAAI;MACF,MAAM;QAAErF,OAAO;QAAE8G,QAAQ;QAAEc,KAAK;QAAEC,YAAY;QAAEC;MAAK,CAAE,GAAGzC,WAAW;MAErE,MAAMC,GAAG,GAAG,MAAMjI,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,4BAA4B,EAAE;QACtFK,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBiG,IAAI,EAAE;UACJuB,QAAQ;UACRiB,QAAQ,EAAEH,KAAK;UACfC,YAAY;UACZC,KAAK;UACLtC,oBAAoB,EAAE;YAAEC,aAAa,EAAEzF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2F;UAAY;SAC7D;QACDC,KAAK,EAAEtI;OACR,CAAC;MAEF,MAAM;QAAEqG,IAAI;QAAEN;MAAK,CAAE,GAAGiC,GAAG;MAC3B,IAAIjC,KAAK,EAAE;QACT,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;OACtD,MAAM,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAACkC,IAAI,EAAE;QAC/C,OAAO;UACLlC,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UACnCP,KAAK,EAAE,IAAIxG,6BAA6B;SACzC;;MAEH,IAAI8G,IAAI,CAACC,OAAO,EAAE;QAChB,MAAM,IAAI,CAACoB,YAAY,CAACrB,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;;MAE7D,OAAO;QAAED,IAAI;QAAEN;MAAK,CAAE;KACvB,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;;;;;;;;;;EAiBA,MAAM2E,aAAaA,CAAC3C,WAA8C;;IAChE,IAAI;MACF,IAAI,OAAO,IAAIA,WAAW,EAAE;QAC1B,MAAM;UAAEU,KAAK;UAAE/F;QAAO,CAAE,GAAGqF,WAAW;QACtC,IAAIY,aAAa,GAAkB,IAAI;QACvC,IAAIC,mBAAmB,GAAkB,IAAI;QAC7C,IAAI,IAAI,CAAC3G,QAAQ,KAAK,MAAM,EAAE;UAC5B;UAAC,CAAC0G,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAM7H,yBAAyB,CACrE,IAAI,CAAC6E,OAAO,EACZ,IAAI,CAAChE,UAAU,CAChB;;QAEH,MAAM;UAAEmE;QAAK,CAAE,GAAG,MAAMhG,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,MAAM,EAAE;UACtEK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBiG,IAAI,EAAE;YACJQ,KAAK;YACLpC,IAAI,EAAE,CAAAlC,EAAA,GAAAzB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2D,IAAI,cAAAlC,EAAA,cAAAA,EAAA,GAAI,EAAE;YACzBwG,WAAW,EAAE,CAAA3E,EAAA,GAAAtD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkI,gBAAgB,cAAA5E,EAAA,cAAAA,EAAA,GAAI,IAAI;YAC9CkC,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2F;YAAY,CAAE;YAC9DU,cAAc,EAAEJ,aAAa;YAC7BK,qBAAqB,EAAEJ;WACxB;UACDC,UAAU,EAAEnG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;SACtB,CAAC;QACF,OAAO;UAAEzC,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,IAAI,OAAO,IAAIgC,WAAW,EAAE;QAC1B,MAAM;UAAEkB,KAAK;UAAEvG;QAAO,CAAE,GAAGqF,WAAW;QACtC,MAAM;UAAE1B,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAMhG,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,MAAM,EAAE;UAC5EK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBiG,IAAI,EAAE;YACJgB,KAAK;YACL5C,IAAI,EAAE,CAAA+B,EAAA,GAAA1F,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2D,IAAI,cAAA+B,EAAA,cAAAA,EAAA,GAAI,EAAE;YACzBuC,WAAW,EAAE,CAAAf,EAAA,GAAAlH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkI,gBAAgB,cAAAhB,EAAA,cAAAA,EAAA,GAAI,IAAI;YAC9C1B,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2F;YAAY,CAAE;YAC9Da,OAAO,EAAE,CAAA2B,EAAA,GAAAnI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwG,OAAO,cAAA2B,EAAA,cAAAA,EAAA,GAAI;;SAEhC,CAAC;QACF,OAAO;UAAExE,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE,IAAI;YAAEwE,SAAS,EAAEzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E;UAAU,CAAE;UAAEhF;QAAK,CAAE;;MAEpF,MAAM,IAAI1G,2BAA2B,CAAC,mDAAmD,CAAC;KAC3F,CAAC,OAAO0G,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMiF,SAASA,CAACnE,MAAuB;;IACrC,IAAI;MACF,IAAIgC,UAAU,GAAuBoC,SAAS;MAC9C,IAAI5C,YAAY,GAAuB4C,SAAS;MAChD,IAAI,SAAS,IAAIpE,MAAM,EAAE;QACvBgC,UAAU,GAAG,CAAA1E,EAAA,GAAA0C,MAAM,CAACnE,OAAO,cAAAyB,EAAA,uBAAAA,EAAA,CAAE0E,UAAU;QACvCR,YAAY,GAAG,CAAArC,EAAA,GAAAa,MAAM,CAACnE,OAAO,cAAAsD,EAAA,uBAAAA,EAAA,CAAEqC,YAAY;;MAE7C,MAAM;QAAEhC,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAMhG,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,SAAS,EAAE;QAC/EK,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBiG,IAAI,EAAApE,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACC+C,MAAM;UACTqB,oBAAoB,EAAE;YAAEC,aAAa,EAAEE;UAAY;QAAE,EACtD;QACDQ,UAAU;QACVP,KAAK,EAAEtI;OACR,CAAC;MAEF,IAAI+F,KAAK,EAAE;QACT,MAAMA,KAAK;;MAGb,IAAI,CAACM,IAAI,EAAE;QACT,MAAM,IAAI6E,KAAK,CAAC,0CAA0C,CAAC;;MAG7D,MAAM5E,OAAO,GAAmBD,IAAI,CAACC,OAAO;MAC5C,MAAMiC,IAAI,GAASlC,IAAI,CAACkC,IAAI;MAE5B,IAAIjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiE,YAAY,EAAE;QACzB,MAAM,IAAI,CAAC7C,YAAY,CAACpB,OAAkB,CAAC;QAC3C,MAAM,IAAI,CAACF,qBAAqB,CAC9BS,MAAM,CAACsE,IAAI,IAAI,UAAU,GAAG,mBAAmB,GAAG,WAAW,EAC7D7E,OAAO,CACR;;MAGH,OAAO;QAAED,IAAI,EAAE;UAAEkC,IAAI;UAAEjC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAChD,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;;;;;;;EAcA,MAAMqF,aAAaA,CAACvE,MAAqB;;IACvC,IAAI;MACF,IAAI8B,aAAa,GAAkB,IAAI;MACvC,IAAIC,mBAAmB,GAAkB,IAAI;MAC7C,IAAI,IAAI,CAAC3G,QAAQ,KAAK,MAAM,EAAE;QAC5B;QAAC,CAAC0G,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAM7H,yBAAyB,CACrE,IAAI,CAAC6E,OAAO,EACZ,IAAI,CAAChE,UAAU,CAChB;;MAGH,OAAO,MAAM7B,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,MAAM,EAAE;QAC3DsG,IAAI,EAAApE,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACE,YAAY,IAAI+C,MAAM,GAAG;UAAEwE,WAAW,EAAExE,MAAM,CAACyE;QAAU,CAAE,GAAG,IAAK,GACnE,QAAQ,IAAIzE,MAAM,GAAG;UAAE0E,MAAM,EAAE1E,MAAM,CAAC0E;QAAM,CAAE,GAAG,IAAK;UAC1DC,WAAW,EAAE,CAAAxF,EAAA,IAAA7B,EAAA,GAAA0C,MAAM,CAACnE,OAAO,cAAAyB,EAAA,uBAAAA,EAAA,CAAE0E,UAAU,cAAA7C,EAAA,cAAAA,EAAA,GAAIiF;QAAS,IAChD,EAAA7C,EAAA,GAAAvB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEnE,OAAO,cAAA0F,EAAA,uBAAAA,EAAA,CAAEC,YAAY,IAC7B;UAAEH,oBAAoB,EAAE;YAAEC,aAAa,EAAEtB,MAAM,CAACnE,OAAO,CAAC2F;UAAY;QAAE,CAAE,GACxE,IAAK;UACToD,kBAAkB,EAAE,IAAI;UACxB1C,cAAc,EAAEJ,aAAa;UAC7BK,qBAAqB,EAAEJ;QAAmB,EAC3C;QACD5G,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBsG,KAAK,EAAEnI;OACR,CAAC;KACH,CAAC,OAAO4F,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EAEA;;;;EAIA,MAAM2F,cAAcA,CAAA;IAClB,MAAM,IAAI,CAACzI,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAAC0D,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAACgF,eAAe,EAAE;IACrC,CAAC,CAAC;EACJ;EAEQ,MAAMA,eAAeA,CAAA;IAC3B,IAAI;MACF,OAAO,MAAM,IAAI,CAACC,WAAW,CAAC,MAAOC,MAAM,IAAI;QAC7C,MAAM;UACJxF,IAAI,EAAE;YAAEC;UAAO,CAAE;UACjBP,KAAK,EAAE+F;QAAY,CACpB,GAAGD,MAAM;QACV,IAAIC,YAAY,EAAE,MAAMA,YAAY;QACpC,IAAI,CAACxF,OAAO,EAAE,MAAM,IAAIhH,uBAAuB,EAAE;QAEjD,MAAM;UAAEyG;QAAK,CAAE,GAAG,MAAMhG,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACtC,GAAG,iBAAiB,EAAE;UAChFK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB+J,GAAG,EAAEzF,OAAO,CAACiE;SACd,CAAC;QACF,OAAO;UAAElE,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;MACvD,CAAC,CAAC;KACH,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMiG,MAAMA,CAACjE,WAAyB;IACpC,IAAI;MACF,MAAMkE,QAAQ,GAAG,GAAG,IAAI,CAACtK,GAAG,SAAS;MACrC,IAAI,OAAO,IAAIoG,WAAW,EAAE;QAC1B,MAAM;UAAEU,KAAK;UAAE0C,IAAI;UAAEzI;QAAO,CAAE,GAAGqF,WAAW;QAC5C,MAAM;UAAEhC;QAAK,CAAE,GAAG,MAAMhG,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAEgI,QAAQ,EAAE;UAC7DjK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBiG,IAAI,EAAE;YACJQ,KAAK;YACL0C,IAAI;YACJjD,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2F;YAAY;WAC7D;UACDQ,UAAU,EAAEnG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG;SACtB,CAAC;QACF,OAAO;UAAEzC,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;OACtD,MAAM,IAAI,OAAO,IAAIgC,WAAW,EAAE;QACjC,MAAM;UAAEkB,KAAK;UAAEkC,IAAI;UAAEzI;QAAO,CAAE,GAAGqF,WAAW;QAC5C,MAAM;UAAE1B,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAMhG,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAEgI,QAAQ,EAAE;UACnEjK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBiG,IAAI,EAAE;YACJgB,KAAK;YACLkC,IAAI;YACJjD,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2F;YAAY;;SAE/D,CAAC;QACF,OAAO;UAAEhC,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE,IAAI;YAAEwE,SAAS,EAAEzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E;UAAU,CAAE;UAAEhF;QAAK,CAAE;;MAEpF,MAAM,IAAI1G,2BAA2B,CACnC,6DAA6D,CAC9D;KACF,CAAC,OAAO0G,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAEvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;;;;;;EAWA,MAAMmG,UAAUA,CAAA;IACd,MAAM,IAAI,CAACjJ,iBAAiB;IAE5B,MAAM4I,MAAM,GAAG,MAAM,IAAI,CAAClF,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACpD,OAAO,IAAI,CAACiF,WAAW,CAAC,MAAOC,MAAM,IAAI;QACvC,OAAOA,MAAM;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOA,MAAM;EACf;EAEA;;;EAGQ,MAAMlF,YAAYA,CAAIrE,cAAsB,EAAEC,EAAoB;IACxE,IAAI,CAAC4D,MAAM,CAAC,eAAe,EAAE,OAAO,EAAE7D,cAAc,CAAC;IAErD,IAAI;MACF,IAAI,IAAI,CAACa,YAAY,EAAE;QACrB,MAAMgJ,IAAI,GAAG,IAAI,CAAC/I,aAAa,CAACgJ,MAAM,GAClC,IAAI,CAAChJ,aAAa,CAAC,IAAI,CAACA,aAAa,CAACgJ,MAAM,GAAG,CAAC,CAAC,GACjDC,OAAO,CAACC,OAAO,EAAE;QAErB,MAAMT,MAAM,GAAG,CAAC,YAAW;UACzB,MAAMM,IAAI;UACV,OAAO,MAAM5J,EAAE,EAAE;QACnB,CAAC,EAAC,CAAE;QAEJ,IAAI,CAACa,aAAa,CAACmJ,IAAI,CACrB,CAAC,YAAW;UACV,IAAI;YACF,MAAMV,MAAM;WACb,CAAC,OAAO/F,CAAM,EAAE;YACf;UAAA;QAEJ,CAAC,EAAC,CAAE,CACL;QAED,OAAO+F,MAAM;;MAGf,OAAO,MAAM,IAAI,CAAC3H,IAAI,CAAC,QAAQ,IAAI,CAACtC,UAAU,EAAE,EAAEU,cAAc,EAAE,YAAW;QAC3E,IAAI,CAAC6D,MAAM,CAAC,eAAe,EAAE,+BAA+B,EAAE,IAAI,CAACvE,UAAU,CAAC;QAE9E,IAAI;UACF,IAAI,CAACuB,YAAY,GAAG,IAAI;UAExB,MAAM0I,MAAM,GAAGtJ,EAAE,EAAE;UAEnB,IAAI,CAACa,aAAa,CAACmJ,IAAI,CACrB,CAAC,YAAW;YACV,IAAI;cACF,MAAMV,MAAM;aACb,CAAC,OAAO/F,CAAM,EAAE;cACf;YAAA;UAEJ,CAAC,EAAC,CAAE,CACL;UAED,MAAM+F,MAAM;UAEZ;UACA,OAAO,IAAI,CAACzI,aAAa,CAACgJ,MAAM,EAAE;YAChC,MAAMI,MAAM,GAAG,CAAC,GAAG,IAAI,CAACpJ,aAAa,CAAC;YAEtC,MAAMiJ,OAAO,CAACI,GAAG,CAACD,MAAM,CAAC;YAEzB,IAAI,CAACpJ,aAAa,CAACsJ,MAAM,CAAC,CAAC,EAAEF,MAAM,CAACJ,MAAM,CAAC;;UAG7C,OAAO,MAAMP,MAAM;SACpB,SAAS;UACR,IAAI,CAAC1F,MAAM,CAAC,eAAe,EAAE,+BAA+B,EAAE,IAAI,CAACvE,UAAU,CAAC;UAE9E,IAAI,CAACuB,YAAY,GAAG,KAAK;;MAE7B,CAAC,CAAC;KACH,SAAS;MACR,IAAI,CAACgD,MAAM,CAAC,eAAe,EAAE,KAAK,CAAC;;EAEvC;EAEA;;;;;;EAMQ,MAAMyF,WAAWA,CACvBrJ,EAoBe;IAEf,IAAI,CAAC4D,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC;IAEpC,IAAI;MACF;MACA,MAAM0F,MAAM,GAAG,MAAM,IAAI,CAACc,aAAa,EAAE;MAEzC,OAAO,MAAMpK,EAAE,CAACsJ,MAAM,CAAC;KACxB,SAAS;MACR,IAAI,CAAC1F,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC;;EAEtC;EAEA;;;;;EAKQ,MAAMwG,aAAaA,CAAA;IAoBzB,IAAI,CAACxG,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC;IAExC,IAAI,CAAC,IAAI,CAAChD,YAAY,EAAE;MACtB,IAAI,CAACgD,MAAM,CAAC,kBAAkB,EAAE,mCAAmC,EAAE,IAAI+E,KAAK,EAAE,CAAC0B,KAAK,CAAC;;IAGzF,IAAI;MACF,IAAIC,cAAc,GAAmB,IAAI;MAEzC,MAAMC,YAAY,GAAG,MAAMzM,YAAY,CAAC,IAAI,CAACuF,OAAO,EAAE,IAAI,CAAChE,UAAU,CAAC;MAEtE,IAAI,CAACuE,MAAM,CAAC,eAAe,EAAE,sBAAsB,EAAE2G,YAAY,CAAC;MAElE,IAAIA,YAAY,KAAK,IAAI,EAAE;QACzB,IAAI,IAAI,CAACC,eAAe,CAACD,YAAY,CAAC,EAAE;UACtCD,cAAc,GAAGC,YAAY;SAC9B,MAAM;UACL,IAAI,CAAC3G,MAAM,CAAC,eAAe,EAAE,mCAAmC,CAAC;UACjE,MAAM,IAAI,CAACqB,cAAc,EAAE;;;MAI/B,IAAI,CAACqF,cAAc,EAAE;QACnB,OAAO;UAAExG,IAAI,EAAE;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAEP,KAAK,EAAE;QAAI,CAAE;;MAGjD;MACA;MACA;MACA;MACA;MACA,MAAMiH,UAAU,GAAGH,cAAc,CAACI,UAAU,GACxCJ,cAAc,CAACI,UAAU,GAAG,IAAI,GAAGxG,IAAI,CAACyG,GAAG,EAAE,GAAGrO,gBAAgB,GAChE,KAAK;MAET,IAAI,CAACsH,MAAM,CACT,kBAAkB,EAClB,cAAc6G,UAAU,GAAG,EAAE,GAAG,MAAM,UAAU,EAChD,YAAY,EACZH,cAAc,CAACI,UAAU,CAC1B;MAED,IAAI,CAACD,UAAU,EAAE;QACf,IAAI,IAAI,CAACpH,OAAO,CAACuH,QAAQ,EAAE;UACzB,IAAIC,eAAe,GAAG,IAAI,CAAClK,yBAAyB;UACpD,MAAMmK,YAAY,GAAY,IAAIC,KAAK,CAACT,cAAc,EAAE;YACtDU,GAAG,EAAEA,CAACC,MAAW,EAAEC,IAAY,EAAEC,QAAa,KAAI;cAChD,IAAI,CAACN,eAAe,IAAIK,IAAI,KAAK,MAAM,EAAE;gBACvC;gBACAlK,OAAO,CAACI,IAAI,CACV,iWAAiW,CAClW;gBACDyJ,eAAe,GAAG,IAAI,EAAC;gBACvB,IAAI,CAAClK,yBAAyB,GAAG,IAAI,EAAC;;cAExC,OAAOyK,OAAO,CAACJ,GAAG,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;YAC5C;WACD,CAAC;UACFb,cAAc,GAAGQ,YAAY;;QAG/B,OAAO;UAAEhH,IAAI,EAAE;YAAEC,OAAO,EAAEuG;UAAc,CAAE;UAAE9G,KAAK,EAAE;QAAI,CAAE;;MAG3D,MAAM;QAAEO,OAAO;QAAEP;MAAK,CAAE,GAAG,MAAM,IAAI,CAAC6H,iBAAiB,CAACf,cAAc,CAACgB,aAAa,CAAC;MACrF,IAAI9H,KAAK,EAAE;QACT,OAAO;UAAEM,IAAI,EAAE;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAG3C,OAAO;QAAEM,IAAI,EAAE;UAAEC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAC1C,SAAS;MACR,IAAI,CAACI,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC;;EAE1C;EAEA;;;;;;;EAOA,MAAM2H,OAAOA,CAAC/B,GAAY;IACxB,IAAIA,GAAG,EAAE;MACP,OAAO,MAAM,IAAI,CAACgC,QAAQ,CAAChC,GAAG,CAAC;;IAGjC,MAAM,IAAI,CAAC9I,iBAAiB;IAE5B,MAAM4I,MAAM,GAAG,MAAM,IAAI,CAAClF,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACpD,OAAO,MAAM,IAAI,CAACoH,QAAQ,EAAE;IAC9B,CAAC,CAAC;IAEF,OAAOlC,MAAM;EACf;EAEQ,MAAMkC,QAAQA,CAAChC,GAAY;IACjC,IAAI;MACF,IAAIA,GAAG,EAAE;QACP,OAAO,MAAMhM,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACtC,GAAG,OAAO,EAAE;UAC3DK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB+J,GAAG,EAAEA,GAAG;UACRzD,KAAK,EAAEpI;SACR,CAAC;;MAGJ,OAAO,MAAM,IAAI,CAAC0L,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UAAExF,IAAI;UAAEN;QAAK,CAAE,GAAG8F,MAAM;QAC9B,IAAI9F,KAAK,EAAE;UACT,MAAMA,KAAK;;QAGb;QACA,IAAI,EAAC,CAAA5B,EAAA,GAAAkC,IAAI,CAACC,OAAO,cAAAnC,EAAA,uBAAAA,EAAA,CAAEoG,YAAY,KAAI,CAAC,IAAI,CAACpI,4BAA4B,EAAE;UACrE,OAAO;YAAEkE,IAAI,EAAE;cAAEkC,IAAI,EAAE;YAAI,CAAE;YAAExC,KAAK,EAAE,IAAIzG,uBAAuB;UAAE,CAAE;;QAGvE,OAAO,MAAMS,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACtC,GAAG,OAAO,EAAE;UAC3DK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB+J,GAAG,EAAE,CAAA3D,EAAA,IAAApC,EAAA,GAAAK,IAAI,CAACC,OAAO,cAAAN,EAAA,uBAAAA,EAAA,CAAEuE,YAAY,cAAAnC,EAAA,cAAAA,EAAA,GAAI6C,SAAS;UAC5C3C,KAAK,EAAEpI;SACR,CAAC;MACJ,CAAC,CAAC;KACH,CAAC,OAAO6F,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,IAAInG,yBAAyB,CAACmG,KAAK,CAAC,EAAE;UACpC;UACA;UAEA,MAAM,IAAI,CAACyB,cAAc,EAAE;UAC3B,MAAMjH,eAAe,CAAC,IAAI,CAACqF,OAAO,EAAE,GAAG,IAAI,CAAChE,UAAU,gBAAgB,CAAC;;QAGzE,OAAO;UAAEyE,IAAI,EAAE;YAAEkC,IAAI,EAAE;UAAI,CAAE;UAAExC;QAAK,CAAE;;MAGxC,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMiI,UAAUA,CACdC,UAA0B,EAC1BvL,OAAA,GAEI,EAAE;IAEN,MAAM,IAAI,CAACO,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAAC0D,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAACuH,WAAW,CAACD,UAAU,EAAEvL,OAAO,CAAC;IACpD,CAAC,CAAC;EACJ;EAEU,MAAMwL,WAAWA,CACzBD,UAA0B,EAC1BvL,OAAA,GAEI,EAAE;IAEN,IAAI;MACF,OAAO,MAAM,IAAI,CAACkJ,WAAW,CAAC,MAAOC,MAAM,IAAI;QAC7C,MAAM;UAAExF,IAAI,EAAE8H,WAAW;UAAEpI,KAAK,EAAE+F;QAAY,CAAE,GAAGD,MAAM;QACzD,IAAIC,YAAY,EAAE;UAChB,MAAMA,YAAY;;QAEpB,IAAI,CAACqC,WAAW,CAAC7H,OAAO,EAAE;UACxB,MAAM,IAAIhH,uBAAuB,EAAE;;QAErC,MAAMgH,OAAO,GAAY6H,WAAW,CAAC7H,OAAO;QAC5C,IAAIqC,aAAa,GAAkB,IAAI;QACvC,IAAIC,mBAAmB,GAAkB,IAAI;QAC7C,IAAI,IAAI,CAAC3G,QAAQ,KAAK,MAAM,IAAIgM,UAAU,CAACxF,KAAK,IAAI,IAAI,EAAE;UACxD;UAAC,CAACE,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAM7H,yBAAyB,CACrE,IAAI,CAAC6E,OAAO,EACZ,IAAI,CAAChE,UAAU,CAChB;;QAGH,MAAM;UAAEyE,IAAI;UAAEN,KAAK,EAAEqI;QAAS,CAAE,GAAG,MAAMrO,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACtC,GAAG,OAAO,EAAE;UACvFK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB6G,UAAU,EAAEnG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoG,eAAe;UACpCb,IAAI,EAAApE,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACCmK,UAAU;YACblF,cAAc,EAAEJ,aAAa;YAC7BK,qBAAqB,EAAEJ;UAAmB,EAC3C;UACDmD,GAAG,EAAEzF,OAAO,CAACiE,YAAY;UACzBjC,KAAK,EAAEpI;SACR,CAAC;QACF,IAAIkO,SAAS,EAAE,MAAMA,SAAS;QAC9B9H,OAAO,CAACiC,IAAI,GAAGlC,IAAI,CAACkC,IAAY;QAChC,MAAM,IAAI,CAACb,YAAY,CAACpB,OAAO,CAAC;QAChC,MAAM,IAAI,CAACF,qBAAqB,CAAC,cAAc,EAAEE,OAAO,CAAC;QACzD,OAAO;UAAED,IAAI,EAAE;YAAEkC,IAAI,EAAEjC,OAAO,CAACiC;UAAI,CAAE;UAAExC,KAAK,EAAE;QAAI,CAAE;MACtD,CAAC,CAAC;KACH,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE;UAAI,CAAE;UAAExC;QAAK,CAAE;;MAGxC,MAAMA,KAAK;;EAEf;EAEA;;;;;EAKA,MAAMsI,UAAUA,CAACxB,cAGhB;IACC,MAAM,IAAI,CAAC5J,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAAC0D,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAAC2H,WAAW,CAACzB,cAAc,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEU,MAAMyB,WAAWA,CAACzB,cAG3B;IACC,IAAI;MACF,IAAI,CAACA,cAAc,CAACtC,YAAY,IAAI,CAACsC,cAAc,CAACgB,aAAa,EAAE;QACjE,MAAM,IAAIvO,uBAAuB,EAAE;;MAGrC,MAAMiP,OAAO,GAAG9H,IAAI,CAACyG,GAAG,EAAE,GAAG,IAAI;MACjC,IAAIsB,SAAS,GAAGD,OAAO;MACvB,IAAIvB,UAAU,GAAG,IAAI;MACrB,IAAI1G,OAAO,GAAmB,IAAI;MAClC,MAAM;QAAEmI;MAAO,CAAE,GAAGvN,SAAS,CAAC2L,cAAc,CAACtC,YAAY,CAAC;MAC1D,IAAIkE,OAAO,CAACC,GAAG,EAAE;QACfF,SAAS,GAAGC,OAAO,CAACC,GAAG;QACvB1B,UAAU,GAAGwB,SAAS,IAAID,OAAO;;MAGnC,IAAIvB,UAAU,EAAE;QACd,MAAM;UAAE1G,OAAO,EAAEqI,gBAAgB;UAAE5I;QAAK,CAAE,GAAG,MAAM,IAAI,CAAC6H,iBAAiB,CACvEf,cAAc,CAACgB,aAAa,CAC7B;QACD,IAAI9H,KAAK,EAAE;UACT,OAAO;YAAEM,IAAI,EAAE;cAAEkC,IAAI,EAAE,IAAI;cAAEjC,OAAO,EAAE;YAAI,CAAE;YAAEP,KAAK,EAAEA;UAAK,CAAE;;QAG9D,IAAI,CAAC4I,gBAAgB,EAAE;UACrB,OAAO;YAAEtI,IAAI,EAAE;cAAEkC,IAAI,EAAE,IAAI;cAAEjC,OAAO,EAAE;YAAI,CAAE;YAAEP,KAAK,EAAE;UAAI,CAAE;;QAE7DO,OAAO,GAAGqI,gBAAgB;OAC3B,MAAM;QACL,MAAM;UAAEtI,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAM,IAAI,CAACgI,QAAQ,CAAClB,cAAc,CAACtC,YAAY,CAAC;QACxE,IAAIxE,KAAK,EAAE;UACT,MAAMA,KAAK;;QAEbO,OAAO,GAAG;UACRiE,YAAY,EAAEsC,cAAc,CAACtC,YAAY;UACzCsD,aAAa,EAAEhB,cAAc,CAACgB,aAAa;UAC3CtF,IAAI,EAAElC,IAAI,CAACkC,IAAI;UACfqG,UAAU,EAAE,QAAQ;UACpBC,UAAU,EAAEL,SAAS,GAAGD,OAAO;UAC/BtB,UAAU,EAAEuB;SACb;QACD,MAAM,IAAI,CAAC9G,YAAY,CAACpB,OAAO,CAAC;QAChC,MAAM,IAAI,CAACF,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;;MAGxD,OAAO;QAAED,IAAI,EAAE;UAAEkC,IAAI,EAAEjC,OAAO,CAACiC,IAAI;UAAEjC;QAAO,CAAE;QAAEP,KAAK,EAAE;MAAI,CAAE;KAC9D,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEC,OAAO,EAAE,IAAI;YAAEiC,IAAI,EAAE;UAAI,CAAE;UAAExC;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;;;;EAMA,MAAM+I,cAAcA,CAACjC,cAA0C;IAC7D,MAAM,IAAI,CAAC5J,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAAC0D,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAACoI,eAAe,CAAClC,cAAc,CAAC;IACnD,CAAC,CAAC;EACJ;EAEU,MAAMkC,eAAeA,CAAClC,cAE/B;IACC,IAAI;MACF,OAAO,MAAM,IAAI,CAACjB,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,IAAI,CAACgB,cAAc,EAAE;UACnB,MAAM;YAAExG,IAAI;YAAEN;UAAK,CAAE,GAAG8F,MAAM;UAC9B,IAAI9F,KAAK,EAAE;YACT,MAAMA,KAAK;;UAGb8G,cAAc,GAAG,CAAA1I,EAAA,GAAAkC,IAAI,CAACC,OAAO,cAAAnC,EAAA,cAAAA,EAAA,GAAI8G,SAAS;;QAG5C,IAAI,EAAC4B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgB,aAAa,GAAE;UAClC,MAAM,IAAIvO,uBAAuB,EAAE;;QAGrC,MAAM;UAAEgH,OAAO;UAAEP;QAAK,CAAE,GAAG,MAAM,IAAI,CAAC6H,iBAAiB,CAACf,cAAc,CAACgB,aAAa,CAAC;QACrF,IAAI9H,KAAK,EAAE;UACT,OAAO;YAAEM,IAAI,EAAE;cAAEkC,IAAI,EAAE,IAAI;cAAEjC,OAAO,EAAE;YAAI,CAAE;YAAEP,KAAK,EAAEA;UAAK,CAAE;;QAG9D,IAAI,CAACO,OAAO,EAAE;UACZ,OAAO;YAAED,IAAI,EAAE;cAAEkC,IAAI,EAAE,IAAI;cAAEjC,OAAO,EAAE;YAAI,CAAE;YAAEP,KAAK,EAAE;UAAI,CAAE;;QAG7D,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAEjC,OAAO,CAACiC,IAAI;YAAEjC;UAAO,CAAE;UAAEP,KAAK,EAAE;QAAI,CAAE;MAC/D,CAAC,CAAC;KACH,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEkC,IAAI,EAAE,IAAI;YAAEjC,OAAO,EAAE;UAAI,CAAE;UAAEP;QAAK,CAAE;;MAGvD,MAAMA,KAAK;;EAEf;EAEA;;;EAGQ,MAAMqB,kBAAkBA,CAC9BP,MAAuC,EACvCI,eAAuB;IAQvB,IAAI;MACF,IAAI,CAAC3G,SAAS,EAAE,EAAE,MAAM,IAAInB,8BAA8B,CAAC,sBAAsB,CAAC;MAElF;MACA,IAAI0H,MAAM,CAACd,KAAK,IAAIc,MAAM,CAACmI,iBAAiB,IAAInI,MAAM,CAACoI,UAAU,EAAE;QACjE;QACA;QACA,MAAM,IAAI9P,8BAA8B,CACtC0H,MAAM,CAACmI,iBAAiB,IAAI,iDAAiD,EAC7E;UACEjJ,KAAK,EAAEc,MAAM,CAACd,KAAK,IAAI,mBAAmB;UAC1CwB,IAAI,EAAEV,MAAM,CAACoI,UAAU,IAAI;SAC5B,CACF;;MAGH;MACA,QAAQhI,eAAe;QACrB,KAAK,UAAU;UACb,IAAI,IAAI,CAAChF,QAAQ,KAAK,MAAM,EAAE;YAC5B,MAAM,IAAI7C,8BAA8B,CAAC,4BAA4B,CAAC;;UAExE;QACF,KAAK,MAAM;UACT,IAAI,IAAI,CAAC6C,QAAQ,KAAK,UAAU,EAAE;YAChC,MAAM,IAAI9C,8BAA8B,CAAC,sCAAsC,CAAC;;UAElF;QACF;QACA;;MAGF;MACA,IAAI8H,eAAe,KAAK,MAAM,EAAE;QAC9B,IAAI,CAACd,MAAM,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,EAAE,IAAI,CAAC;QAC5D,IAAI,CAACU,MAAM,CAACU,IAAI,EAAE,MAAM,IAAInI,8BAA8B,CAAC,mBAAmB,CAAC;QAC/E,MAAM;UAAEiH,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAM,IAAI,CAACgE,uBAAuB,CAAClD,MAAM,CAACU,IAAI,CAAC;QACvE,IAAIxB,KAAK,EAAE,MAAMA,KAAK;QAEtB,MAAMpE,GAAG,GAAG,IAAIuN,GAAG,CAACpI,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;QACzCrF,GAAG,CAACwN,YAAY,CAACC,MAAM,CAAC,MAAM,CAAC;QAE/BtI,MAAM,CAACuI,OAAO,CAACC,YAAY,CAACxI,MAAM,CAACuI,OAAO,CAACE,KAAK,EAAE,EAAE,EAAE5N,GAAG,CAAC6N,QAAQ,EAAE,CAAC;QAErE,OAAO;UAAEnJ,IAAI,EAAE;YAAEC,OAAO,EAAED,IAAI,CAACC,OAAO;YAAEmB,YAAY,EAAE;UAAI,CAAE;UAAE1B,KAAK,EAAE;QAAI,CAAE;;MAG7E,MAAM;QACJ0J,cAAc;QACdC,sBAAsB;QACtBnF,YAAY;QACZsD,aAAa;QACbgB,UAAU;QACV5B,UAAU;QACV2B;MAAU,CACX,GAAG/H,MAAM;MAEV,IAAI,CAAC0D,YAAY,IAAI,CAACsE,UAAU,IAAI,CAAChB,aAAa,IAAI,CAACe,UAAU,EAAE;QACjE,MAAM,IAAIzP,8BAA8B,CAAC,2BAA2B,CAAC;;MAGvE,MAAMoP,OAAO,GAAGoB,IAAI,CAACC,KAAK,CAACnJ,IAAI,CAACyG,GAAG,EAAE,GAAG,IAAI,CAAC;MAC7C,MAAM2C,SAAS,GAAGC,QAAQ,CAACjB,UAAU,CAAC;MACtC,IAAIL,SAAS,GAAGD,OAAO,GAAGsB,SAAS;MAEnC,IAAI5C,UAAU,EAAE;QACduB,SAAS,GAAGsB,QAAQ,CAAC7C,UAAU,CAAC;;MAGlC,MAAM8C,iBAAiB,GAAGvB,SAAS,GAAGD,OAAO;MAC7C,IAAIwB,iBAAiB,GAAG,IAAI,IAAIjR,6BAA6B,EAAE;QAC7DyE,OAAO,CAACI,IAAI,CACV,iEAAiEoM,iBAAiB,iCAAiCF,SAAS,GAAG,CAChI;;MAGH,MAAMG,QAAQ,GAAGxB,SAAS,GAAGqB,SAAS;MACtC,IAAItB,OAAO,GAAGyB,QAAQ,IAAI,GAAG,EAAE;QAC7BzM,OAAO,CAACI,IAAI,CACV,iGAAiG,EACjGqM,QAAQ,EACRxB,SAAS,EACTD,OAAO,CACR;OACF,MAAM,IAAIA,OAAO,GAAGyB,QAAQ,GAAG,CAAC,EAAE;QACjCzM,OAAO,CAACI,IAAI,CACV,8GAA8G,EAC9GqM,QAAQ,EACRxB,SAAS,EACTD,OAAO,CACR;;MAGH,MAAM;QAAElI,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAM,IAAI,CAACgI,QAAQ,CAACxD,YAAY,CAAC;MACzD,IAAIxE,KAAK,EAAE,MAAMA,KAAK;MAEtB,MAAMO,OAAO,GAAY;QACvBmJ,cAAc;QACdC,sBAAsB;QACtBnF,YAAY;QACZsE,UAAU,EAAEgB,SAAS;QACrB5C,UAAU,EAAEuB,SAAS;QACrBX,aAAa;QACbe,UAAU;QACVrG,IAAI,EAAElC,IAAI,CAACkC;OACZ;MAED;MACAzB,MAAM,CAACC,QAAQ,CAACkJ,IAAI,GAAG,EAAE;MACzB,IAAI,CAAC9J,MAAM,CAAC,uBAAuB,EAAE,+BAA+B,CAAC;MAErE,OAAO;QAAEE,IAAI,EAAE;UAAEC,OAAO;UAAEmB,YAAY,EAAEZ,MAAM,CAACsE;QAAI,CAAE;QAAEpF,KAAK,EAAE;MAAI,CAAE;KACrE,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEC,OAAO,EAAE,IAAI;YAAEmB,YAAY,EAAE;UAAI,CAAE;UAAE1B;QAAK,CAAE;;MAG/D,MAAMA,KAAK;;EAEf;EAEA;;;EAGQmB,wBAAwBA,CAACL,MAAuC;IACtE,OAAOqJ,OAAO,CAACrJ,MAAM,CAAC0D,YAAY,IAAI1D,MAAM,CAACmI,iBAAiB,CAAC;EACjE;EAEA;;;EAGQ,MAAM7H,eAAeA,CAACN,MAAuC;IACnE,MAAMsJ,qBAAqB,GAAG,MAAM9P,YAAY,CAC9C,IAAI,CAACuF,OAAO,EACZ,GAAG,IAAI,CAAChE,UAAU,gBAAgB,CACnC;IAED,OAAO,CAAC,EAAEiF,MAAM,CAACU,IAAI,IAAI4I,qBAAqB,CAAC;EACjD;EAEA;;;;;;;;EAQA,MAAMC,OAAOA,CAAC1N,OAAA,GAAmB;IAAE2N,KAAK,EAAE;EAAQ,CAAE;IAClD,MAAM,IAAI,CAACpN,iBAAiB;IAE5B,OAAO,MAAM,IAAI,CAAC0D,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MAC5C,OAAO,MAAM,IAAI,CAAC2J,QAAQ,CAAC5N,OAAO,CAAC;IACrC,CAAC,CAAC;EACJ;EAEU,MAAM4N,QAAQA,CACtB;IAAED;EAAK,IAAc;IAAEA,KAAK,EAAE;EAAQ,CAAE;IAExC,OAAO,MAAM,IAAI,CAACzE,WAAW,CAAC,MAAOC,MAAM,IAAI;;MAC7C,MAAM;QAAExF,IAAI;QAAEN,KAAK,EAAE+F;MAAY,CAAE,GAAGD,MAAM;MAC5C,IAAIC,YAAY,EAAE;QAChB,OAAO;UAAE/F,KAAK,EAAE+F;QAAY,CAAE;;MAEhC,MAAMyE,WAAW,GAAG,CAAApM,EAAA,GAAAkC,IAAI,CAACC,OAAO,cAAAnC,EAAA,uBAAAA,EAAA,CAAEoG,YAAY;MAC9C,IAAIgG,WAAW,EAAE;QACf,MAAM;UAAExK;QAAK,CAAE,GAAG,MAAM,IAAI,CAAC/B,KAAK,CAACoM,OAAO,CAACG,WAAW,EAAEF,KAAK,CAAC;QAC9D,IAAItK,KAAK,EAAE;UACT;UACA;UACA,IACE,EACEtG,cAAc,CAACsG,KAAK,CAAC,KACpBA,KAAK,CAACyK,MAAM,KAAK,GAAG,IAAIzK,KAAK,CAACyK,MAAM,KAAK,GAAG,IAAIzK,KAAK,CAACyK,MAAM,KAAK,GAAG,CAAC,CACvE,EACD;YACA,OAAO;cAAEzK;YAAK,CAAE;;;;MAItB,IAAIsK,KAAK,KAAK,QAAQ,EAAE;QACtB,MAAM,IAAI,CAAC7I,cAAc,EAAE;QAC3B,MAAMjH,eAAe,CAAC,IAAI,CAACqF,OAAO,EAAE,GAAG,IAAI,CAAChE,UAAU,gBAAgB,CAAC;;MAEzE,OAAO;QAAEmE,KAAK,EAAE;MAAI,CAAE;IACxB,CAAC,CAAC;EACJ;EAEA;;;;EAIA0K,iBAAiBA,CACfC,QAAmF;IAInF,MAAMC,EAAE,GAAWjQ,IAAI,EAAE;IACzB,MAAMkQ,YAAY,GAAiB;MACjCD,EAAE;MACFD,QAAQ;MACRG,WAAW,EAAEA,CAAA,KAAK;QAChB,IAAI,CAAC1K,MAAM,CAAC,gBAAgB,EAAE,uCAAuC,EAAEwK,EAAE,CAAC;QAE1E,IAAI,CAAC/N,mBAAmB,CAACwM,MAAM,CAACuB,EAAE,CAAC;MACrC;KACD;IAED,IAAI,CAACxK,MAAM,CAAC,sBAAsB,EAAE,6BAA6B,EAAEwK,EAAE,CAAC;IAEtE,IAAI,CAAC/N,mBAAmB,CAACkO,GAAG,CAACH,EAAE,EAAEC,YAAY,CAAC;IAC7C,CAAC,YAAW;MACX,MAAM,IAAI,CAAC3N,iBAAiB;MAE5B,MAAM,IAAI,CAAC0D,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;QACrC,IAAI,CAACoK,mBAAmB,CAACJ,EAAE,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,EAAC,CAAE;IAEJ,OAAO;MAAEtK,IAAI,EAAE;QAAEuK;MAAY;IAAE,CAAE;EACnC;EAEQ,MAAMG,mBAAmBA,CAACJ,EAAU;IAC1C,OAAO,MAAM,IAAI,CAAC/E,WAAW,CAAC,MAAOC,MAAM,IAAI;;MAC7C,IAAI;QACF,MAAM;UACJxF,IAAI,EAAE;YAAEC;UAAO,CAAE;UACjBP;QAAK,CACN,GAAG8F,MAAM;QACV,IAAI9F,KAAK,EAAE,MAAMA,KAAK;QAEtB,OAAM,CAAA5B,EAAA,OAAI,CAACvB,mBAAmB,CAAC2K,GAAG,CAACoD,EAAE,CAAC,cAAAxM,EAAA,uBAAAA,EAAA,CAAEuM,QAAQ,CAAC,iBAAiB,EAAEpK,OAAO,CAAC;QAC5E,IAAI,CAACH,MAAM,CAAC,iBAAiB,EAAE,aAAa,EAAEwK,EAAE,EAAE,SAAS,EAAErK,OAAO,CAAC;OACtE,CAAC,OAAO0K,GAAG,EAAE;QACZ,OAAM,CAAAhL,EAAA,OAAI,CAACpD,mBAAmB,CAAC2K,GAAG,CAACoD,EAAE,CAAC,cAAA3K,EAAA,uBAAAA,EAAA,CAAE0K,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC;QACzE,IAAI,CAACvK,MAAM,CAAC,iBAAiB,EAAE,aAAa,EAAEwK,EAAE,EAAE,OAAO,EAAEK,GAAG,CAAC;QAC/DzN,OAAO,CAACwC,KAAK,CAACiL,GAAG,CAAC;;IAEtB,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOA,MAAMC,qBAAqBA,CACzBxI,KAAa,EACb/F,OAAA,GAGI,EAAE;IAQN,IAAIiG,aAAa,GAAkB,IAAI;IACvC,IAAIC,mBAAmB,GAAkB,IAAI;IAE7C,IAAI,IAAI,CAAC3G,QAAQ,KAAK,MAAM,EAAE;MAC5B;MAAC,CAAC0G,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAM7H,yBAAyB,CACrE,IAAI,CAAC6E,OAAO,EACZ,IAAI,CAAChE,UAAU,EACf,IAAI,CAAC;OACN;;IAEH,IAAI;MACF,OAAO,MAAM7B,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,UAAU,EAAE;QAC/DsG,IAAI,EAAE;UACJQ,KAAK;UACLM,cAAc,EAAEJ,aAAa;UAC7BK,qBAAqB,EAAEJ,mBAAmB;UAC1CV,oBAAoB,EAAE;YAAEC,aAAa,EAAEzF,OAAO,CAAC2F;UAAY;SAC5D;QACDrG,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB6G,UAAU,EAAEnG,OAAO,CAACmG;OACrB,CAAC;KACH,CAAC,OAAO9C,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAG9B,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMmL,iBAAiBA,CAAA;;IASrB,IAAI;MACF,MAAM;QAAE7K,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAM,IAAI,CAAC+H,OAAO,EAAE;MAC5C,IAAI/H,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEM,IAAI,EAAE;UAAE8K,UAAU,EAAE,CAAAhN,EAAA,GAAAkC,IAAI,CAACkC,IAAI,CAAC4I,UAAU,cAAAhN,EAAA,cAAAA,EAAA,GAAI;QAAE,CAAE;QAAE4B,KAAK,EAAE;MAAI,CAAE;KACzE,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EACA;;;;EAIA,MAAMqL,YAAYA,CAACrJ,WAAuC;;IACxD,IAAI;MACF,MAAM;QAAE1B,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAM,IAAI,CAAC6F,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC9D,MAAM;UAAExF,IAAI;UAAEN;QAAK,CAAE,GAAG8F,MAAM;QAC9B,IAAI9F,KAAK,EAAE,MAAMA,KAAK;QACtB,MAAMpE,GAAG,GAAW,MAAM,IAAI,CAAC0P,kBAAkB,CAC/C,GAAG,IAAI,CAAC1P,GAAG,4BAA4B,EACvCoG,WAAW,CAACyB,QAAQ,EACpB;UACEX,UAAU,EAAE,CAAA1E,EAAA,GAAA4D,WAAW,CAACrF,OAAO,cAAAyB,EAAA,uBAAAA,EAAA,CAAE0E,UAAU;UAC3CY,MAAM,EAAE,CAAAzD,EAAA,GAAA+B,WAAW,CAACrF,OAAO,cAAAsD,EAAA,uBAAAA,EAAA,CAAEyD,MAAM;UACnCC,WAAW,EAAE,CAAAtB,EAAA,GAAAL,WAAW,CAACrF,OAAO,cAAA0F,EAAA,uBAAAA,EAAA,CAAEsB,WAAW;UAC7CC,mBAAmB,EAAE;SACtB,CACF;QACD,OAAO,MAAM5J,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,KAAK,EAAEtC,GAAG,EAAE;UAC5CK,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB+J,GAAG,EAAE,CAAAlB,EAAA,IAAAjB,EAAA,GAAAvD,IAAI,CAACC,OAAO,cAAAsD,EAAA,uBAAAA,EAAA,CAAEW,YAAY,cAAAM,EAAA,cAAAA,EAAA,GAAII;SACpC,CAAC;MACJ,CAAC,CAAC;MACF,IAAIlF,KAAK,EAAE,MAAMA,KAAK;MACtB,IAAIzF,SAAS,EAAE,IAAI,EAAC,CAAA6D,EAAA,GAAA4D,WAAW,CAACrF,OAAO,cAAAyB,EAAA,uBAAAA,EAAA,CAAEwF,mBAAmB,GAAE;QAC5D7C,MAAM,CAACC,QAAQ,CAACjD,MAAM,CAACuC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE1E,GAAG,CAAC;;MAEnC,OAAO;QAAE0E,IAAI,EAAE;UAAEmD,QAAQ,EAAEzB,WAAW,CAACyB,QAAQ;UAAE7H,GAAG,EAAE0E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE1E;QAAG,CAAE;QAAEoE,KAAK,EAAE;MAAI,CAAE;KACjF,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEmD,QAAQ,EAAEzB,WAAW,CAACyB,QAAQ;YAAE7H,GAAG,EAAE;UAAI,CAAE;UAAEoE;QAAK,CAAE;;MAEvE,MAAMA,KAAK;;EAEf;EAEA;;;EAGA,MAAMuL,cAAcA,CAACC,QAAsB;IAOzC,IAAI;MACF,OAAO,MAAM,IAAI,CAAC3F,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UAAExF,IAAI;UAAEN;QAAK,CAAE,GAAG8F,MAAM;QAC9B,IAAI9F,KAAK,EAAE;UACT,MAAMA,KAAK;;QAEb,OAAO,MAAMhG,QAAQ,CACnB,IAAI,CAACkE,KAAK,EACV,QAAQ,EACR,GAAG,IAAI,CAACtC,GAAG,oBAAoB4P,QAAQ,CAACC,WAAW,EAAE,EACrD;UACExP,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB+J,GAAG,EAAE,CAAA/F,EAAA,IAAA7B,EAAA,GAAAkC,IAAI,CAACC,OAAO,cAAAnC,EAAA,uBAAAA,EAAA,CAAEoG,YAAY,cAAAvE,EAAA,cAAAA,EAAA,GAAIiF;SACpC,CACF;MACH,CAAC,CAAC;KACH,CAAC,OAAOlF,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EAEA;;;;EAIQ,MAAM0L,mBAAmBA,CAACC,YAAoB;IACpD,MAAMC,SAAS,GAAG,wBAAwBD,YAAY,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM;IAC5E,IAAI,CAACzL,MAAM,CAACwL,SAAS,EAAE,OAAO,CAAC;IAE/B,IAAI;MACF,MAAME,SAAS,GAAGpL,IAAI,CAACyG,GAAG,EAAE;MAE5B;MACA,OAAO,MAAMvM,SAAS,CACpB,MAAOmR,OAAO,IAAI;QAChB,IAAIA,OAAO,GAAG,CAAC,EAAE;UACf,MAAMlR,KAAK,CAAC,GAAG,GAAG+O,IAAI,CAACoC,GAAG,CAAC,CAAC,EAAED,OAAO,GAAG,CAAC,CAAC,CAAC,EAAC;;QAG9C,IAAI,CAAC3L,MAAM,CAACwL,SAAS,EAAE,oBAAoB,EAAEG,OAAO,CAAC;QAErD,OAAO,MAAM/R,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,iCAAiC,EAAE;UACtFsG,IAAI,EAAE;YAAE4F,aAAa,EAAE6D;UAAY,CAAE;UACrC1P,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBsG,KAAK,EAAEtI;SACR,CAAC;MACJ,CAAC,EACD,CAAC8R,OAAO,EAAE/L,KAAK,KAAI;QACjB,MAAMiM,mBAAmB,GAAG,GAAG,GAAGrC,IAAI,CAACoC,GAAG,CAAC,CAAC,EAAED,OAAO,CAAC;QACtD,OACE/L,KAAK,IACLpG,yBAAyB,CAACoG,KAAK,CAAC;QAChC;QACAU,IAAI,CAACyG,GAAG,EAAE,GAAG8E,mBAAmB,GAAGH,SAAS,GAAG/S,6BAA6B;MAEhF,CAAC,CACF;KACF,CAAC,OAAOiH,KAAK,EAAE;MACd,IAAI,CAACI,MAAM,CAACwL,SAAS,EAAE,OAAO,EAAE5L,KAAK,CAAC;MAEtC,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE;YAAEC,OAAO,EAAE,IAAI;YAAEiC,IAAI,EAAE;UAAI,CAAE;UAAExC;QAAK,CAAE;;MAEvD,MAAMA,KAAK;KACZ,SAAS;MACR,IAAI,CAACI,MAAM,CAACwL,SAAS,EAAE,KAAK,CAAC;;EAEjC;EAEQ5E,eAAeA,CAACD,YAAqB;IAC3C,MAAMmF,cAAc,GAClB,OAAOnF,YAAY,KAAK,QAAQ,IAChCA,YAAY,KAAK,IAAI,IACrB,cAAc,IAAIA,YAAY,IAC9B,eAAe,IAAIA,YAAY,IAC/B,YAAY,IAAIA,YAAY;IAE9B,OAAOmF,cAAc;EACvB;EAEQ,MAAM1I,qBAAqBA,CACjCC,QAAkB,EAClB9G,OAKC;IAED,MAAMf,GAAG,GAAW,MAAM,IAAI,CAAC0P,kBAAkB,CAAC,GAAG,IAAI,CAAC1P,GAAG,YAAY,EAAE6H,QAAQ,EAAE;MACnFX,UAAU,EAAEnG,OAAO,CAACmG,UAAU;MAC9BY,MAAM,EAAE/G,OAAO,CAAC+G,MAAM;MACtBC,WAAW,EAAEhH,OAAO,CAACgH;KACtB,CAAC;IAEF,IAAI,CAACvD,MAAM,CAAC,0BAA0B,EAAE,UAAU,EAAEqD,QAAQ,EAAE,SAAS,EAAE9G,OAAO,EAAE,KAAK,EAAEf,GAAG,CAAC;IAE7F;IACA,IAAIrB,SAAS,EAAE,IAAI,CAACoC,OAAO,CAACiH,mBAAmB,EAAE;MAC/C7C,MAAM,CAACC,QAAQ,CAACjD,MAAM,CAACnC,GAAG,CAAC;;IAG7B,OAAO;MAAE0E,IAAI,EAAE;QAAEmD,QAAQ;QAAE7H;MAAG,CAAE;MAAEoE,KAAK,EAAE;IAAI,CAAE;EACjD;EAEA;;;;EAIQ,MAAM6B,kBAAkBA,CAAA;;IAC9B,MAAM+J,SAAS,GAAG,uBAAuB;IACzC,IAAI,CAACxL,MAAM,CAACwL,SAAS,EAAE,OAAO,CAAC;IAE/B,IAAI;MACF,MAAM9E,cAAc,GAAG,MAAMxM,YAAY,CAAC,IAAI,CAACuF,OAAO,EAAE,IAAI,CAAChE,UAAU,CAAC;MACxE,IAAI,CAACuE,MAAM,CAACwL,SAAS,EAAE,sBAAsB,EAAE9E,cAAc,CAAC;MAE9D,IAAI,CAAC,IAAI,CAACE,eAAe,CAACF,cAAc,CAAC,EAAE;QACzC,IAAI,CAAC1G,MAAM,CAACwL,SAAS,EAAE,sBAAsB,CAAC;QAC9C,IAAI9E,cAAc,KAAK,IAAI,EAAE;UAC3B,MAAM,IAAI,CAACrF,cAAc,EAAE;;QAG7B;;MAGF,MAAM0K,iBAAiB,GACrB,CAAC,CAAA/N,EAAA,GAAA0I,cAAc,CAACI,UAAU,cAAA9I,EAAA,cAAAA,EAAA,GAAIgO,QAAQ,IAAI,IAAI,GAAG1L,IAAI,CAACyG,GAAG,EAAE,GAAGrO,gBAAgB;MAEhF,IAAI,CAACsH,MAAM,CACTwL,SAAS,EACT,cAAcO,iBAAiB,GAAG,EAAE,GAAG,MAAM,2BAA2BrT,gBAAgB,GAAG,CAC5F;MAED,IAAIqT,iBAAiB,EAAE;QACrB,IAAI,IAAI,CAACrQ,gBAAgB,IAAIgL,cAAc,CAACgB,aAAa,EAAE;UACzD,MAAM;YAAE9H;UAAK,CAAE,GAAG,MAAM,IAAI,CAAC6H,iBAAiB,CAACf,cAAc,CAACgB,aAAa,CAAC;UAE5E,IAAI9H,KAAK,EAAE;YACTxC,OAAO,CAACwC,KAAK,CAACA,KAAK,CAAC;YAEpB,IAAI,CAACpG,yBAAyB,CAACoG,KAAK,CAAC,EAAE;cACrC,IAAI,CAACI,MAAM,CACTwL,SAAS,EACT,iEAAiE,EACjE5L,KAAK,CACN;cACD,MAAM,IAAI,CAACyB,cAAc,EAAE;;;;OAIlC,MAAM;QACL;QACA;QACA;QACA,MAAM,IAAI,CAACpB,qBAAqB,CAAC,WAAW,EAAEyG,cAAc,CAAC;;KAEhE,CAAC,OAAOmE,GAAG,EAAE;MACZ,IAAI,CAAC7K,MAAM,CAACwL,SAAS,EAAE,OAAO,EAAEX,GAAG,CAAC;MAEpCzN,OAAO,CAACwC,KAAK,CAACiL,GAAG,CAAC;MAClB;KACD,SAAS;MACR,IAAI,CAAC7K,MAAM,CAACwL,SAAS,EAAE,KAAK,CAAC;;EAEjC;EAEQ,MAAM/D,iBAAiBA,CAAC8D,YAAoB;;IAClD,IAAI,CAACA,YAAY,EAAE;MACjB,MAAM,IAAIpS,uBAAuB,EAAE;;IAGrC;IACA,IAAI,IAAI,CAAC0D,kBAAkB,EAAE;MAC3B,OAAO,IAAI,CAACA,kBAAkB,CAACoP,OAAO;;IAGxC,MAAMT,SAAS,GAAG,sBAAsBD,YAAY,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM;IAE1E,IAAI,CAACzL,MAAM,CAACwL,SAAS,EAAE,OAAO,CAAC;IAE/B,IAAI;MACF,IAAI,CAAC3O,kBAAkB,GAAG,IAAI5C,QAAQ,EAA0B;MAEhE,MAAM;QAAEiG,IAAI;QAAEN;MAAK,CAAE,GAAG,MAAM,IAAI,CAAC0L,mBAAmB,CAACC,YAAY,CAAC;MACpE,IAAI3L,KAAK,EAAE,MAAMA,KAAK;MACtB,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,MAAM,IAAIhH,uBAAuB,EAAE;MAEtD,MAAM,IAAI,CAACoI,YAAY,CAACrB,IAAI,CAACC,OAAO,CAAC;MACrC,MAAM,IAAI,CAACF,qBAAqB,CAAC,iBAAiB,EAAEC,IAAI,CAACC,OAAO,CAAC;MAEjE,MAAMuF,MAAM,GAAG;QAAEvF,OAAO,EAAED,IAAI,CAACC,OAAO;QAAEP,KAAK,EAAE;MAAI,CAAE;MAErD,IAAI,CAAC/C,kBAAkB,CAACsJ,OAAO,CAACT,MAAM,CAAC;MAEvC,OAAOA,MAAM;KACd,CAAC,OAAO9F,KAAK,EAAE;MACd,IAAI,CAACI,MAAM,CAACwL,SAAS,EAAE,OAAO,EAAE5L,KAAK,CAAC;MAEtC,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,MAAM8F,MAAM,GAAG;UAAEvF,OAAO,EAAE,IAAI;UAAEP;QAAK,CAAE;QAEvC,IAAI,CAACpG,yBAAyB,CAACoG,KAAK,CAAC,EAAE;UACrC,MAAM,IAAI,CAACyB,cAAc,EAAE;;QAG7B,CAAArD,EAAA,OAAI,CAACnB,kBAAkB,cAAAmB,EAAA,uBAAAA,EAAA,CAAEmI,OAAO,CAACT,MAAM,CAAC;QAExC,OAAOA,MAAM;;MAGf,CAAA7F,EAAA,OAAI,CAAChD,kBAAkB,cAAAgD,EAAA,uBAAAA,EAAA,CAAEqM,MAAM,CAACtM,KAAK,CAAC;MACtC,MAAMA,KAAK;KACZ,SAAS;MACR,IAAI,CAAC/C,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACmD,MAAM,CAACwL,SAAS,EAAE,KAAK,CAAC;;EAEjC;EAEQ,MAAMvL,qBAAqBA,CACjCF,KAAsB,EACtBI,OAAuB,EACvBgM,SAAS,GAAG,IAAI;IAEhB,MAAMX,SAAS,GAAG,0BAA0BzL,KAAK,GAAG;IACpD,IAAI,CAACC,MAAM,CAACwL,SAAS,EAAE,OAAO,EAAErL,OAAO,EAAE,eAAegM,SAAS,EAAE,CAAC;IAEpE,IAAI;MACF,IAAI,IAAI,CAACjP,gBAAgB,IAAIiP,SAAS,EAAE;QACtC,IAAI,CAACjP,gBAAgB,CAACkP,WAAW,CAAC;UAAErM,KAAK;UAAEI;QAAO,CAAE,CAAC;;MAGvD,MAAMkM,MAAM,GAAU,EAAE;MACxB,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/P,mBAAmB,CAACgQ,MAAM,EAAE,CAAC,CAACC,GAAG,CAAC,MAAOC,CAAC,IAAI;QAC7E,IAAI;UACF,MAAMA,CAAC,CAACpC,QAAQ,CAACxK,KAAK,EAAEI,OAAO,CAAC;SACjC,CAAC,OAAOR,CAAM,EAAE;UACf0M,MAAM,CAACjG,IAAI,CAACzG,CAAC,CAAC;;MAElB,CAAC,CAAC;MAEF,MAAMuG,OAAO,CAACI,GAAG,CAACgG,QAAQ,CAAC;MAE3B,IAAID,MAAM,CAACpG,MAAM,GAAG,CAAC,EAAE;QACrB,KAAK,IAAI2G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACpG,MAAM,EAAE2G,CAAC,IAAI,CAAC,EAAE;UACzCxP,OAAO,CAACwC,KAAK,CAACyM,MAAM,CAACO,CAAC,CAAC,CAAC;;QAG1B,MAAMP,MAAM,CAAC,CAAC,CAAC;;KAElB,SAAS;MACR,IAAI,CAACrM,MAAM,CAACwL,SAAS,EAAE,KAAK,CAAC;;EAEjC;EAEA;;;;EAIQ,MAAMjK,YAAYA,CAACpB,OAAgB;IACzC,IAAI,CAACH,MAAM,CAAC,iBAAiB,EAAEG,OAAO,CAAC;IACvC;IACA;IACA,IAAI,CAACpD,yBAAyB,GAAG,IAAI;IACrC,MAAMzC,YAAY,CAAC,IAAI,CAACmF,OAAO,EAAE,IAAI,CAAChE,UAAU,EAAE0E,OAAO,CAAC;EAC5D;EAEQ,MAAMkB,cAAcA,CAAA;IAC1B,IAAI,CAACrB,MAAM,CAAC,mBAAmB,CAAC;IAEhC,MAAM5F,eAAe,CAAC,IAAI,CAACqF,OAAO,EAAE,IAAI,CAAChE,UAAU,CAAC;IACpD,MAAM,IAAI,CAACwE,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;EACtD;EAEA;;;;;;EAMQ4M,gCAAgCA,CAAA;IACtC,IAAI,CAAC7M,MAAM,CAAC,qCAAqC,CAAC;IAElD,MAAMuK,QAAQ,GAAG,IAAI,CAAC3N,yBAAyB;IAC/C,IAAI,CAACA,yBAAyB,GAAG,IAAI;IAErC,IAAI;MACF,IAAI2N,QAAQ,IAAIpQ,SAAS,EAAE,KAAIwG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEmM,mBAAmB,GAAE;QAC1DnM,MAAM,CAACmM,mBAAmB,CAAC,kBAAkB,EAAEvC,QAAQ,CAAC;;KAE3D,CAAC,OAAO5K,CAAC,EAAE;MACVvC,OAAO,CAACwC,KAAK,CAAC,2CAA2C,EAAED,CAAC,CAAC;;EAEjE;EAEA;;;;EAIQ,MAAMoN,iBAAiBA,CAAA;IAC7B,MAAM,IAAI,CAACC,gBAAgB,EAAE;IAE7B,IAAI,CAAChN,MAAM,CAAC,sBAAsB,CAAC;IAEnC,MAAMiN,MAAM,GAAGC,WAAW,CAAC,MAAM,IAAI,CAACC,qBAAqB,EAAE,EAAExU,6BAA6B,CAAC;IAC7F,IAAI,CAACgE,iBAAiB,GAAGsQ,MAAM;IAE/B,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACG,KAAK,KAAK,UAAU,EAAE;MAC9E;MACA;MACA;MACA;MACA;MACA;MACAH,MAAM,CAACG,KAAK,EAAE;MACd;KACD,MAAM,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAI,OAAOA,IAAI,CAACC,UAAU,KAAK,UAAU,EAAE;MAC/E;MACA;MACA;MACAD,IAAI,CAACC,UAAU,CAACL,MAAM,CAAC;;IAGzB;IACA;IACA;IACAzL,UAAU,CAAC,YAAW;MACpB,MAAM,IAAI,CAAC1E,iBAAiB;MAC5B,MAAM,IAAI,CAACqQ,qBAAqB,EAAE;IACpC,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;;;;EAIQ,MAAMH,gBAAgBA,CAAA;IAC5B,IAAI,CAAChN,MAAM,CAAC,qBAAqB,CAAC;IAElC,MAAMiN,MAAM,GAAG,IAAI,CAACtQ,iBAAiB;IACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;IAE7B,IAAIsQ,MAAM,EAAE;MACVM,aAAa,CAACN,MAAM,CAAC;;EAEzB;EAEA;;;;;;;;;;;;;;;;;;;;;;EAsBA,MAAMO,gBAAgBA,CAAA;IACpB,IAAI,CAACX,gCAAgC,EAAE;IACvC,MAAM,IAAI,CAACE,iBAAiB,EAAE;EAChC;EAEA;;;;;;;;EAQA,MAAMU,eAAeA,CAAA;IACnB,IAAI,CAACZ,gCAAgC,EAAE;IACvC,MAAM,IAAI,CAACG,gBAAgB,EAAE;EAC/B;EAEA;;;EAGQ,MAAMG,qBAAqBA,CAAA;IACjC,IAAI,CAACnN,MAAM,CAAC,0BAA0B,EAAE,OAAO,CAAC;IAEhD,IAAI;MACF,MAAM,IAAI,CAACQ,YAAY,CAAC,CAAC,EAAE,YAAW;QACpC,IAAI;UACF,MAAMuG,GAAG,GAAGzG,IAAI,CAACyG,GAAG,EAAE;UAEtB,IAAI;YACF,OAAO,MAAM,IAAI,CAACtB,WAAW,CAAC,MAAOC,MAAM,IAAI;cAC7C,MAAM;gBACJxF,IAAI,EAAE;kBAAEC;gBAAO;cAAE,CAClB,GAAGuF,MAAM;cAEV,IAAI,CAACvF,OAAO,IAAI,CAACA,OAAO,CAACuH,aAAa,IAAI,CAACvH,OAAO,CAAC2G,UAAU,EAAE;gBAC7D,IAAI,CAAC9G,MAAM,CAAC,0BAA0B,EAAE,YAAY,CAAC;gBACrD;;cAGF;cACA,MAAM0N,cAAc,GAAGlE,IAAI,CAACmE,KAAK,CAC/B,CAACxN,OAAO,CAAC2G,UAAU,GAAG,IAAI,GAAGC,GAAG,IAAIpO,6BAA6B,CAClE;cAED,IAAI,CAACqH,MAAM,CACT,0BAA0B,EAC1B,2BAA2B0N,cAAc,wBAAwB/U,6BAA6B,4BAA4BC,2BAA2B,QAAQ,CAC9J;cAED,IAAI8U,cAAc,IAAI9U,2BAA2B,EAAE;gBACjD,MAAM,IAAI,CAAC6O,iBAAiB,CAACtH,OAAO,CAACuH,aAAa,CAAC;;YAEvD,CAAC,CAAC;WACH,CAAC,OAAO/H,CAAM,EAAE;YACfvC,OAAO,CAACwC,KAAK,CACX,wEAAwE,EACxED,CAAC,CACF;;SAEJ,SAAS;UACR,IAAI,CAACK,MAAM,CAAC,0BAA0B,EAAE,KAAK,CAAC;;MAElD,CAAC,CAAC;KACH,CAAC,OAAOL,CAAM,EAAE;MACf,IAAIA,CAAC,CAACiO,gBAAgB,IAAIjO,CAAC,YAAYvE,uBAAuB,EAAE;QAC9D,IAAI,CAAC4E,MAAM,CAAC,4CAA4C,CAAC;OAC1D,MAAM;QACL,MAAML,CAAC;;;EAGb;EAEA;;;;;EAKQ,MAAM+B,uBAAuBA,CAAA;IACnC,IAAI,CAAC1B,MAAM,CAAC,4BAA4B,CAAC;IAEzC,IAAI,CAAC7F,SAAS,EAAE,IAAI,EAACwG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEb,gBAAgB,GAAE;MAC7C,IAAI,IAAI,CAACpE,gBAAgB,EAAE;QACzB;QACA,IAAI,CAAC8R,gBAAgB,EAAE;;MAGzB,OAAO,KAAK;;IAGd,IAAI;MACF,IAAI,CAAC5Q,yBAAyB,GAAG,YAAY,MAAM,IAAI,CAACiR,oBAAoB,CAAC,KAAK,CAAC;MAEnFlN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEb,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAClD,yBAAyB,CAAC;MAE5E;MACA;MACA,MAAM,IAAI,CAACiR,oBAAoB,CAAC,IAAI,CAAC,EAAC;KACvC,CAAC,OAAOjO,KAAK,EAAE;MACdxC,OAAO,CAACwC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;EAEnD;EAEA;;;EAGQ,MAAMiO,oBAAoBA,CAACC,oBAA6B;IAC9D,MAAMC,UAAU,GAAG,yBAAyBD,oBAAoB,GAAG;IACnE,IAAI,CAAC9N,MAAM,CAAC+N,UAAU,EAAE,iBAAiB,EAAEC,QAAQ,CAACC,eAAe,CAAC;IAEpE,IAAID,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;MAC1C,IAAI,IAAI,CAACvS,gBAAgB,EAAE;QACzB;QACA;QACA,IAAI,CAACqR,iBAAiB,EAAE;;MAG1B,IAAI,CAACe,oBAAoB,EAAE;QACzB;QACA;QACA;QACA;QACA,MAAM,IAAI,CAAChR,iBAAiB;QAE5B,MAAM,IAAI,CAAC0D,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;UACrC,IAAIwN,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;YAC1C,IAAI,CAACjO,MAAM,CACT+N,UAAU,EACV,0GAA0G,CAC3G;YAED;YACA;;UAGF;UACA,MAAM,IAAI,CAACtM,kBAAkB,EAAE;QACjC,CAAC,CAAC;;KAEL,MAAM,IAAIuM,QAAQ,CAACC,eAAe,KAAK,QAAQ,EAAE;MAChD,IAAI,IAAI,CAACvS,gBAAgB,EAAE;QACzB,IAAI,CAACsR,gBAAgB,EAAE;;;EAG7B;EAEA;;;;;;EAMQ,MAAM9B,kBAAkBA,CAC9B1P,GAAW,EACX6H,QAAkB,EAClB9G,OAKC;IAED,MAAM2R,SAAS,GAAa,CAAC,YAAYC,kBAAkB,CAAC9K,QAAQ,CAAC,EAAE,CAAC;IACxE,IAAI9G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmG,UAAU,EAAE;MACvBwL,SAAS,CAAC9H,IAAI,CAAC,eAAe+H,kBAAkB,CAAC5R,OAAO,CAACmG,UAAU,CAAC,EAAE,CAAC;;IAEzE,IAAInG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+G,MAAM,EAAE;MACnB4K,SAAS,CAAC9H,IAAI,CAAC,UAAU+H,kBAAkB,CAAC5R,OAAO,CAAC+G,MAAM,CAAC,EAAE,CAAC;;IAEhE,IAAI,IAAI,CAACxH,QAAQ,KAAK,MAAM,EAAE;MAC5B,MAAM,CAAC0G,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAM7H,yBAAyB,CAC1E,IAAI,CAAC6E,OAAO,EACZ,IAAI,CAAChE,UAAU,CAChB;MAED,MAAM2S,UAAU,GAAG,IAAIC,eAAe,CAAC;QACrCzL,cAAc,EAAE,GAAGuL,kBAAkB,CAAC3L,aAAa,CAAC,EAAE;QACtDK,qBAAqB,EAAE,GAAGsL,kBAAkB,CAAC1L,mBAAmB,CAAC;OAClE,CAAC;MACFyL,SAAS,CAAC9H,IAAI,CAACgI,UAAU,CAAC/E,QAAQ,EAAE,CAAC;;IAEvC,IAAI9M,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgH,WAAW,EAAE;MACxB,MAAM+K,KAAK,GAAG,IAAID,eAAe,CAAC9R,OAAO,CAACgH,WAAW,CAAC;MACtD2K,SAAS,CAAC9H,IAAI,CAACkI,KAAK,CAACjF,QAAQ,EAAE,CAAC;;IAElC,IAAI9M,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiH,mBAAmB,EAAE;MAChC0K,SAAS,CAAC9H,IAAI,CAAC,sBAAsB7J,OAAO,CAACiH,mBAAmB,EAAE,CAAC;;IAGrE,OAAO,GAAGhI,GAAG,IAAI0S,SAAS,CAACK,IAAI,CAAC,GAAG,CAAC,EAAE;EACxC;EAEQ,MAAMvP,SAASA,CAAC0B,MAAyB;IAC/C,IAAI;MACF,OAAO,MAAM,IAAI,CAAC+E,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UAAExF,IAAI,EAAE8H,WAAW;UAAEpI,KAAK,EAAE+F;QAAY,CAAE,GAAGD,MAAM;QACzD,IAAIC,YAAY,EAAE;UAChB,OAAO;YAAEzF,IAAI,EAAE,IAAI;YAAEN,KAAK,EAAE+F;UAAY,CAAE;;QAG5C,OAAO,MAAM/L,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAACtC,GAAG,YAAYkF,MAAM,CAAC8N,QAAQ,EAAE,EAAE;UACpF3S,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB+J,GAAG,EAAE,CAAA5H,EAAA,GAAAgK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE7H,OAAO,cAAAnC,EAAA,uBAAAA,EAAA,CAAEoG;SAC5B,CAAC;MACJ,CAAC,CAAC;KACH,CAAC,OAAOxE,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EAOQ,MAAMd,OAAOA,CAAC4B,MAAuB;IAC3C,IAAI;MACF,OAAO,MAAM,IAAI,CAAC+E,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UAAExF,IAAI,EAAE8H,WAAW;UAAEpI,KAAK,EAAE+F;QAAY,CAAE,GAAGD,MAAM;QACzD,IAAIC,YAAY,EAAE;UAChB,OAAO;YAAEzF,IAAI,EAAE,IAAI;YAAEN,KAAK,EAAE+F;UAAY,CAAE;;QAG5C,MAAM7D,IAAI,GAAApE,MAAA,CAAAC,MAAA;UACR8Q,aAAa,EAAE/N,MAAM,CAACgO,YAAY;UAClCC,WAAW,EAAEjO,MAAM,CAACkO;QAAU,GAC1BlO,MAAM,CAACkO,UAAU,KAAK,OAAO,GAAG;UAAE9L,KAAK,EAAEpC,MAAM,CAACoC;QAAK,CAAE,GAAG;UAAE+L,MAAM,EAAEnO,MAAM,CAACmO;QAAM,CAAG,CACzF;QAED,MAAM;UAAE3O,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAMhG,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAACtC,GAAG,UAAU,EAAE;UAChFsG,IAAI;UACJjG,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB+J,GAAG,EAAE,CAAA5H,EAAA,GAAAgK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE7H,OAAO,cAAAnC,EAAA,uBAAAA,EAAA,CAAEoG;SAC5B,CAAC;QAEF,IAAIxE,KAAK,EAAE;UACT,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAK,CAAE;;QAG9B,IAAIc,MAAM,CAACkO,UAAU,KAAK,MAAM,KAAI,CAAA/O,EAAA,GAAAK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4O,IAAI,cAAAjP,EAAA,uBAAAA,EAAA,CAAEkP,OAAO,GAAE;UACvD7O,IAAI,CAAC4O,IAAI,CAACC,OAAO,GAAG,4BAA4B7O,IAAI,CAAC4O,IAAI,CAACC,OAAO,EAAE;;QAGrE,OAAO;UAAE7O,IAAI;UAAEN,KAAK,EAAE;QAAI,CAAE;MAC9B,CAAC,CAAC;KACH,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;EAEA;;;EAGQ,MAAMjB,OAAOA,CAAC+B,MAAuB;IAC3C,OAAO,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACtC,IAAI;QACF,OAAO,MAAM,IAAI,CAACiF,WAAW,CAAC,MAAOC,MAAM,IAAI;;UAC7C,MAAM;YAAExF,IAAI,EAAE8H,WAAW;YAAEpI,KAAK,EAAE+F;UAAY,CAAE,GAAGD,MAAM;UACzD,IAAIC,YAAY,EAAE;YAChB,OAAO;cAAEzF,IAAI,EAAE,IAAI;cAAEN,KAAK,EAAE+F;YAAY,CAAE;;UAG5C,MAAM;YAAEzF,IAAI;YAAEN;UAAK,CAAE,GAAG,MAAMhG,QAAQ,CACpC,IAAI,CAACkE,KAAK,EACV,MAAM,EACN,GAAG,IAAI,CAACtC,GAAG,YAAYkF,MAAM,CAAC8N,QAAQ,SAAS,EAC/C;YACE1M,IAAI,EAAE;cAAEV,IAAI,EAAEV,MAAM,CAACU,IAAI;cAAE4N,YAAY,EAAEtO,MAAM,CAACuO;YAAW,CAAE;YAC7DpT,OAAO,EAAE,IAAI,CAACA,OAAO;YACrB+J,GAAG,EAAE,CAAA5H,EAAA,GAAAgK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE7H,OAAO,cAAAnC,EAAA,uBAAAA,EAAA,CAAEoG;WAC5B,CACF;UACD,IAAIxE,KAAK,EAAE;YACT,OAAO;cAAEM,IAAI,EAAE,IAAI;cAAEN;YAAK,CAAE;;UAG9B,MAAM,IAAI,CAAC2B,YAAY,CAAA7D,MAAA,CAAAC,MAAA;YACrBmJ,UAAU,EAAE0C,IAAI,CAACC,KAAK,CAACnJ,IAAI,CAACyG,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG7G,IAAI,CAACwI;UAAU,GACxDxI,IAAI,EACP;UACF,MAAM,IAAI,CAACD,qBAAqB,CAAC,wBAAwB,EAAEC,IAAI,CAAC;UAEhE,OAAO;YAAEA,IAAI;YAAEN;UAAK,CAAE;QACxB,CAAC,CAAC;OACH,CAAC,OAAOA,KAAK,EAAE;QACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;UACtB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAK,CAAE;;QAE9B,MAAMA,KAAK;;IAEf,CAAC,CAAC;EACJ;EAEA;;;EAGQ,MAAMV,UAAUA,CAACwB,MAA0B;IACjD,OAAO,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACtC,IAAI;QACF,OAAO,MAAM,IAAI,CAACiF,WAAW,CAAC,MAAOC,MAAM,IAAI;;UAC7C,MAAM;YAAExF,IAAI,EAAE8H,WAAW;YAAEpI,KAAK,EAAE+F;UAAY,CAAE,GAAGD,MAAM;UACzD,IAAIC,YAAY,EAAE;YAChB,OAAO;cAAEzF,IAAI,EAAE,IAAI;cAAEN,KAAK,EAAE+F;YAAY,CAAE;;UAG5C,OAAO,MAAM/L,QAAQ,CACnB,IAAI,CAACkE,KAAK,EACV,MAAM,EACN,GAAG,IAAI,CAACtC,GAAG,YAAYkF,MAAM,CAAC8N,QAAQ,YAAY,EAClD;YACE1M,IAAI,EAAE;cAAEiB,OAAO,EAAErC,MAAM,CAACqC;YAAO,CAAE;YACjClH,OAAO,EAAE,IAAI,CAACA,OAAO;YACrB+J,GAAG,EAAE,CAAA5H,EAAA,GAAAgK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE7H,OAAO,cAAAnC,EAAA,uBAAAA,EAAA,CAAEoG;WAC5B,CACF;QACH,CAAC,CAAC;OACH,CAAC,OAAOxE,KAAK,EAAE;QACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;UACtB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAK,CAAE;;QAE9B,MAAMA,KAAK;;IAEf,CAAC,CAAC;EACJ;EAEA;;;EAGQ,MAAMN,mBAAmBA,CAC/BoB,MAAmC;IAEnC;IACA;IAEA,MAAM;MAAER,IAAI,EAAEgP,aAAa;MAAEtP,KAAK,EAAEuP;IAAc,CAAE,GAAG,MAAM,IAAI,CAACjQ,UAAU,CAAC;MAC3EsP,QAAQ,EAAE9N,MAAM,CAAC8N;KAClB,CAAC;IACF,IAAIW,cAAc,EAAE;MAClB,OAAO;QAAEjP,IAAI,EAAE,IAAI;QAAEN,KAAK,EAAEuP;MAAc,CAAE;;IAG9C,OAAO,MAAM,IAAI,CAACxQ,OAAO,CAAC;MACxB6P,QAAQ,EAAE9N,MAAM,CAAC8N,QAAQ;MACzBS,WAAW,EAAEC,aAAa,CAAC1E,EAAE;MAC7BpJ,IAAI,EAAEV,MAAM,CAACU;KACd,CAAC;EACJ;EAEA;;;EAGQ,MAAMhC,YAAYA,CAAA;IACxB;IACA,MAAM;MACJc,IAAI,EAAE;QAAEkC;MAAI,CAAE;MACdxC,KAAK,EAAEqI;IAAS,CACjB,GAAG,MAAM,IAAI,CAACN,OAAO,EAAE;IACxB,IAAIM,SAAS,EAAE;MACb,OAAO;QAAE/H,IAAI,EAAE,IAAI;QAAEN,KAAK,EAAEqI;MAAS,CAAE;;IAGzC,MAAMmH,OAAO,GAAG,CAAAhN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgN,OAAO,KAAI,EAAE;IACnC,MAAMN,IAAI,GAAGM,OAAO,CAACC,MAAM,CACxBC,MAAM,IAAKA,MAAM,CAACX,WAAW,KAAK,MAAM,IAAIW,MAAM,CAACjF,MAAM,KAAK,UAAU,CAC1E;IACD,MAAMvH,KAAK,GAAGsM,OAAO,CAACC,MAAM,CACzBC,MAAM,IAAKA,MAAM,CAACX,WAAW,KAAK,OAAO,IAAIW,MAAM,CAACjF,MAAM,KAAK,UAAU,CAC3E;IAED,OAAO;MACLnK,IAAI,EAAE;QACJoG,GAAG,EAAE8I,OAAO;QACZN,IAAI;QACJhM;OACD;MACDlD,KAAK,EAAE;KACR;EACH;EAEA;;;EAGQ,MAAMJ,+BAA+BA,CAAA;IAC3C,OAAO,IAAI,CAACgB,YAAY,CAAC,CAAC,CAAC,EAAE,YAAW;MACtC,OAAO,MAAM,IAAI,CAACiF,WAAW,CAAC,MAAOC,MAAM,IAAI;;QAC7C,MAAM;UACJxF,IAAI,EAAE;YAAEC;UAAO,CAAE;UACjBP,KAAK,EAAE+F;QAAY,CACpB,GAAGD,MAAM;QACV,IAAIC,YAAY,EAAE;UAChB,OAAO;YAAEzF,IAAI,EAAE,IAAI;YAAEN,KAAK,EAAE+F;UAAY,CAAE;;QAE5C,IAAI,CAACxF,OAAO,EAAE;UACZ,OAAO;YACLD,IAAI,EAAE;cAAEqP,YAAY,EAAE,IAAI;cAAEC,SAAS,EAAE,IAAI;cAAEC,4BAA4B,EAAE;YAAE,CAAE;YAC/E7P,KAAK,EAAE;WACR;;QAGH,MAAM;UAAE0I;QAAO,CAAE,GAAGvN,SAAS,CAACoF,OAAO,CAACiE,YAAY,CAAC;QAEnD,IAAImL,YAAY,GAAwC,IAAI;QAE5D,IAAIjH,OAAO,CAACoH,GAAG,EAAE;UACfH,YAAY,GAAGjH,OAAO,CAACoH,GAAG;;QAG5B,IAAIF,SAAS,GAAwCD,YAAY;QAEjE,MAAMI,eAAe,GACnB,CAAA9P,EAAA,IAAA7B,EAAA,GAAAmC,OAAO,CAACiC,IAAI,CAACgN,OAAO,cAAApR,EAAA,uBAAAA,EAAA,CAAEqR,MAAM,CAAEC,MAAc,IAAKA,MAAM,CAACjF,MAAM,KAAK,UAAU,CAAC,cAAAxK,EAAA,cAAAA,EAAA,GAAI,EAAE;QAEtF,IAAI8P,eAAe,CAAC1J,MAAM,GAAG,CAAC,EAAE;UAC9BuJ,SAAS,GAAG,MAAM;;QAGpB,MAAMC,4BAA4B,GAAGnH,OAAO,CAACsH,GAAG,IAAI,EAAE;QAEtD,OAAO;UAAE1P,IAAI,EAAE;YAAEqP,YAAY;YAAEC,SAAS;YAAEC;UAA4B,CAAE;UAAE7P,KAAK,EAAE;QAAI,CAAE;MACzF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEQ,MAAMiQ,QAAQA,CAACC,GAAW,EAAE1R,IAAA,GAAwB;IAAEC,IAAI,EAAE;EAAE,CAAE;IACtE;IACA,IAAI0R,GAAG,GAAG3R,IAAI,CAACC,IAAI,CAAC2R,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACH,GAAG,KAAKA,GAAG,CAAC;IAClD,IAAIC,GAAG,EAAE;MACP,OAAOA,GAAG;;IAGZ;IACAA,GAAG,GAAG,IAAI,CAAC3R,IAAI,CAACC,IAAI,CAAC2R,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACH,GAAG,KAAKA,GAAG,CAAC;IAEnD;IACA,IAAIC,GAAG,IAAI,IAAI,CAACzR,cAAc,GAAGvF,QAAQ,GAAGuH,IAAI,CAACyG,GAAG,EAAE,EAAE;MACtD,OAAOgJ,GAAG;;IAEZ;IACA,MAAM;MAAE7P,IAAI;MAAEN;IAAK,CAAE,GAAG,MAAMhG,QAAQ,CAAC,IAAI,CAACkE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAACtC,GAAG,wBAAwB,EAAE;MAC7FK,OAAO,EAAE,IAAI,CAACA;KACf,CAAC;IACF,IAAI+D,KAAK,EAAE;MACT,MAAMA,KAAK;;IAEb,IAAI,CAACM,IAAI,CAAC7B,IAAI,IAAI6B,IAAI,CAAC7B,IAAI,CAAC4H,MAAM,KAAK,CAAC,EAAE;MACxC,MAAM,IAAItM,mBAAmB,CAAC,eAAe,CAAC;;IAEhD,IAAI,CAACyE,IAAI,GAAG8B,IAAI;IAChB,IAAI,CAAC5B,cAAc,GAAGgC,IAAI,CAACyG,GAAG,EAAE;IAChC;IACAgJ,GAAG,GAAG7P,IAAI,CAAC7B,IAAI,CAAC2R,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACH,GAAG,KAAKA,GAAG,CAAC;IACnD,IAAI,CAACC,GAAG,EAAE;MACR,MAAM,IAAIpW,mBAAmB,CAAC,uCAAuC,CAAC;;IAExE,OAAOoW,GAAG;EACZ;EAEA;;;;EAIA,MAAMG,SAASA,CACbtK,GAAY,EACZxH,IAAA,GAAwB;IAAEC,IAAI,EAAE;EAAE,CAAE;IASpC,IAAI;MACF,IAAI8F,KAAK,GAAGyB,GAAG;MACf,IAAI,CAACzB,KAAK,EAAE;QACV,MAAM;UAAEjE,IAAI;UAAEN;QAAK,CAAE,GAAG,MAAM,IAAI,CAACmG,UAAU,EAAE;QAC/C,IAAInG,KAAK,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE;UAC1B,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEN;UAAK,CAAE;;QAE9BuE,KAAK,GAAGjE,IAAI,CAACC,OAAO,CAACiE,YAAY;;MAGnC,MAAM;QACJ+L,MAAM;QACN7H,OAAO;QACP8H,SAAS;QACTC,GAAG,EAAE;UAAEF,MAAM,EAAEG,SAAS;UAAEhI,OAAO,EAAEiI;QAAU;MAAE,CAChD,GAAGxV,SAAS,CAACoJ,KAAK,CAAC;MAEpB;MACArJ,WAAW,CAACwN,OAAO,CAACC,GAAG,CAAC;MAExB;MACA,IACE,CAAC4H,MAAM,CAACL,GAAG,IACXK,MAAM,CAACK,GAAG,KAAK,OAAO,IACtB,EAAE,QAAQ,IAAIvS,UAAU,IAAI,QAAQ,IAAIA,UAAU,CAACwS,MAAM,CAAC,EAC1D;QACA,MAAM;UAAE7Q;QAAK,CAAE,GAAG,MAAM,IAAI,CAAC+H,OAAO,CAACxD,KAAK,CAAC;QAC3C,IAAIvE,KAAK,EAAE;UACT,MAAMA,KAAK;;QAEb;QACA,OAAO;UACLM,IAAI,EAAE;YACJwQ,MAAM,EAAEpI,OAAO;YACf6H,MAAM;YACNC;WACD;UACDxQ,KAAK,EAAE;SACR;;MAGH,MAAM+Q,SAAS,GAAG9V,YAAY,CAACsV,MAAM,CAACK,GAAG,CAAC;MAC1C,MAAMI,UAAU,GAAG,MAAM,IAAI,CAACf,QAAQ,CAACM,MAAM,CAACL,GAAG,EAAE1R,IAAI,CAAC;MAExD;MACA,MAAMyS,SAAS,GAAG,MAAMJ,MAAM,CAACK,MAAM,CAACC,SAAS,CAAC,KAAK,EAAEH,UAAU,EAAED,SAAS,EAAE,IAAI,EAAE,CAClF,QAAQ,CACT,CAAC;MAEF;MACA,MAAMK,OAAO,GAAG,MAAMP,MAAM,CAACK,MAAM,CAACpS,MAAM,CACxCiS,SAAS,EACTE,SAAS,EACTT,SAAS,EACT9U,kBAAkB,CAAC,GAAGgV,SAAS,IAAIC,UAAU,EAAE,CAAC,CACjD;MAED,IAAI,CAACS,OAAO,EAAE;QACZ,MAAM,IAAIrX,mBAAmB,CAAC,uBAAuB,CAAC;;MAGxD;MACA,OAAO;QACLuG,IAAI,EAAE;UACJwQ,MAAM,EAAEpI,OAAO;UACf6H,MAAM;UACNC;SACD;QACDxQ,KAAK,EAAE;OACR;KACF,CAAC,OAAOA,KAAK,EAAE;MACd,IAAIrG,WAAW,CAACqG,KAAK,CAAC,EAAE;QACtB,OAAO;UAAEM,IAAI,EAAE,IAAI;UAAEN;QAAK,CAAE;;MAE9B,MAAMA,KAAK;;EAEf;;AAtiFevD,YAAA,CAAAkB,cAAc,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}