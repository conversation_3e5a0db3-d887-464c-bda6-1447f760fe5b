{"ast": null, "code": "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef Options\n *   Configuration.\n * @property {Array<Node> | null | undefined} [ancestors]\n *   Stack of (inclusive) ancestor nodes surrounding the message (optional).\n * @property {Error | null | undefined} [cause]\n *   Original error cause of the message (optional).\n * @property {Point | Position | null | undefined} [place]\n *   Place of message (optional).\n * @property {string | null | undefined} [ruleId]\n *   Category of message (optional, example: `'my-rule'`).\n * @property {string | null | undefined} [source]\n *   Namespace of who sent the message (optional, example: `'my-package'`).\n */\n\nimport { stringifyPosition } from 'unist-util-stringify-position';\n\n/**\n * Message.\n */\nexport class VFileMessage extends Error {\n  /**\n   * Create a message for `reason`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Options | null | undefined} [options]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | Options | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // eslint-disable-next-line complexity\n  constructor(causeOrReason, optionsOrParentOrPlace, origin) {\n    super();\n    if (typeof optionsOrParentOrPlace === 'string') {\n      origin = optionsOrParentOrPlace;\n      optionsOrParentOrPlace = undefined;\n    }\n\n    /** @type {string} */\n    let reason = '';\n    /** @type {Options} */\n    let options = {};\n    let legacyCause = false;\n    if (optionsOrParentOrPlace) {\n      // Point.\n      if ('line' in optionsOrParentOrPlace && 'column' in optionsOrParentOrPlace) {\n        options = {\n          place: optionsOrParentOrPlace\n        };\n      }\n      // Position.\n      else if ('start' in optionsOrParentOrPlace && 'end' in optionsOrParentOrPlace) {\n        options = {\n          place: optionsOrParentOrPlace\n        };\n      }\n      // Node.\n      else if ('type' in optionsOrParentOrPlace) {\n        options = {\n          ancestors: [optionsOrParentOrPlace],\n          place: optionsOrParentOrPlace.position\n        };\n      }\n      // Options.\n      else {\n        options = {\n          ...optionsOrParentOrPlace\n        };\n      }\n    }\n    if (typeof causeOrReason === 'string') {\n      reason = causeOrReason;\n    }\n    // Error.\n    else if (!options.cause && causeOrReason) {\n      legacyCause = true;\n      reason = causeOrReason.message;\n      options.cause = causeOrReason;\n    }\n    if (!options.ruleId && !options.source && typeof origin === 'string') {\n      const index = origin.indexOf(':');\n      if (index === -1) {\n        options.ruleId = origin;\n      } else {\n        options.source = origin.slice(0, index);\n        options.ruleId = origin.slice(index + 1);\n      }\n    }\n    if (!options.place && options.ancestors && options.ancestors) {\n      const parent = options.ancestors[options.ancestors.length - 1];\n      if (parent) {\n        options.place = parent.position;\n      }\n    }\n    const start = options.place && 'start' in options.place ? options.place.start : options.place;\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Stack of ancestor nodes surrounding the message.\n     *\n     * @type {Array<Node> | undefined}\n     */\n    this.ancestors = options.ancestors || undefined;\n\n    /**\n     * Original error cause of the message.\n     *\n     * @type {Error | undefined}\n     */\n    this.cause = options.cause || undefined;\n\n    /**\n     * Starting column of message.\n     *\n     * @type {number | undefined}\n     */\n    this.column = start ? start.column : undefined;\n\n    /**\n     * State of problem.\n     *\n     * * `true` — error, file not usable\n     * * `false` — warning, change may be needed\n     * * `undefined` — change likely not needed\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal = undefined;\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | undefined}\n     */\n    this.file;\n\n    // Field from `Error`.\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = reason;\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | undefined}\n     */\n    this.line = start ? start.line : undefined;\n\n    // Field from `Error`.\n    /**\n     * Serialized positional info of message.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = stringifyPosition(options.place) || '1:1';\n\n    /**\n     * Place of message.\n     *\n     * @type {Point | Position | undefined}\n     */\n    this.place = options.place || undefined;\n\n    /**\n     * Reason for message, should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message;\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | undefined}\n     */\n    this.ruleId = options.ruleId || undefined;\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | undefined}\n     */\n    this.source = options.source || undefined;\n\n    // Field from `Error`.\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack = legacyCause && options.cause && typeof options.cause.stack === 'string' ? options.cause.stack : '';\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | undefined}\n     */\n    this.actual;\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | undefined}\n     */\n    this.expected;\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | undefined}\n     */\n    this.note;\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | undefined}\n     */\n    this.url;\n    /* eslint-enable no-unused-expressions */\n  }\n}\nVFileMessage.prototype.file = '';\nVFileMessage.prototype.name = '';\nVFileMessage.prototype.reason = '';\nVFileMessage.prototype.message = '';\nVFileMessage.prototype.stack = '';\nVFileMessage.prototype.column = undefined;\nVFileMessage.prototype.line = undefined;\nVFileMessage.prototype.ancestors = undefined;\nVFileMessage.prototype.cause = undefined;\nVFileMessage.prototype.fatal = undefined;\nVFileMessage.prototype.place = undefined;\nVFileMessage.prototype.ruleId = undefined;\nVFileMessage.prototype.source = undefined;", "map": {"version": 3, "names": ["stringifyPosition", "VFileMessage", "Error", "constructor", "causeOrReason", "optionsOrParentOrPlace", "origin", "undefined", "reason", "options", "legacyCause", "place", "ancestors", "position", "cause", "message", "ruleId", "source", "index", "indexOf", "slice", "parent", "length", "start", "column", "fatal", "file", "line", "name", "stack", "actual", "expected", "note", "url", "prototype"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/vfile-message/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef Options\n *   Configuration.\n * @property {Array<Node> | null | undefined} [ancestors]\n *   Stack of (inclusive) ancestor nodes surrounding the message (optional).\n * @property {Error | null | undefined} [cause]\n *   Original error cause of the message (optional).\n * @property {Point | Position | null | undefined} [place]\n *   Place of message (optional).\n * @property {string | null | undefined} [ruleId]\n *   Category of message (optional, example: `'my-rule'`).\n * @property {string | null | undefined} [source]\n *   Namespace of who sent the message (optional, example: `'my-package'`).\n */\n\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\n/**\n * Message.\n */\nexport class VFileMessage extends Error {\n  /**\n   * Create a message for `reason`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Options | null | undefined} [options]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | Options | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // eslint-disable-next-line complexity\n  constructor(causeOrReason, optionsOrParentOrPlace, origin) {\n    super()\n\n    if (typeof optionsOrParentOrPlace === 'string') {\n      origin = optionsOrParentOrPlace\n      optionsOrParentOrPlace = undefined\n    }\n\n    /** @type {string} */\n    let reason = ''\n    /** @type {Options} */\n    let options = {}\n    let legacyCause = false\n\n    if (optionsOrParentOrPlace) {\n      // Point.\n      if (\n        'line' in optionsOrParentOrPlace &&\n        'column' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Position.\n      else if (\n        'start' in optionsOrParentOrPlace &&\n        'end' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Node.\n      else if ('type' in optionsOrParentOrPlace) {\n        options = {\n          ancestors: [optionsOrParentOrPlace],\n          place: optionsOrParentOrPlace.position\n        }\n      }\n      // Options.\n      else {\n        options = {...optionsOrParentOrPlace}\n      }\n    }\n\n    if (typeof causeOrReason === 'string') {\n      reason = causeOrReason\n    }\n    // Error.\n    else if (!options.cause && causeOrReason) {\n      legacyCause = true\n      reason = causeOrReason.message\n      options.cause = causeOrReason\n    }\n\n    if (!options.ruleId && !options.source && typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        options.ruleId = origin\n      } else {\n        options.source = origin.slice(0, index)\n        options.ruleId = origin.slice(index + 1)\n      }\n    }\n\n    if (!options.place && options.ancestors && options.ancestors) {\n      const parent = options.ancestors[options.ancestors.length - 1]\n\n      if (parent) {\n        options.place = parent.position\n      }\n    }\n\n    const start =\n      options.place && 'start' in options.place\n        ? options.place.start\n        : options.place\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Stack of ancestor nodes surrounding the message.\n     *\n     * @type {Array<Node> | undefined}\n     */\n    this.ancestors = options.ancestors || undefined\n\n    /**\n     * Original error cause of the message.\n     *\n     * @type {Error | undefined}\n     */\n    this.cause = options.cause || undefined\n\n    /**\n     * Starting column of message.\n     *\n     * @type {number | undefined}\n     */\n    this.column = start ? start.column : undefined\n\n    /**\n     * State of problem.\n     *\n     * * `true` — error, file not usable\n     * * `false` — warning, change may be needed\n     * * `undefined` — change likely not needed\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal = undefined\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | undefined}\n     */\n    this.file\n\n    // Field from `Error`.\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = reason\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | undefined}\n     */\n    this.line = start ? start.line : undefined\n\n    // Field from `Error`.\n    /**\n     * Serialized positional info of message.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = stringifyPosition(options.place) || '1:1'\n\n    /**\n     * Place of message.\n     *\n     * @type {Point | Position | undefined}\n     */\n    this.place = options.place || undefined\n\n    /**\n     * Reason for message, should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | undefined}\n     */\n    this.ruleId = options.ruleId || undefined\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | undefined}\n     */\n    this.source = options.source || undefined\n\n    // Field from `Error`.\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack =\n      legacyCause && options.cause && typeof options.cause.stack === 'string'\n        ? options.cause.stack\n        : ''\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | undefined}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | undefined}\n     */\n    this.expected\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | undefined}\n     */\n    this.note\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | undefined}\n     */\n    this.url\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.column = undefined\nVFileMessage.prototype.line = undefined\nVFileMessage.prototype.ancestors = undefined\nVFileMessage.prototype.cause = undefined\nVFileMessage.prototype.fatal = undefined\nVFileMessage.prototype.place = undefined\nVFileMessage.prototype.ruleId = undefined\nVFileMessage.prototype.source = undefined\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,iBAAiB,QAAO,+BAA+B;;AAE/D;AACA;AACA;AACA,OAAO,MAAMC,YAAY,SAASC,KAAK,CAAC;EACtC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE;EACAC,WAAWA,CAACC,aAAa,EAAEC,sBAAsB,EAAEC,MAAM,EAAE;IACzD,KAAK,CAAC,CAAC;IAEP,IAAI,OAAOD,sBAAsB,KAAK,QAAQ,EAAE;MAC9CC,MAAM,GAAGD,sBAAsB;MAC/BA,sBAAsB,GAAGE,SAAS;IACpC;;IAEA;IACA,IAAIC,MAAM,GAAG,EAAE;IACf;IACA,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,WAAW,GAAG,KAAK;IAEvB,IAAIL,sBAAsB,EAAE;MAC1B;MACA,IACE,MAAM,IAAIA,sBAAsB,IAChC,QAAQ,IAAIA,sBAAsB,EAClC;QACAI,OAAO,GAAG;UAACE,KAAK,EAAEN;QAAsB,CAAC;MAC3C;MACA;MAAA,KACK,IACH,OAAO,IAAIA,sBAAsB,IACjC,KAAK,IAAIA,sBAAsB,EAC/B;QACAI,OAAO,GAAG;UAACE,KAAK,EAAEN;QAAsB,CAAC;MAC3C;MACA;MAAA,KACK,IAAI,MAAM,IAAIA,sBAAsB,EAAE;QACzCI,OAAO,GAAG;UACRG,SAAS,EAAE,CAACP,sBAAsB,CAAC;UACnCM,KAAK,EAAEN,sBAAsB,CAACQ;QAChC,CAAC;MACH;MACA;MAAA,KACK;QACHJ,OAAO,GAAG;UAAC,GAAGJ;QAAsB,CAAC;MACvC;IACF;IAEA,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;MACrCI,MAAM,GAAGJ,aAAa;IACxB;IACA;IAAA,KACK,IAAI,CAACK,OAAO,CAACK,KAAK,IAAIV,aAAa,EAAE;MACxCM,WAAW,GAAG,IAAI;MAClBF,MAAM,GAAGJ,aAAa,CAACW,OAAO;MAC9BN,OAAO,CAACK,KAAK,GAAGV,aAAa;IAC/B;IAEA,IAAI,CAACK,OAAO,CAACO,MAAM,IAAI,CAACP,OAAO,CAACQ,MAAM,IAAI,OAAOX,MAAM,KAAK,QAAQ,EAAE;MACpE,MAAMY,KAAK,GAAGZ,MAAM,CAACa,OAAO,CAAC,GAAG,CAAC;MAEjC,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBT,OAAO,CAACO,MAAM,GAAGV,MAAM;MACzB,CAAC,MAAM;QACLG,OAAO,CAACQ,MAAM,GAAGX,MAAM,CAACc,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC;QACvCT,OAAO,CAACO,MAAM,GAAGV,MAAM,CAACc,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC;MAC1C;IACF;IAEA,IAAI,CAACT,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACG,SAAS,EAAE;MAC5D,MAAMS,MAAM,GAAGZ,OAAO,CAACG,SAAS,CAACH,OAAO,CAACG,SAAS,CAACU,MAAM,GAAG,CAAC,CAAC;MAE9D,IAAID,MAAM,EAAE;QACVZ,OAAO,CAACE,KAAK,GAAGU,MAAM,CAACR,QAAQ;MACjC;IACF;IAEA,MAAMU,KAAK,GACTd,OAAO,CAACE,KAAK,IAAI,OAAO,IAAIF,OAAO,CAACE,KAAK,GACrCF,OAAO,CAACE,KAAK,CAACY,KAAK,GACnBd,OAAO,CAACE,KAAK;;IAEnB;IACA;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,SAAS,GAAGH,OAAO,CAACG,SAAS,IAAIL,SAAS;;IAE/C;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACO,KAAK,GAAGL,OAAO,CAACK,KAAK,IAAIP,SAAS;;IAEvC;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACiB,MAAM,GAAGD,KAAK,GAAGA,KAAK,CAACC,MAAM,GAAGjB,SAAS;;IAE9C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACkB,KAAK,GAAGlB,SAAS;;IAEtB;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACmB,IAAI;;IAET;IACA;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACX,OAAO,GAAGP,MAAM;;IAErB;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACmB,IAAI,GAAGJ,KAAK,GAAGA,KAAK,CAACI,IAAI,GAAGpB,SAAS;;IAE1C;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI,CAACqB,IAAI,GAAG5B,iBAAiB,CAACS,OAAO,CAACE,KAAK,CAAC,IAAI,KAAK;;IAErD;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACA,KAAK,GAAGF,OAAO,CAACE,KAAK,IAAIJ,SAAS;;IAEvC;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM,GAAG,IAAI,CAACO,OAAO;;IAE1B;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM,GAAGP,OAAO,CAACO,MAAM,IAAIT,SAAS;;IAEzC;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACU,MAAM,GAAGR,OAAO,CAACQ,MAAM,IAAIV,SAAS;;IAEzC;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACsB,KAAK,GACRnB,WAAW,IAAID,OAAO,CAACK,KAAK,IAAI,OAAOL,OAAO,CAACK,KAAK,CAACe,KAAK,KAAK,QAAQ,GACnEpB,OAAO,CAACK,KAAK,CAACe,KAAK,GACnB,EAAE;;IAER;IACA;IACA;;IAEA;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM;;IAEX;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,QAAQ;;IAEb;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,IAAI;;IAET;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,GAAG;IACR;EACF;AACF;AAEAhC,YAAY,CAACiC,SAAS,CAACR,IAAI,GAAG,EAAE;AAChCzB,YAAY,CAACiC,SAAS,CAACN,IAAI,GAAG,EAAE;AAChC3B,YAAY,CAACiC,SAAS,CAAC1B,MAAM,GAAG,EAAE;AAClCP,YAAY,CAACiC,SAAS,CAACnB,OAAO,GAAG,EAAE;AACnCd,YAAY,CAACiC,SAAS,CAACL,KAAK,GAAG,EAAE;AACjC5B,YAAY,CAACiC,SAAS,CAACV,MAAM,GAAGjB,SAAS;AACzCN,YAAY,CAACiC,SAAS,CAACP,IAAI,GAAGpB,SAAS;AACvCN,YAAY,CAACiC,SAAS,CAACtB,SAAS,GAAGL,SAAS;AAC5CN,YAAY,CAACiC,SAAS,CAACpB,KAAK,GAAGP,SAAS;AACxCN,YAAY,CAACiC,SAAS,CAACT,KAAK,GAAGlB,SAAS;AACxCN,YAAY,CAACiC,SAAS,CAACvB,KAAK,GAAGJ,SAAS;AACxCN,YAAY,CAACiC,SAAS,CAAClB,MAAM,GAAGT,SAAS;AACzCN,YAAY,CAACiC,SAAS,CAACjB,MAAM,GAAGV,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}