{"ast": null, "code": "/**\n * @import {<PERSON><PERSON>, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentType, JSX, ReactElement, ReactNode} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in keyof JSX.IntrinsicElements]?: ComponentType<JSX.IntrinsicElements[Key] & ExtraProps> | keyof JSX.IntrinsicElements\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @typedef HooksOptionsOnly\n *   Configuration specifically for {@linkcode MarkdownHooks}.\n * @property {ReactNode | null | undefined} [fallback]\n *   Content to render while the processor processing the markdown (optional).\n */\n\n/**\n * @typedef {Options & HooksOptionsOnly} HooksOptions\n *   Configuration for {@linkcode MarkdownHooks};\n *   extends the regular {@linkcode Options} with a `fallback` prop.\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\nimport { unreachable } from 'devlop';\nimport { toJsxRuntime } from 'hast-util-to-jsx-runtime';\nimport { urlAttributes } from 'html-url-attributes';\nimport { Fragment, jsx, jsxs } from 'react/jsx-runtime';\nimport { useEffect, useState } from 'react';\nimport remarkParse from 'remark-parse';\nimport remarkRehype from 'remark-rehype';\nimport { unified } from 'unified';\nimport { visit } from 'unist-util-visit';\nimport { VFile } from 'vfile';\nconst changelog = 'https://github.com/remarkjs/react-markdown/blob/main/changelog.md';\n\n/** @type {PluggableList} */\nconst emptyPlugins = [];\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {\n  allowDangerousHtml: true\n};\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i;\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [{\n  from: 'astPlugins',\n  id: 'remove-buggy-html-in-markdown-parser'\n}, {\n  from: 'allowDangerousHtml',\n  id: 'remove-buggy-html-in-markdown-parser'\n}, {\n  from: 'allowNode',\n  id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n  to: 'allowElement'\n}, {\n  from: 'allowedTypes',\n  id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n  to: 'allowedElements'\n}, {\n  from: 'className',\n  id: 'remove-classname'\n}, {\n  from: 'disallowedTypes',\n  id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n  to: 'disallowedElements'\n}, {\n  from: 'escapeHtml',\n  id: 'remove-buggy-html-in-markdown-parser'\n}, {\n  from: 'includeElementIndex',\n  id: '#remove-includeelementindex'\n}, {\n  from: 'includeNodeIndex',\n  id: 'change-includenodeindex-to-includeelementindex'\n}, {\n  from: 'linkTarget',\n  id: 'remove-linktarget'\n}, {\n  from: 'plugins',\n  id: 'change-plugins-to-remarkplugins',\n  to: 'remarkPlugins'\n}, {\n  from: 'rawSourcePos',\n  id: '#remove-rawsourcepos'\n}, {\n  from: 'renderers',\n  id: 'change-renderers-to-components',\n  to: 'components'\n}, {\n  from: 'source',\n  id: 'change-source-to-children',\n  to: 'children'\n}, {\n  from: 'sourcePos',\n  id: '#remove-sourcepos'\n}, {\n  from: 'transformImageUri',\n  id: '#add-urltransform',\n  to: 'urlTransform'\n}, {\n  from: 'transformLinkUri',\n  id: '#add-urltransform',\n  to: 'urlTransform'\n}];\n\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nexport function Markdown(options) {\n  const processor = createProcessor(options);\n  const file = createFile(options);\n  return post(processor.runSync(processor.parse(file), file), options);\n}\n\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */\nexport async function MarkdownAsync(options) {\n  const processor = createProcessor(options);\n  const file = createFile(options);\n  const tree = await processor.run(processor.parse(file), file);\n  return post(tree, options);\n}\n\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<HooksOptions>} options\n *   Props.\n * @returns {ReactNode}\n *   React node.\n */\nexport function MarkdownHooks(options) {\n  const processor = createProcessor(options);\n  const [error, setError] = useState(/** @type {Error | undefined} */undefined);\n  const [tree, setTree] = useState(/** @type {Root | undefined} */undefined);\n  useEffect(function () {\n    let cancelled = false;\n    const file = createFile(options);\n    processor.run(processor.parse(file), file, function (error, tree) {\n      if (!cancelled) {\n        setError(error);\n        setTree(tree);\n      }\n    });\n\n    /**\n     * @returns {undefined}\n     *   Nothing.\n     */\n    return function () {\n      cancelled = true;\n    };\n  }, [options.children, options.rehypePlugins, options.remarkPlugins, options.remarkRehypeOptions]);\n  if (error) throw error;\n  return tree ? post(tree, options) : options.fallback;\n}\n\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */\nfunction createProcessor(options) {\n  const rehypePlugins = options.rehypePlugins || emptyPlugins;\n  const remarkPlugins = options.remarkPlugins || emptyPlugins;\n  const remarkRehypeOptions = options.remarkRehypeOptions ? {\n    ...options.remarkRehypeOptions,\n    ...emptyRemarkRehypeOptions\n  } : emptyRemarkRehypeOptions;\n  const processor = unified().use(remarkParse).use(remarkPlugins).use(remarkRehype, remarkRehypeOptions).use(rehypePlugins);\n  return processor;\n}\n\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */\nfunction createFile(options) {\n  const children = options.children || '';\n  const file = new VFile();\n  if (typeof children === 'string') {\n    file.value = children;\n  } else {\n    unreachable('Unexpected value `' + children + '` for `children` prop, expected `string`');\n  }\n  return file;\n}\n\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction post(tree, options) {\n  const allowedElements = options.allowedElements;\n  const allowElement = options.allowElement;\n  const components = options.components;\n  const disallowedElements = options.disallowedElements;\n  const skipHtml = options.skipHtml;\n  const unwrapDisallowed = options.unwrapDisallowed;\n  const urlTransform = options.urlTransform || defaultUrlTransform;\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      unreachable('Unexpected `' + deprecation.from + '` prop, ' + (deprecation.to ? 'use `' + deprecation.to + '` instead' : 'remove it') + ' (see <' + changelog + '#' + deprecation.id + '> for more info)');\n    }\n  }\n  if (allowedElements && disallowedElements) {\n    unreachable('Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other');\n  }\n  visit(tree, transform);\n  return toJsxRuntime(tree, {\n    Fragment,\n    components,\n    ignoreInvalidStyle: true,\n    jsx,\n    jsxs,\n    passKeys: true,\n    passNode: true\n  });\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1);\n      } else {\n        parent.children[index] = {\n          type: 'text',\n          value: node.value\n        };\n      }\n      return index;\n    }\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key;\n      for (key in urlAttributes) {\n        if (Object.hasOwn(urlAttributes, key) && Object.hasOwn(node.properties, key)) {\n          const value = node.properties[key];\n          const test = urlAttributes[key];\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node);\n          }\n        }\n      }\n    }\n    if (node.type === 'element') {\n      let remove = allowedElements ? !allowedElements.includes(node.tagName) : disallowedElements ? disallowedElements.includes(node.tagName) : false;\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent);\n      }\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children);\n        } else {\n          parent.children.splice(index, 1);\n        }\n        return index;\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nexport function defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':');\n  const questionMark = value.indexOf('?');\n  const numberSign = value.indexOf('#');\n  const slash = value.indexOf('/');\n  if (\n  // If there is no protocol, it’s relative.\n  colon === -1 ||\n  // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n  slash !== -1 && colon > slash || questionMark !== -1 && colon > questionMark || numberSign !== -1 && colon > numberSign ||\n  // It is a protocol, it should be allowed.\n  safeProtocol.test(value.slice(0, colon))) {\n    return value;\n  }\n  return '';\n}", "map": {"version": 3, "names": ["unreachable", "toJsxRuntime", "urlAttributes", "Fragment", "jsx", "jsxs", "useEffect", "useState", "remark<PERSON><PERSON><PERSON>", "remarkRehype", "unified", "visit", "VFile", "changelog", "emptyPlugins", "emptyRemarkRehypeOptions", "allowDangerousHtml", "safeProtocol", "deprecations", "from", "id", "to", "<PERSON><PERSON>", "options", "processor", "createProcessor", "file", "createFile", "post", "runSync", "parse", "<PERSON>downAsync", "tree", "run", "MarkdownHook<PERSON>", "error", "setError", "undefined", "setTree", "cancelled", "children", "rehypePlugins", "remarkPlugins", "remarkRehypeOptions", "fallback", "use", "value", "allowedElements", "allowElement", "components", "disallowedElements", "skipHtml", "unwrapDisallowed", "urlTransform", "defaultUrlTransform", "deprecation", "Object", "hasOwn", "transform", "ignoreInvalidStyle", "pass<PERSON><PERSON><PERSON>", "passNode", "node", "index", "parent", "type", "splice", "key", "properties", "test", "includes", "tagName", "String", "remove", "colon", "indexOf", "questionMark", "numberSign", "slash", "slice"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/react-markdown/lib/index.js"], "sourcesContent": ["/**\n * @import {<PERSON><PERSON>, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentType, JSX, ReactElement, ReactNode} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in keyof JSX.IntrinsicElements]?: ComponentType<JSX.IntrinsicElements[Key] & ExtraProps> | keyof JSX.IntrinsicElements\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @typedef HooksOptionsOnly\n *   Configuration specifically for {@linkcode MarkdownHooks}.\n * @property {ReactNode | null | undefined} [fallback]\n *   Content to render while the processor processing the markdown (optional).\n */\n\n/**\n * @typedef {Options & HooksOptionsOnly} HooksOptions\n *   Configuration for {@linkcode MarkdownHooks};\n *   extends the regular {@linkcode Options} with a `fallback` prop.\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\nimport {unreachable} from 'devlop'\nimport {toJsxRuntime} from 'hast-util-to-jsx-runtime'\nimport {urlAttributes} from 'html-url-attributes'\nimport {Fragment, jsx, jsxs} from 'react/jsx-runtime'\nimport {useEffect, useState} from 'react'\nimport remarkParse from 'remark-parse'\nimport remarkRehype from 'remark-rehype'\nimport {unified} from 'unified'\nimport {visit} from 'unist-util-visit'\nimport {VFile} from 'vfile'\n\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {PluggableList} */\nconst emptyPlugins = []\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {allowDangerousHtml: true}\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [\n  {from: 'astPlugins', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'allowDangerousHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {\n    from: 'allowNode',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowElement'\n  },\n  {\n    from: 'allowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowedElements'\n  },\n  {from: 'className', id: 'remove-classname'},\n  {\n    from: 'disallowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'disallowedElements'\n  },\n  {from: 'escapeHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'includeElementIndex', id: '#remove-includeelementindex'},\n  {\n    from: 'includeNodeIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  },\n  {from: 'linkTarget', id: 'remove-linktarget'},\n  {from: 'plugins', id: 'change-plugins-to-remarkplugins', to: 'remarkPlugins'},\n  {from: 'rawSourcePos', id: '#remove-rawsourcepos'},\n  {from: 'renderers', id: 'change-renderers-to-components', to: 'components'},\n  {from: 'source', id: 'change-source-to-children', to: 'children'},\n  {from: 'sourcePos', id: '#remove-sourcepos'},\n  {from: 'transformImageUri', id: '#add-urltransform', to: 'urlTransform'},\n  {from: 'transformLinkUri', id: '#add-urltransform', to: 'urlTransform'}\n]\n\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nexport function Markdown(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  return post(processor.runSync(processor.parse(file), file), options)\n}\n\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */\nexport async function MarkdownAsync(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  const tree = await processor.run(processor.parse(file), file)\n  return post(tree, options)\n}\n\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<HooksOptions>} options\n *   Props.\n * @returns {ReactNode}\n *   React node.\n */\nexport function MarkdownHooks(options) {\n  const processor = createProcessor(options)\n  const [error, setError] = useState(\n    /** @type {Error | undefined} */ (undefined)\n  )\n  const [tree, setTree] = useState(/** @type {Root | undefined} */ (undefined))\n\n  useEffect(\n    function () {\n      let cancelled = false\n      const file = createFile(options)\n\n      processor.run(processor.parse(file), file, function (error, tree) {\n        if (!cancelled) {\n          setError(error)\n          setTree(tree)\n        }\n      })\n\n      /**\n       * @returns {undefined}\n       *   Nothing.\n       */\n      return function () {\n        cancelled = true\n      }\n    },\n    [\n      options.children,\n      options.rehypePlugins,\n      options.remarkPlugins,\n      options.remarkRehypeOptions\n    ]\n  )\n\n  if (error) throw error\n\n  return tree ? post(tree, options) : options.fallback\n}\n\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */\nfunction createProcessor(options) {\n  const rehypePlugins = options.rehypePlugins || emptyPlugins\n  const remarkPlugins = options.remarkPlugins || emptyPlugins\n  const remarkRehypeOptions = options.remarkRehypeOptions\n    ? {...options.remarkRehypeOptions, ...emptyRemarkRehypeOptions}\n    : emptyRemarkRehypeOptions\n\n  const processor = unified()\n    .use(remarkParse)\n    .use(remarkPlugins)\n    .use(remarkRehype, remarkRehypeOptions)\n    .use(rehypePlugins)\n\n  return processor\n}\n\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */\nfunction createFile(options) {\n  const children = options.children || ''\n  const file = new VFile()\n\n  if (typeof children === 'string') {\n    file.value = children\n  } else {\n    unreachable(\n      'Unexpected value `' +\n        children +\n        '` for `children` prop, expected `string`'\n    )\n  }\n\n  return file\n}\n\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction post(tree, options) {\n  const allowedElements = options.allowedElements\n  const allowElement = options.allowElement\n  const components = options.components\n  const disallowedElements = options.disallowedElements\n  const skipHtml = options.skipHtml\n  const unwrapDisallowed = options.unwrapDisallowed\n  const urlTransform = options.urlTransform || defaultUrlTransform\n\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      unreachable(\n        'Unexpected `' +\n          deprecation.from +\n          '` prop, ' +\n          (deprecation.to\n            ? 'use `' + deprecation.to + '` instead'\n            : 'remove it') +\n          ' (see <' +\n          changelog +\n          '#' +\n          deprecation.id +\n          '> for more info)'\n      )\n    }\n  }\n\n  if (allowedElements && disallowedElements) {\n    unreachable(\n      'Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other'\n    )\n  }\n\n  visit(tree, transform)\n\n  return toJsxRuntime(tree, {\n    Fragment,\n    components,\n    ignoreInvalidStyle: true,\n    jsx,\n    jsxs,\n    passKeys: true,\n    passNode: true\n  })\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1)\n      } else {\n        parent.children[index] = {type: 'text', value: node.value}\n      }\n\n      return index\n    }\n\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key\n\n      for (key in urlAttributes) {\n        if (\n          Object.hasOwn(urlAttributes, key) &&\n          Object.hasOwn(node.properties, key)\n        ) {\n          const value = node.properties[key]\n          const test = urlAttributes[key]\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node)\n          }\n        }\n      }\n    }\n\n    if (node.type === 'element') {\n      let remove = allowedElements\n        ? !allowedElements.includes(node.tagName)\n        : disallowedElements\n          ? disallowedElements.includes(node.tagName)\n          : false\n\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent)\n      }\n\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children)\n        } else {\n          parent.children.splice(index, 1)\n        }\n\n        return index\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nexport function defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon === -1 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash !== -1 && colon > slash) ||\n    (questionMark !== -1 && colon > questionMark) ||\n    (numberSign !== -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,WAAW,QAAO,QAAQ;AAClC,SAAQC,YAAY,QAAO,0BAA0B;AACrD,SAAQC,aAAa,QAAO,qBAAqB;AACjD,SAAQC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAO,mBAAmB;AACrD,SAAQC,SAAS,EAAEC,QAAQ,QAAO,OAAO;AACzC,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,YAAY,MAAM,eAAe;AACxC,SAAQC,OAAO,QAAO,SAAS;AAC/B,SAAQC,KAAK,QAAO,kBAAkB;AACtC,SAAQC,KAAK,QAAO,OAAO;AAE3B,MAAMC,SAAS,GACb,mEAAmE;;AAErE;AACA,MAAMC,YAAY,GAAG,EAAE;AACvB;AACA,MAAMC,wBAAwB,GAAG;EAACC,kBAAkB,EAAE;AAAI,CAAC;AAC3D,MAAMC,YAAY,GAAG,+BAA+B;;AAEpD;AACA;AACA,MAAMC,YAAY,GAAG,CACnB;EAACC,IAAI,EAAE,YAAY;EAAEC,EAAE,EAAE;AAAsC,CAAC,EAChE;EAACD,IAAI,EAAE,oBAAoB;EAAEC,EAAE,EAAE;AAAsC,CAAC,EACxE;EACED,IAAI,EAAE,WAAW;EACjBC,EAAE,EAAE,oDAAoD;EACxDC,EAAE,EAAE;AACN,CAAC,EACD;EACEF,IAAI,EAAE,cAAc;EACpBC,EAAE,EAAE,oDAAoD;EACxDC,EAAE,EAAE;AACN,CAAC,EACD;EAACF,IAAI,EAAE,WAAW;EAAEC,EAAE,EAAE;AAAkB,CAAC,EAC3C;EACED,IAAI,EAAE,iBAAiB;EACvBC,EAAE,EAAE,oDAAoD;EACxDC,EAAE,EAAE;AACN,CAAC,EACD;EAACF,IAAI,EAAE,YAAY;EAAEC,EAAE,EAAE;AAAsC,CAAC,EAChE;EAACD,IAAI,EAAE,qBAAqB;EAAEC,EAAE,EAAE;AAA6B,CAAC,EAChE;EACED,IAAI,EAAE,kBAAkB;EACxBC,EAAE,EAAE;AACN,CAAC,EACD;EAACD,IAAI,EAAE,YAAY;EAAEC,EAAE,EAAE;AAAmB,CAAC,EAC7C;EAACD,IAAI,EAAE,SAAS;EAAEC,EAAE,EAAE,iCAAiC;EAAEC,EAAE,EAAE;AAAe,CAAC,EAC7E;EAACF,IAAI,EAAE,cAAc;EAAEC,EAAE,EAAE;AAAsB,CAAC,EAClD;EAACD,IAAI,EAAE,WAAW;EAAEC,EAAE,EAAE,gCAAgC;EAAEC,EAAE,EAAE;AAAY,CAAC,EAC3E;EAACF,IAAI,EAAE,QAAQ;EAAEC,EAAE,EAAE,2BAA2B;EAAEC,EAAE,EAAE;AAAU,CAAC,EACjE;EAACF,IAAI,EAAE,WAAW;EAAEC,EAAE,EAAE;AAAmB,CAAC,EAC5C;EAACD,IAAI,EAAE,mBAAmB;EAAEC,EAAE,EAAE,mBAAmB;EAAEC,EAAE,EAAE;AAAc,CAAC,EACxE;EAACF,IAAI,EAAE,kBAAkB;EAAEC,EAAE,EAAE,mBAAmB;EAAEC,EAAE,EAAE;AAAc,CAAC,CACxE;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,OAAO,EAAE;EAChC,MAAMC,SAAS,GAAGC,eAAe,CAACF,OAAO,CAAC;EAC1C,MAAMG,IAAI,GAAGC,UAAU,CAACJ,OAAO,CAAC;EAChC,OAAOK,IAAI,CAACJ,SAAS,CAACK,OAAO,CAACL,SAAS,CAACM,KAAK,CAACJ,IAAI,CAAC,EAAEA,IAAI,CAAC,EAAEH,OAAO,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeQ,aAAaA,CAACR,OAAO,EAAE;EAC3C,MAAMC,SAAS,GAAGC,eAAe,CAACF,OAAO,CAAC;EAC1C,MAAMG,IAAI,GAAGC,UAAU,CAACJ,OAAO,CAAC;EAChC,MAAMS,IAAI,GAAG,MAAMR,SAAS,CAACS,GAAG,CAACT,SAAS,CAACM,KAAK,CAACJ,IAAI,CAAC,EAAEA,IAAI,CAAC;EAC7D,OAAOE,IAAI,CAACI,IAAI,EAAET,OAAO,CAAC;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,aAAaA,CAACX,OAAO,EAAE;EACrC,MAAMC,SAAS,GAAGC,eAAe,CAACF,OAAO,CAAC;EAC1C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAChC,gCAAkC8B,SACpC,CAAC;EACD,MAAM,CAACL,IAAI,EAAEM,OAAO,CAAC,GAAG/B,QAAQ,CAAC,+BAAiC8B,SAAU,CAAC;EAE7E/B,SAAS,CACP,YAAY;IACV,IAAIiC,SAAS,GAAG,KAAK;IACrB,MAAMb,IAAI,GAAGC,UAAU,CAACJ,OAAO,CAAC;IAEhCC,SAAS,CAACS,GAAG,CAACT,SAAS,CAACM,KAAK,CAACJ,IAAI,CAAC,EAAEA,IAAI,EAAE,UAAUS,KAAK,EAAEH,IAAI,EAAE;MAChE,IAAI,CAACO,SAAS,EAAE;QACdH,QAAQ,CAACD,KAAK,CAAC;QACfG,OAAO,CAACN,IAAI,CAAC;MACf;IACF,CAAC,CAAC;;IAEF;AACN;AACA;AACA;IACM,OAAO,YAAY;MACjBO,SAAS,GAAG,IAAI;IAClB,CAAC;EACH,CAAC,EACD,CACEhB,OAAO,CAACiB,QAAQ,EAChBjB,OAAO,CAACkB,aAAa,EACrBlB,OAAO,CAACmB,aAAa,EACrBnB,OAAO,CAACoB,mBAAmB,CAE/B,CAAC;EAED,IAAIR,KAAK,EAAE,MAAMA,KAAK;EAEtB,OAAOH,IAAI,GAAGJ,IAAI,CAACI,IAAI,EAAET,OAAO,CAAC,GAAGA,OAAO,CAACqB,QAAQ;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnB,eAAeA,CAACF,OAAO,EAAE;EAChC,MAAMkB,aAAa,GAAGlB,OAAO,CAACkB,aAAa,IAAI3B,YAAY;EAC3D,MAAM4B,aAAa,GAAGnB,OAAO,CAACmB,aAAa,IAAI5B,YAAY;EAC3D,MAAM6B,mBAAmB,GAAGpB,OAAO,CAACoB,mBAAmB,GACnD;IAAC,GAAGpB,OAAO,CAACoB,mBAAmB;IAAE,GAAG5B;EAAwB,CAAC,GAC7DA,wBAAwB;EAE5B,MAAMS,SAAS,GAAGd,OAAO,CAAC,CAAC,CACxBmC,GAAG,CAACrC,WAAW,CAAC,CAChBqC,GAAG,CAACH,aAAa,CAAC,CAClBG,GAAG,CAACpC,YAAY,EAAEkC,mBAAmB,CAAC,CACtCE,GAAG,CAACJ,aAAa,CAAC;EAErB,OAAOjB,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,UAAUA,CAACJ,OAAO,EAAE;EAC3B,MAAMiB,QAAQ,GAAGjB,OAAO,CAACiB,QAAQ,IAAI,EAAE;EACvC,MAAMd,IAAI,GAAG,IAAId,KAAK,CAAC,CAAC;EAExB,IAAI,OAAO4B,QAAQ,KAAK,QAAQ,EAAE;IAChCd,IAAI,CAACoB,KAAK,GAAGN,QAAQ;EACvB,CAAC,MAAM;IACLxC,WAAW,CACT,oBAAoB,GAClBwC,QAAQ,GACR,0CACJ,CAAC;EACH;EAEA,OAAOd,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,IAAIA,CAACI,IAAI,EAAET,OAAO,EAAE;EAC3B,MAAMwB,eAAe,GAAGxB,OAAO,CAACwB,eAAe;EAC/C,MAAMC,YAAY,GAAGzB,OAAO,CAACyB,YAAY;EACzC,MAAMC,UAAU,GAAG1B,OAAO,CAAC0B,UAAU;EACrC,MAAMC,kBAAkB,GAAG3B,OAAO,CAAC2B,kBAAkB;EACrD,MAAMC,QAAQ,GAAG5B,OAAO,CAAC4B,QAAQ;EACjC,MAAMC,gBAAgB,GAAG7B,OAAO,CAAC6B,gBAAgB;EACjD,MAAMC,YAAY,GAAG9B,OAAO,CAAC8B,YAAY,IAAIC,mBAAmB;EAEhE,KAAK,MAAMC,WAAW,IAAIrC,YAAY,EAAE;IACtC,IAAIsC,MAAM,CAACC,MAAM,CAAClC,OAAO,EAAEgC,WAAW,CAACpC,IAAI,CAAC,EAAE;MAC5CnB,WAAW,CACT,cAAc,GACZuD,WAAW,CAACpC,IAAI,GAChB,UAAU,IACToC,WAAW,CAAClC,EAAE,GACX,OAAO,GAAGkC,WAAW,CAAClC,EAAE,GAAG,WAAW,GACtC,WAAW,CAAC,GAChB,SAAS,GACTR,SAAS,GACT,GAAG,GACH0C,WAAW,CAACnC,EAAE,GACd,kBACJ,CAAC;IACH;EACF;EAEA,IAAI2B,eAAe,IAAIG,kBAAkB,EAAE;IACzClD,WAAW,CACT,2FACF,CAAC;EACH;EAEAW,KAAK,CAACqB,IAAI,EAAE0B,SAAS,CAAC;EAEtB,OAAOzD,YAAY,CAAC+B,IAAI,EAAE;IACxB7B,QAAQ;IACR8C,UAAU;IACVU,kBAAkB,EAAE,IAAI;IACxBvD,GAAG;IACHC,IAAI;IACJuD,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,SAASH,SAASA,CAACI,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACtC,IAAIF,IAAI,CAACG,IAAI,KAAK,KAAK,IAAID,MAAM,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;MAC9D,IAAIZ,QAAQ,EAAE;QACZa,MAAM,CAACxB,QAAQ,CAAC0B,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MAClC,CAAC,MAAM;QACLC,MAAM,CAACxB,QAAQ,CAACuB,KAAK,CAAC,GAAG;UAACE,IAAI,EAAE,MAAM;UAAEnB,KAAK,EAAEgB,IAAI,CAAChB;QAAK,CAAC;MAC5D;MAEA,OAAOiB,KAAK;IACd;IAEA,IAAID,IAAI,CAACG,IAAI,KAAK,SAAS,EAAE;MAC3B;MACA,IAAIE,GAAG;MAEP,KAAKA,GAAG,IAAIjE,aAAa,EAAE;QACzB,IACEsD,MAAM,CAACC,MAAM,CAACvD,aAAa,EAAEiE,GAAG,CAAC,IACjCX,MAAM,CAACC,MAAM,CAACK,IAAI,CAACM,UAAU,EAAED,GAAG,CAAC,EACnC;UACA,MAAMrB,KAAK,GAAGgB,IAAI,CAACM,UAAU,CAACD,GAAG,CAAC;UAClC,MAAME,IAAI,GAAGnE,aAAa,CAACiE,GAAG,CAAC;UAC/B,IAAIE,IAAI,KAAK,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAACR,IAAI,CAACS,OAAO,CAAC,EAAE;YAChDT,IAAI,CAACM,UAAU,CAACD,GAAG,CAAC,GAAGd,YAAY,CAACmB,MAAM,CAAC1B,KAAK,IAAI,EAAE,CAAC,EAAEqB,GAAG,EAAEL,IAAI,CAAC;UACrE;QACF;MACF;IACF;IAEA,IAAIA,IAAI,CAACG,IAAI,KAAK,SAAS,EAAE;MAC3B,IAAIQ,MAAM,GAAG1B,eAAe,GACxB,CAACA,eAAe,CAACuB,QAAQ,CAACR,IAAI,CAACS,OAAO,CAAC,GACvCrB,kBAAkB,GAChBA,kBAAkB,CAACoB,QAAQ,CAACR,IAAI,CAACS,OAAO,CAAC,GACzC,KAAK;MAEX,IAAI,CAACE,MAAM,IAAIzB,YAAY,IAAI,OAAOe,KAAK,KAAK,QAAQ,EAAE;QACxDU,MAAM,GAAG,CAACzB,YAAY,CAACc,IAAI,EAAEC,KAAK,EAAEC,MAAM,CAAC;MAC7C;MAEA,IAAIS,MAAM,IAAIT,MAAM,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;QACjD,IAAIX,gBAAgB,IAAIU,IAAI,CAACtB,QAAQ,EAAE;UACrCwB,MAAM,CAACxB,QAAQ,CAAC0B,MAAM,CAACH,KAAK,EAAE,CAAC,EAAE,GAAGD,IAAI,CAACtB,QAAQ,CAAC;QACpD,CAAC,MAAM;UACLwB,MAAM,CAACxB,QAAQ,CAAC0B,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;QAClC;QAEA,OAAOA,KAAK;MACd;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAST,mBAAmBA,CAACR,KAAK,EAAE;EACzC;EACA;EACA;EACA,MAAM4B,KAAK,GAAG5B,KAAK,CAAC6B,OAAO,CAAC,GAAG,CAAC;EAChC,MAAMC,YAAY,GAAG9B,KAAK,CAAC6B,OAAO,CAAC,GAAG,CAAC;EACvC,MAAME,UAAU,GAAG/B,KAAK,CAAC6B,OAAO,CAAC,GAAG,CAAC;EACrC,MAAMG,KAAK,GAAGhC,KAAK,CAAC6B,OAAO,CAAC,GAAG,CAAC;EAEhC;EACE;EACAD,KAAK,KAAK,CAAC,CAAC;EACZ;EACCI,KAAK,KAAK,CAAC,CAAC,IAAIJ,KAAK,GAAGI,KAAM,IAC9BF,YAAY,KAAK,CAAC,CAAC,IAAIF,KAAK,GAAGE,YAAa,IAC5CC,UAAU,KAAK,CAAC,CAAC,IAAIH,KAAK,GAAGG,UAAW;EACzC;EACA5D,YAAY,CAACoD,IAAI,CAACvB,KAAK,CAACiC,KAAK,CAAC,CAAC,EAAEL,KAAK,CAAC,CAAC,EACxC;IACA,OAAO5B,KAAK;EACd;EAEA,OAAO,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}