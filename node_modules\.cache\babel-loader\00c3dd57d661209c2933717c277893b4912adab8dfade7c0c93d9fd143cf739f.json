{"ast": null, "code": "import { caseInsensitiveTransform } from './util/case-insensitive-transform.js';\nimport { create } from './util/create.js';\nimport { booleanish, boolean, commaSeparated, number, overloadedBoolean, spaceSeparated } from './util/types.js';\nexport const html = create({\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    blocking: spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: overloadedBoolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shadowRootClonable: boolean,\n    shadowRootDelegatesFocus: boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n    writingSuggestions: null,\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null,\n    // Several. Use CSS `text-align` instead,\n    aLink: null,\n    // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated,\n    // `<object>`. List of URIs to archives\n    axis: null,\n    // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null,\n    // `<body>`. Use CSS `background-image` instead\n    bgColor: null,\n    // `<body>` and table elements. Use CSS `background-color` instead\n    border: number,\n    // `<table>`. Use CSS `border-width` instead,\n    borderColor: null,\n    // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number,\n    // `<body>`\n    cellPadding: null,\n    // `<table>`\n    cellSpacing: null,\n    // `<table>`\n    char: null,\n    // Several table elements. When `align=char`, sets the character to align on\n    charOff: null,\n    // Several table elements. When `char`, offsets the alignment\n    classId: null,\n    // `<object>`\n    clear: null,\n    // `<br>`. Use CSS `clear` instead\n    code: null,\n    // `<object>`\n    codeBase: null,\n    // `<object>`\n    codeType: null,\n    // `<object>`\n    color: null,\n    // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean,\n    // Lists. Use CSS to reduce space between items instead\n    declare: boolean,\n    // `<object>`\n    event: null,\n    // `<script>`\n    face: null,\n    // `<font>`. Use CSS instead\n    frame: null,\n    // `<table>`\n    frameBorder: null,\n    // `<iframe>`. Use CSS `border` instead\n    hSpace: number,\n    // `<img>` and `<object>`\n    leftMargin: number,\n    // `<body>`\n    link: null,\n    // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null,\n    // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null,\n    // `<img>`. Use a `<picture>`\n    marginHeight: number,\n    // `<body>`\n    marginWidth: number,\n    // `<body>`\n    noResize: boolean,\n    // `<frame>`\n    noHref: boolean,\n    // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean,\n    // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean,\n    // `<td>` and `<th>`\n    object: null,\n    // `<applet>`\n    profile: null,\n    // `<head>`\n    prompt: null,\n    // `<isindex>`\n    rev: null,\n    // `<link>`\n    rightMargin: number,\n    // `<body>`\n    rules: null,\n    // `<table>`\n    scheme: null,\n    // `<meta>`\n    scrolling: booleanish,\n    // `<frame>`. Use overflow in the child context\n    standby: null,\n    // `<object>`\n    summary: null,\n    // `<table>`\n    text: null,\n    // `<body>`. Use CSS `color` instead\n    topMargin: number,\n    // `<body>`\n    valueType: null,\n    // `<param>`\n    version: null,\n    // `<html>`. Use a doctype.\n    vAlign: null,\n    // Several. Use CSS `vertical-align` instead\n    vLink: null,\n    // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number,\n    // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  },\n  space: 'html',\n  transform: caseInsensitiveTransform\n});", "map": {"version": 3, "names": ["caseInsensitiveTransform", "create", "booleanish", "boolean", "commaSeparated", "number", "overloadedBoolean", "spaceSeparated", "html", "attributes", "acceptcharse<PERSON>", "classname", "htmlfor", "httpequiv", "mustUseProperty", "properties", "abbr", "accept", "acceptCharset", "accessKey", "action", "allow", "allowFullScreen", "allowPaymentRequest", "allowUserMedia", "alt", "as", "async", "autoCapitalize", "autoComplete", "autoFocus", "autoPlay", "blocking", "capture", "charSet", "checked", "cite", "className", "cols", "colSpan", "content", "contentEditable", "controls", "controlsList", "coords", "crossOrigin", "data", "dateTime", "decoding", "default", "defer", "dir", "<PERSON><PERSON><PERSON>", "disabled", "download", "draggable", "encType", "enterKeyHint", "fetchPriority", "form", "formAction", "formEncType", "formMethod", "formNoValidate", "formTarget", "headers", "height", "hidden", "high", "href", "hrefLang", "htmlFor", "httpEquiv", "id", "imageSizes", "imageSrcSet", "inert", "inputMode", "integrity", "is", "isMap", "itemId", "itemProp", "itemRef", "itemScope", "itemType", "kind", "label", "lang", "language", "list", "loading", "loop", "low", "manifest", "max", "max<PERSON><PERSON><PERSON>", "media", "method", "min", "<PERSON><PERSON><PERSON><PERSON>", "multiple", "muted", "name", "nonce", "noModule", "noValidate", "onAbort", "onAfterPrint", "onAuxClick", "onBeforeMatch", "onBeforePrint", "onBeforeToggle", "onBeforeUnload", "onBlur", "onCancel", "onCanPlay", "onCanPlayThrough", "onChange", "onClick", "onClose", "onContextLost", "onContextMenu", "onContextRestored", "onCopy", "onCueChange", "onCut", "onDblClick", "onDrag", "onDragEnd", "onDragEnter", "onDragExit", "onDragLeave", "onDragOver", "onDragStart", "onDrop", "onDurationChange", "onEmptied", "onEnded", "onError", "onFocus", "onFormData", "onHashChange", "onInput", "onInvalid", "onKeyDown", "onKeyPress", "onKeyUp", "onLanguageChange", "onLoad", "onLoadedData", "onLoadedMetadata", "onLoadEnd", "onLoadStart", "onMessage", "onMessageError", "onMouseDown", "onMouseEnter", "onMouseLeave", "onMouseMove", "onMouseOut", "onMouseOver", "onMouseUp", "onOffline", "onOnline", "onPageHide", "onPageShow", "onPaste", "onPause", "onPlay", "onPlaying", "onPopState", "onProgress", "onRateChange", "onRejectionHandled", "onReset", "onResize", "onScroll", "onScrollEnd", "onSecurityPolicyViolation", "onSeeked", "onSeeking", "onSelect", "onSlotChange", "onStalled", "onStorage", "onSubmit", "onSuspend", "onTimeUpdate", "onToggle", "onUnhandledRejection", "onUnload", "onVolumeChange", "onWaiting", "onWheel", "open", "optimum", "pattern", "ping", "placeholder", "playsInline", "popover", "popoverTarget", "popoverTargetAction", "poster", "preload", "readOnly", "referrerPolicy", "rel", "required", "reversed", "rows", "rowSpan", "sandbox", "scope", "scoped", "seamless", "selected", "shadowRootClonable", "shadowRootDelegatesFocus", "shadowRootMode", "shape", "size", "sizes", "slot", "span", "spell<PERSON>heck", "src", "srcDoc", "srcLang", "srcSet", "start", "step", "style", "tabIndex", "target", "title", "translate", "type", "typeMustMatch", "useMap", "value", "width", "wrap", "writingSuggestions", "align", "aLink", "archive", "axis", "background", "bgColor", "border", "borderColor", "bottom<PERSON>argin", "cellPadding", "cellSpacing", "char", "char<PERSON>ff", "classId", "clear", "code", "codeBase", "codeType", "color", "compact", "declare", "event", "face", "frame", "frameBorder", "hSpace", "leftMargin", "link", "longDesc", "lowSrc", "marginHeight", "marginWid<PERSON>", "noResize", "noHref", "noShade", "noWrap", "object", "profile", "prompt", "rev", "<PERSON><PERSON><PERSON><PERSON>", "rules", "scheme", "scrolling", "standby", "summary", "text", "<PERSON><PERSON><PERSON><PERSON>", "valueType", "version", "vAlign", "vLink", "vSpace", "allowTransparency", "autoCorrect", "autoSave", "disablePictureInPicture", "disableRemotePlayback", "prefix", "property", "results", "security", "unselectable", "space", "transform"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/property-information/lib/html.js"], "sourcesContent": ["import {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\nimport {create} from './util/create.js'\nimport {\n  booleanish,\n  boolean,\n  commaSeparated,\n  number,\n  overloadedBoolean,\n  spaceSeparated\n} from './util/types.js'\n\nexport const html = create({\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    blocking: spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: overloadedBoolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shadowRootClonable: boolean,\n    shadowRootDelegatesFocus: boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  },\n  space: 'html',\n  transform: caseInsensitiveTransform\n})\n"], "mappings": "AAAA,SAAQA,wBAAwB,QAAO,sCAAsC;AAC7E,SAAQC,MAAM,QAAO,kBAAkB;AACvC,SACEC,UAAU,EACVC,OAAO,EACPC,cAAc,EACdC,MAAM,EACNC,iBAAiB,EACjBC,cAAc,QACT,iBAAiB;AAExB,OAAO,MAAMC,IAAI,GAAGP,MAAM,CAAC;EACzBQ,UAAU,EAAE;IACVC,aAAa,EAAE,gBAAgB;IAC/BC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE;EACb,CAAC;EACDC,eAAe,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC;EAC7DC,UAAU,EAAE;IACV;IACAC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAEb,cAAc;IACtBc,aAAa,EAAEX,cAAc;IAC7BY,SAAS,EAAEZ,cAAc;IACzBa,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,IAAI;IACXC,eAAe,EAAEnB,OAAO;IACxBoB,mBAAmB,EAAEpB,OAAO;IAC5BqB,cAAc,EAAErB,OAAO;IACvBsB,GAAG,EAAE,IAAI;IACTC,EAAE,EAAE,IAAI;IACRC,KAAK,EAAExB,OAAO;IACdyB,cAAc,EAAE,IAAI;IACpBC,YAAY,EAAEtB,cAAc;IAC5BuB,SAAS,EAAE3B,OAAO;IAClB4B,QAAQ,EAAE5B,OAAO;IACjB6B,QAAQ,EAAEzB,cAAc;IACxB0B,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAEhC,OAAO;IAChBiC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE9B,cAAc;IACzB+B,IAAI,EAAEjC,MAAM;IACZkC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,IAAI;IACbC,eAAe,EAAEvC,UAAU;IAC3BwC,QAAQ,EAAEvC,OAAO;IACjBwC,YAAY,EAAEpC,cAAc;IAC5BqC,MAAM,EAAEvC,MAAM,GAAGD,cAAc;IAC/ByC,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE9C,OAAO;IAChB+C,KAAK,EAAE/C,OAAO;IACdgD,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAElD,OAAO;IACjBmD,QAAQ,EAAEhD,iBAAiB;IAC3BiD,SAAS,EAAErD,UAAU;IACrBsD,OAAO,EAAE,IAAI;IACbC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,IAAI;IACnBC,IAAI,EAAE,IAAI;IACVC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE5D,OAAO;IACvB6D,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE1D,cAAc;IACvB2D,MAAM,EAAE7D,MAAM;IACd8D,MAAM,EAAE7D,iBAAiB;IACzB8D,IAAI,EAAE/D,MAAM;IACZgE,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAEhE,cAAc;IACvBiE,SAAS,EAAEjE,cAAc;IACzBkE,EAAE,EAAE,IAAI;IACRC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,KAAK,EAAEzE,OAAO;IACd0E,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE7E,OAAO;IACd8E,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE3E,cAAc;IACxB4E,OAAO,EAAE5E,cAAc;IACvB6E,SAAS,EAAEjF,OAAO;IAClBkF,QAAQ,EAAE9E,cAAc;IACxB+E,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAEzF,OAAO;IACb0F,GAAG,EAAExF,MAAM;IACXyF,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE3F,MAAM;IACjB4F,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE/F,MAAM;IACjBgG,QAAQ,EAAElG,OAAO;IACjBmG,KAAK,EAAEnG,OAAO;IACdoG,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAEtG,OAAO;IACjBuG,UAAU,EAAEvG,OAAO;IACnBwG,OAAO,EAAE,IAAI;IACbC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE,IAAI;IACpBC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,iBAAiB,EAAE,IAAI;IACvBC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,IAAI;IACjBC,KAAK,EAAE,IAAI;IACXC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,IAAI;IACZC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,IAAI;IACbC,gBAAgB,EAAE,IAAI;IACtBC,MAAM,EAAE,IAAI;IACZC,YAAY,EAAE,IAAI;IAClBC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE,IAAI;IACjBC,yBAAyB,EAAE,IAAI;IAC/BC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,IAAI;IACdC,oBAAoB,EAAE,IAAI;IAC1BC,QAAQ,EAAE,IAAI;IACdC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAEhM,OAAO;IACbiM,OAAO,EAAE/L,MAAM;IACfgM,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE/L,cAAc;IACpBgM,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAErM,OAAO;IACpBsM,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,IAAI;IACzBC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE3M,OAAO;IACjB4M,cAAc,EAAE,IAAI;IACpBC,GAAG,EAAEzM,cAAc;IACnB0M,QAAQ,EAAE9M,OAAO;IACjB+M,QAAQ,EAAE/M,OAAO;IACjBgN,IAAI,EAAE9M,MAAM;IACZ+M,OAAO,EAAE/M,MAAM;IACfgN,OAAO,EAAE9M,cAAc;IACvB+M,KAAK,EAAE,IAAI;IACXC,MAAM,EAAEpN,OAAO;IACfqN,QAAQ,EAAErN,OAAO;IACjBsN,QAAQ,EAAEtN,OAAO;IACjBuN,kBAAkB,EAAEvN,OAAO;IAC3BwN,wBAAwB,EAAExN,OAAO;IACjCyN,cAAc,EAAE,IAAI;IACpBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAEzN,MAAM;IACZ0N,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE5N,MAAM;IACZ6N,UAAU,EAAEhO,UAAU;IACtBiO,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAElO,MAAM;IACbmO,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAErO,MAAM;IAChBsO,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,IAAI,EAAE,IAAI;IACVC,aAAa,EAAE5O,OAAO;IACtB6O,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE/O,UAAU;IACjBgP,KAAK,EAAE7O,MAAM;IACb8O,IAAI,EAAE,IAAI;IACVC,kBAAkB,EAAE,IAAI;IAExB;IACA;IACAC,KAAK,EAAE,IAAI;IAAE;IACbC,KAAK,EAAE,IAAI;IAAE;IACbC,OAAO,EAAEhP,cAAc;IAAE;IACzBiP,IAAI,EAAE,IAAI;IAAE;IACZC,UAAU,EAAE,IAAI;IAAE;IAClBC,OAAO,EAAE,IAAI;IAAE;IACfC,MAAM,EAAEtP,MAAM;IAAE;IAChBuP,WAAW,EAAE,IAAI;IAAE;IACnBC,YAAY,EAAExP,MAAM;IAAE;IACtByP,WAAW,EAAE,IAAI;IAAE;IACnBC,WAAW,EAAE,IAAI;IAAE;IACnBC,IAAI,EAAE,IAAI;IAAE;IACZC,OAAO,EAAE,IAAI;IAAE;IACfC,OAAO,EAAE,IAAI;IAAE;IACfC,KAAK,EAAE,IAAI;IAAE;IACbC,IAAI,EAAE,IAAI;IAAE;IACZC,QAAQ,EAAE,IAAI;IAAE;IAChBC,QAAQ,EAAE,IAAI;IAAE;IAChBC,KAAK,EAAE,IAAI;IAAE;IACbC,OAAO,EAAErQ,OAAO;IAAE;IAClBsQ,OAAO,EAAEtQ,OAAO;IAAE;IAClBuQ,KAAK,EAAE,IAAI;IAAE;IACbC,IAAI,EAAE,IAAI;IAAE;IACZC,KAAK,EAAE,IAAI;IAAE;IACbC,WAAW,EAAE,IAAI;IAAE;IACnBC,MAAM,EAAEzQ,MAAM;IAAE;IAChB0Q,UAAU,EAAE1Q,MAAM;IAAE;IACpB2Q,IAAI,EAAE,IAAI;IAAE;IACZC,QAAQ,EAAE,IAAI;IAAE;IAChBC,MAAM,EAAE,IAAI;IAAE;IACdC,YAAY,EAAE9Q,MAAM;IAAE;IACtB+Q,WAAW,EAAE/Q,MAAM;IAAE;IACrBgR,QAAQ,EAAElR,OAAO;IAAE;IACnBmR,MAAM,EAAEnR,OAAO;IAAE;IACjBoR,OAAO,EAAEpR,OAAO;IAAE;IAClBqR,MAAM,EAAErR,OAAO;IAAE;IACjBsR,MAAM,EAAE,IAAI;IAAE;IACdC,OAAO,EAAE,IAAI;IAAE;IACfC,MAAM,EAAE,IAAI;IAAE;IACdC,GAAG,EAAE,IAAI;IAAE;IACXC,WAAW,EAAExR,MAAM;IAAE;IACrByR,KAAK,EAAE,IAAI;IAAE;IACbC,MAAM,EAAE,IAAI;IAAE;IACdC,SAAS,EAAE9R,UAAU;IAAE;IACvB+R,OAAO,EAAE,IAAI;IAAE;IACfC,OAAO,EAAE,IAAI;IAAE;IACfC,IAAI,EAAE,IAAI;IAAE;IACZC,SAAS,EAAE/R,MAAM;IAAE;IACnBgS,SAAS,EAAE,IAAI;IAAE;IACjBC,OAAO,EAAE,IAAI;IAAE;IACfC,MAAM,EAAE,IAAI;IAAE;IACdC,KAAK,EAAE,IAAI;IAAE;IACbC,MAAM,EAAEpS,MAAM;IAAE;;IAEhB;IACAqS,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,IAAI;IACdC,uBAAuB,EAAE1S,OAAO;IAChC2S,qBAAqB,EAAE3S,OAAO;IAC9B4S,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE5S,MAAM;IACf6S,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE;EAChB,CAAC;EACDC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAErT;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}