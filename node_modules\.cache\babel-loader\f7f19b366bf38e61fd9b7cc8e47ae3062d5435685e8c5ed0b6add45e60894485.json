{"ast": null, "code": "// Somewhat based on:\n// <https://github.com/defunctzombie/node-process/blob/master/browser.js>.\n// But I don’t think one tiny line of code can be copyrighted. 😅\nexport const minproc = {\n  cwd\n};\nfunction cwd() {\n  return '/';\n}", "map": {"version": 3, "names": ["minproc", "cwd"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/node_modules/vfile/lib/minproc.browser.js"], "sourcesContent": ["// Somewhat based on:\n// <https://github.com/defunctzombie/node-process/blob/master/browser.js>.\n// But I don’t think one tiny line of code can be copyrighted. 😅\nexport const minproc = {cwd}\n\nfunction cwd() {\n  return '/'\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,OAAO,GAAG;EAACC;AAAG,CAAC;AAE5B,SAASA,GAAGA,CAAA,EAAG;EACb,OAAO,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}